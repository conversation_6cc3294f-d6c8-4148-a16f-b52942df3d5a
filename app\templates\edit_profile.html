<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人信息管理 - 教材管理系统</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script defer src="/static/js/alpine.min.js"></script>
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮渐变样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }
        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        
        /* 按钮禁用状态样式 */
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }
        
        /* 确保禁用状态的按钮悬停时不改变样式 */
        button:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }

        /* 状态切换时强制隐藏下拉菜单 */
        .custom-select.force-hidden {
            display: none !important;
        }

        .custom-select-trigger {
            width: 100%;
            height: 48px;
            padding: 12px 40px 12px 40px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: border-color 0.2s ease;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            max-height: 300px;
            overflow: hidden;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
        }

        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            font-style: italic;
        }

        /* 滚动条样式 */
        .custom-select-options::-webkit-scrollbar {
            width: 4px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }

        /* 编辑状态过渡动画 */
        .edit-transition {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .edit-transition input {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }



        /* 表单字段过渡效果 */
        .form-field-transition {
            transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 消息通知组件 -->
    <div id="messageContainer" class="fixed top-6 right-6 z-50 space-y-3">
        <!-- 消息将通过JavaScript动态添加 -->
    </div>
    
    <div class="container mx-auto px-6 py-8">
        <div class="max-w-4xl mx-auto space-y-6">
            <!-- 个人信息卡片 -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden" x-data="profileManager()">
                <div class="p-6 border-b border-slate-100">
                    <h2 class="text-xl font-bold text-slate-800 flex items-center">
                        <i class="fas fa-user-circle text-blue-500 mr-3"></i>
                        基本信息
                    </h2>
                    <p class="text-sm text-slate-600 mt-1">更新您的个人基本信息</p>
                </div>
                <div class="p-6">
                    <!-- 加载状态 -->
                    <div x-show="loading" class="flex items-center justify-center py-8">
                        <div class="flex items-center space-x-3">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                            <span class="text-slate-600">正在加载用户信息...</span>
                        </div>
                    </div>

                    <form id="profileForm" class="space-y-6" x-show="!loading">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 用户名 (只读) -->
                            <div>
                                <label for="username" class="block text-sm font-medium text-slate-700 mb-2">用户名</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-user text-slate-400"></i>
                                    </div>
                                    <input type="text" id="username" name="username"
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700"
                                           x-bind:value="formData.username || ''" readonly>
                                </div>
                            </div>

                            <!-- 角色 (只读) -->
                            <div>
                                <label for="role" class="block text-sm font-medium text-slate-700 mb-2">用户角色</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-user-tag text-slate-400"></i>
                                    </div>
                                    <input type="text" id="role" name="role"
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700"
                                           x-bind:value="getRoleDisplayName(userRole)" readonly>
                                </div>
                            </div>

                            <!-- 所属单位 -->
                            <div>
                                <label for="organization" class="block text-sm font-medium text-slate-700 mb-2">所属单位<span class="text-red-500">*</span></label>
                                <!-- 展示状态 -->
                                <div x-show="!isEditing" class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-building text-slate-400"></i>
                                    </div>
                                    <input type="text" id="organization_display"
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700"
                                           x-bind:value="organizationName || '未设置'" readonly>
                                </div>
                                <!-- 编辑状态 - 仅教师用户可见 -->
                                <div x-show="isEditing && userRole === 'teacher'"
                                     x-transition:enter="transition ease-out duration-300"
                                     x-transition:enter-start="opacity-0 transform scale-95"
                                     x-transition:enter-end="opacity-100 transform scale-100"
                                     x-transition:leave="transition ease-in duration-200"
                                     x-transition:leave-start="opacity-100 transform scale-100"
                                     x-transition:leave-end="opacity-0 transform scale-95">
                                    <div class="custom-select" id="schoolSelectContainer">
                                        <div class="custom-select-trigger">
                                            <span class="custom-select-text">请选择学校</span>
                                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                                        </div>
                                        <div class="custom-select-dropdown">
                                            <div class="custom-select-search">
                                                <input type="text" placeholder="搜索学校..." />
                                            </div>
                                            <div class="custom-select-options" id="schoolSelectOptions">
                                                <!-- 选项将动态生成 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 编辑状态 - 非教师用户显示只读 -->
                                <div x-show="isEditing && userRole !== 'teacher'"
                                     x-transition:enter="transition ease-out duration-300"
                                     x-transition:enter-start="opacity-0 transform scale-95"
                                     x-transition:enter-end="opacity-100 transform scale-100"
                                     x-transition:leave="transition ease-in duration-200"
                                     x-transition:leave-start="opacity-100 transform scale-100"
                                     x-transition:leave-end="opacity-0 transform scale-95"
                                     class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-building text-slate-400"></i>
                                    </div>
                                    <input type="text"
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none form-field-transition text-slate-700"
                                           x-bind:value="organizationName || '未设置'" readonly>
                                </div>
                            </div>

                            <!-- 姓名 -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-slate-700 mb-2">
                                    姓名 <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-id-card text-slate-400"></i>
                                    </div>
                                    <input type="text" id="name" name="name"
                                           x-bind:class="isEditing ? 'w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent form-field-transition text-slate-700' : 'w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none form-field-transition text-slate-700'"
                                           x-bind:readonly="!isEditing"
                                           x-model="formData.name"
                                           placeholder="请输入姓名" required>
                                </div>
                            </div>
                        </div>

                        <!-- 手机号码 -->
                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-slate-700 mb-2">
                                手机号码 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-mobile-alt text-slate-400"></i>
                                </div>
                                <input type="tel" id="phone_number" name="phone_number"
                                       x-bind:class="isEditing ? 'w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent form-field-transition text-slate-700' : 'w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none form-field-transition text-slate-700'"
                                       x-bind:readonly="!isEditing"
                                       x-model="formData.phone_number"
                                       placeholder="请输入手机号码" required>
                            </div>
                        </div>

                        <!-- 邮箱 -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-slate-700 mb-2">
                                邮箱 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-envelope text-slate-400"></i>
                                </div>
                                <input type="email" id="email" name="email"
                                       x-bind:class="isEditing ? 'w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent form-field-transition text-slate-700' : 'w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none form-field-transition text-slate-700'"
                                       x-bind:readonly="!isEditing"
                                       x-model="formData.email"
                                       placeholder="请输入邮箱地址" required>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="pt-4 flex justify-end space-x-4 min-h-[48px]">
                            <!-- 编辑按钮 (默认显示) -->
                            <div x-show="!isEditing" class="flex space-x-4">
                                <button type="button" x-on:click="startEdit()"
                                        class="btn-primary text-white font-medium h-12 px-6 rounded-xl flex items-center space-x-2 shadow-lg">
                                    <i class="fas fa-edit"></i>
                                    <span>编辑信息</span>
                                </button>
                            </div>

                            <!-- 保存和取消按钮 (编辑时显示) -->
                            <div x-show="isEditing" class="flex space-x-4">
                                <button type="button" x-on:click="cancelEdit()"
                                        class="px-6 py-3 bg-slate-500 text-white rounded-xl hover:bg-slate-600 font-medium h-12 flex items-center space-x-2 transition-colors duration-200">
                                    <i class="fas fa-times"></i>
                                    <span>取消</span>
                                </button>

                                <button type="button" x-on:click="saveProfile()"
                                        class="btn-primary text-white font-medium h-12 px-6 rounded-xl flex items-center space-x-2 shadow-lg">
                                    <i class="fas fa-save"></i>
                                    <span>保存个人信息</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 角色信息卡片 (仅教师用户显示) -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden" x-data="teacherInfoManager()" x-show="showCard">
                <div class="p-6 border-b border-slate-100">
                    <h2 class="text-xl font-bold text-slate-800 flex items-center">
                        <i class="fas fa-graduation-cap text-green-500 mr-3"></i>
                        角色信息
                    </h2>
                    <p class="text-sm text-slate-600 mt-1">管理您的教师角色相关信息</p>
                </div>
                <div class="p-6">
                    <!-- 加载状态 -->
                    <div x-show="loading" class="flex items-center justify-center py-8">
                        <div class="flex items-center space-x-3">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></div>
                            <span class="text-slate-600">正在加载角色信息...</span>
                        </div>
                    </div>

                    <form class="space-y-6" x-show="!loading">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 院系 -->
                            <div>
                                <label for="department" class="block text-sm font-medium text-slate-700 mb-2">院系</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-university text-slate-400"></i>
                                    </div>
                                    <input type="text" id="department" name="department"
                                           x-bind:class="isEditing ? 'w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent form-field-transition text-slate-700' : 'w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none form-field-transition text-slate-700'"
                                           x-bind:readonly="!isEditing"
                                           x-model="formData.department"
                                           placeholder="请输入院系">
                                </div>
                            </div>

                            <!-- 职务 -->
                            <div>
                                <label for="position" class="block text-sm font-medium text-slate-700 mb-2">职务</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-briefcase text-slate-400"></i>
                                    </div>
                                    <input type="text" id="position" name="position"
                                           x-bind:class="isEditing ? 'w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent form-field-transition text-slate-700' : 'w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none form-field-transition text-slate-700'"
                                           x-bind:readonly="!isEditing"
                                           x-model="formData.position"
                                           placeholder="请输入职务">
                                </div>
                            </div>

                            <!-- 职称 -->
                            <div>
                                <label for="title" class="block text-sm font-medium text-slate-700 mb-2">职称</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-award text-slate-400"></i>
                                    </div>
                                    <input type="text" id="title" name="title"
                                           x-bind:class="isEditing ? 'w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent form-field-transition text-slate-700' : 'w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none form-field-transition text-slate-700'"
                                           x-bind:readonly="!isEditing"
                                           x-model="formData.title"
                                           placeholder="请输入职称">
                                </div>
                            </div>

                            <!-- 性别 -->
                            <div>
                                <label for="gender" class="block text-sm font-medium text-slate-700 mb-2">性别</label>
                                <!-- 展示状态 -->
                                <div x-show="!isEditing" class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-venus-mars text-slate-400"></i>
                                    </div>
                                    <input type="text"
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none form-field-transition text-slate-700"
                                           x-bind:value="formData.gender || '未设置'" readonly>
                                </div>
                                <!-- 编辑状态 -->
                                <div x-show="isEditing"
                                     x-transition:enter="transition ease-out duration-300"
                                     x-transition:enter-start="opacity-0 transform scale-95"
                                     x-transition:enter-end="opacity-100 transform scale-100"
                                     x-transition:leave="transition ease-in duration-200"
                                     x-transition:leave-start="opacity-100 transform scale-100"
                                     x-transition:leave-end="opacity-0 transform scale-95">
                                    <div class="custom-select" id="genderSelectContainer">
                                        <div class="custom-select-trigger">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-venus-mars text-slate-400"></i>
                                            </div>
                                            <span class="custom-select-text pl-7">请选择性别</span>
                                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                                        </div>
                                        <div class="custom-select-dropdown">
                                            <div class="custom-select-options" id="genderSelectOptions">
                                                <!-- 选项将动态生成 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="pt-4 flex justify-end space-x-4 min-h-[48px]">
                            <!-- 编辑按钮 (默认显示) -->
                            <div x-show="!isEditing" class="flex space-x-4">
                                <button type="button" x-on:click="startEdit()"
                                        class="bg-green-500 hover:bg-green-600 text-white font-medium h-12 px-6 rounded-xl flex items-center space-x-2 shadow-lg transition-colors duration-200">
                                    <i class="fas fa-edit"></i>
                                    <span>编辑角色信息</span>
                                </button>
                            </div>

                            <!-- 保存和取消按钮 (编辑时显示) -->
                            <div x-show="isEditing" class="flex space-x-4">
                                <button type="button" x-on:click="cancelEdit()"
                                        class="px-6 py-3 bg-slate-500 text-white rounded-xl hover:bg-slate-600 font-medium h-12 flex items-center space-x-2 transition-colors duration-200">
                                    <i class="fas fa-times"></i>
                                    <span>取消</span>
                                </button>

                                <button type="button" x-on:click="saveTeacherInfo()"
                                        class="bg-green-500 hover:bg-green-600 text-white font-medium h-12 px-6 rounded-xl flex items-center space-x-2 shadow-lg transition-colors duration-200">
                                    <i class="fas fa-save"></i>
                                    <span>保存角色信息</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 用户名修改卡片 -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden" x-data="usernameManager()">
                <div class="p-6 border-b border-slate-100">
                    <h2 class="text-xl font-bold text-slate-800 flex items-center">
                        <i class="fas fa-user-edit text-green-500 mr-3"></i>
                        修改用户名
                    </h2>
                    <p class="text-sm text-slate-600 mt-1">用户名修改后半年内不可再次修改</p>
                </div>
                <div class="p-6">
                    <!-- 当前用户名显示 -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-slate-700 mb-2">
                            当前用户名
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-user text-slate-400"></i>
                            </div>
                            <input type="text"
                                   class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700"
                                   value="{{ user.username if user.username else '' }}" readonly>
                        </div>
                        <!-- 权限状态提示 -->
                        <p x-show="permissionChecked && canChange" class="text-sm text-slate-700 mt-2">
                            <i class="fas fa-check-circle text-green-500 mr-1"></i>
                            您可以修改用户名，修改后半年内不可再次修改
                        </p>
                        <p x-show="permissionChecked && !canChange" class="text-sm text-slate-400 mt-2">
                            <i class="fas fa-clock text-slate-400 mr-1"></i>
                            <span x-show="nextChangeTime">下次可修改时间：<span x-text="nextChangeTime"></span></span>
                            <span x-show="!nextChangeTime">暂时无法修改用户名</span>
                        </p>

                    </div>

                    <!-- 最近一次修改时间 -->
                    <div class="mb-6" x-show="history">
                        <label class="block text-sm font-medium text-slate-700 mb-2">
                            最近一次修改时间
                        </label>
                        <div class="bg-slate-50 border border-slate-200 rounded-lg p-4">
                            <div class="text-sm text-slate-600">
                                <p><span x-text="history?.change_time"></span></p>
                            </div>
                        </div>
                    </div>

                    <!-- 新用户名输入区域 (默认隐藏) -->
                    <div class="mb-6" x-show="showEditForm" x-transition>
                        <label for="new_username" class="block text-sm font-medium text-slate-700 mb-2">
                            新用户名 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-user-edit text-slate-400"></i>
                            </div>
                            <input type="text" id="new_username" name="new_username"
                                   class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all text-slate-700"
                                   placeholder="请输入新用户名" maxlength="50">
                        </div>
                        <p class="text-xs text-slate-500 mt-2">用户名长度3-50个字符，只能包含字母、数字、下划线和中文字符</p>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex justify-end space-x-4">
                        <!-- 修改按钮 (默认显示) -->
                        <button type="button" x-show="!showEditForm" x-on:click="startUsernameEdit()"
                                x-bind:disabled="!canChange"
                                x-bind:class="canChange ? 'bg-green-500 hover:bg-green-600 text-white' : 'bg-slate-300 text-slate-500 cursor-not-allowed'"
                                class="px-6 py-3 rounded-xl transition-all font-medium">
                            <i class="fas fa-edit mr-2"></i>
                            <span>修改用户名</span>
                        </button>

                        <!-- 保存和取消按钮 (编辑时显示) -->
                        <template x-if="showEditForm">
                            <div class="flex space-x-4">
                                <button type="button" x-on:click="cancelUsernameEdit()"
                                        class="px-6 py-3 bg-slate-500 text-white rounded-xl hover:bg-slate-600 transition-all font-medium">
                                    <i class="fas fa-times mr-2"></i>
                                    <span>取消</span>
                                </button>

                                <button type="button" x-on:click="saveUsername()"
                                        class="px-6 py-3 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-all font-medium">
                                    <i class="fas fa-save mr-2"></i>
                                    <span>保存</span>
                                </button>
                            </div>
                        </template>
                    </div>
                </div>
            </div>

            <!-- 密码修改卡片 -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden" x-data="passwordManager()">
                <div class="p-6 border-b border-slate-100">
                    <h2 class="text-xl font-bold text-slate-800 flex items-center">
                        <i class="fas fa-key text-purple-500 mr-3"></i> 
                        修改密码
                    </h2>
                    <p class="text-sm text-slate-600 mt-1">确保您的账户安全，定期更换密码</p>
                </div>
                <div class="p-6">
                    <!-- 方式选择标签 -->
                    <div class="flex bg-slate-100 rounded-lg p-1 mb-6">
                        <button type="button" @click="switchMode('password')"
                                :class="['flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all', 
                                         currentMode === 'password' ? 'bg-white text-purple-600 shadow-sm' : 'text-slate-600 hover:text-slate-800']">
                            <i class="fas fa-lock mr-2"></i>旧密码验证
                        </button>
                        <button type="button" @click="switchMode('email')"
                                :class="['flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all', 
                                         currentMode === 'email' ? 'bg-white text-purple-600 shadow-sm' : 'text-slate-600 hover:text-slate-800']">
                            <i class="fas fa-envelope mr-2"></i>邮箱验证码
                        </button>
                    </div>

                    <!-- 旧密码验证方式 -->
                    <div x-show="currentMode === 'password'" x-transition class="space-y-6">
                        <form id="passwordForm" class="space-y-6">
                            <!-- 当前密码 -->
                            <div>
                                <label for="current_password" class="block text-sm font-medium text-slate-700 mb-2">
                                    当前密码 <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-lock text-slate-400"></i>
                                    </div>
                                    <input type="password" id="current_password" name="current_password" 
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" 
                                           placeholder="请输入当前密码" required>
                                </div>
                            </div>
                            
                            <!-- 新密码 -->
                            <div>
                                <label for="new_password" class="block text-sm font-medium text-slate-700 mb-2">
                                    新密码 <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-unlock text-slate-400"></i>
                                    </div>
                                    <input type="password" id="new_password" name="new_password" 
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" 
                                           placeholder="请输入新密码(至少6位)" required>
                                </div>
                                <p class="text-xs text-slate-500 mt-2">密码长度至少6个字符</p>
                            </div>
                            
                            <!-- 确认新密码 -->
                            <div>
                                <label for="confirm_password" class="block text-sm font-medium text-slate-700 mb-2">
                                    确认新密码 <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-check-circle text-slate-400"></i>
                                    </div>
                                    <input type="password" id="confirm_password" name="confirm_password" 
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" 
                                           placeholder="请再次输入新密码" required>
                                </div>
                            </div>
                            
                            <!-- 提交按钮 -->
                            <div class="pt-4">
                                <button type="button" id="changePasswordBtn" 
                                        class="btn-purple text-white font-medium h-12 px-6 rounded-xl flex items-center space-x-2 shadow-lg">
                                    <i class="fas fa-key"></i>
                                    <span>修改密码</span>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- 邮箱验证码方式 -->
                    <div x-show="currentMode === 'email'" x-transition class="space-y-6">
                        <form id="emailPasswordForm" class="space-y-6">
                            <!-- 邮箱地址显示 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">
                                    验证邮箱
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-envelope text-slate-400"></i>
                                    </div>
                                    <input type="email" 
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700" 
                                           value="{{ user.email if user.email else '' }}" readonly>
                                </div>
                                <p class="text-xs text-slate-500 mt-2">验证码将发送到此邮箱地址</p>
                            </div>

                            <!-- 验证码输入 -->
                            <div>
                                <label for="email_verification_code" class="block text-sm font-medium text-slate-700 mb-2">
                                    邮箱验证码 <span class="text-red-500">*</span>
                                </label>
                                <div class="flex space-x-3">
                                    <div class="relative flex-1">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-shield-alt text-slate-400"></i>
                                        </div>
                                        <input type="text" id="email_verification_code" name="email_verification_code" 
                                               class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" 
                                               placeholder="请输入6位验证码" maxlength="6" required>
                                    </div>
                                    <button type="button" id="sendEmailCodeBtn" 
                                            class="btn-success text-white font-medium h-12 px-4 rounded-xl flex items-center space-x-2 shadow-lg whitespace-nowrap"
                                            :disabled="emailCodeCooldown > 0">
                                        <i class="fas fa-paper-plane"></i>
                                        <span x-text="emailCodeCooldown > 0 ? `${emailCodeCooldown}s` : '发送验证码'"></span>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 新密码 -->
                            <div>
                                <label for="email_new_password" class="block text-sm font-medium text-slate-700 mb-2">
                                    新密码 <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-unlock text-slate-400"></i>
                                    </div>
                                    <input type="password" id="email_new_password" name="email_new_password" 
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" 
                                           placeholder="请输入新密码(至少6位)" required>
                                </div>
                                <p class="text-xs text-slate-500 mt-2">密码长度至少6个字符</p>
                            </div>
                            
                            <!-- 确认新密码 -->
                            <div>
                                <label for="email_confirm_password" class="block text-sm font-medium text-slate-700 mb-2">
                                    确认新密码 <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-check-circle text-slate-400"></i>
                                    </div>
                                    <input type="password" id="email_confirm_password" name="email_confirm_password" 
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all" 
                                           placeholder="请再次输入新密码" required>
                                </div>
                            </div>
                            
                            <!-- 提交按钮 -->
                            <div class="pt-4">
                                <button type="button" id="changePasswordByEmailBtn" 
                                        class="btn-purple text-white font-medium h-12 px-6 rounded-xl flex items-center space-x-2 shadow-lg">
                                    <i class="fas fa-key"></i>
                                    <span>修改密码</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let messageId = 0;

        // Alpine.js 个人资料管理组件
        function profileManager() {
            return {
                isEditing: false,
                userRole: '',
                organizationName: '',
                selectedSchoolId: '',
                schoolSelect: null,
                formData: {
                    username: '',
                    name: '',
                    phone_number: '',
                    email: ''
                },
                originalData: {},
                loading: true,

                async init() {
                    // 从API获取用户信息
                    await this.loadUserInfo();
                },

                async loadUserInfo() {
                    try {
                        const response = await fetch('/api/common/get_user_info');
                        const data = await response.json();

                        if (data.status === 'success') {
                            const userInfo = data.user_info;
                            this.userRole = userInfo.role;
                            this.selectedSchoolId = userInfo.teacher_school_id || '';
                            this.formData = {
                                username: userInfo.username || '',
                                name: userInfo.name || '',
                                phone_number: userInfo.phone_number || '',
                                email: userInfo.email || ''
                            };

                            // 获取组织名称
                            if (userInfo.role === 'teacher' && userInfo.school_name) {
                                this.organizationName = userInfo.school_name;
                            } else if (userInfo.role === 'publisher' && userInfo.company_name) {
                                this.organizationName = userInfo.company_name;
                            } else if (userInfo.role === 'dealer' && userInfo.company_name) {
                                this.organizationName = userInfo.company_name;
                            }

                            // 保存原始数据
                            this.originalData = JSON.parse(JSON.stringify(this.formData));
                            this.originalData.organization_name = this.organizationName;
                            this.originalData.school_id = this.selectedSchoolId;

                            this.loading = false;
                        } else {
                            showMessage('获取用户信息失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                startEdit() {
                    // 先隐藏所有下拉菜单
                    this.hideAllDropdowns();

                    this.isEditing = true;

                    // 如果是教师用户，初始化学校选择器
                    if (this.userRole === 'teacher') {
                        this.$nextTick(() => {
                            this.showAllDropdowns();
                            this.initSchoolSelect();
                        });
                    } else {
                        this.$nextTick(() => {
                            this.showAllDropdowns();
                        });
                    }
                },

                cancelEdit() {
                    // 先隐藏所有下拉菜单
                    this.hideAllDropdowns();

                    // 销毁学校选择器
                    this.destroySchoolSelect();

                    this.isEditing = false;

                    // 恢复原始数据
                    this.formData = JSON.parse(JSON.stringify(this.originalData));
                    this.organizationName = this.originalData.organization_name;
                    this.selectedSchoolId = this.originalData.school_id;

                    // 延迟恢复下拉菜单显示
                    this.$nextTick(() => {
                        setTimeout(() => {
                            this.showAllDropdowns();
                        }, 100);
                    });
                },

                async saveProfile() {
                    // 表单验证
                    if (!this.formData.name.trim()) {
                        showMessage('姓名不能为空', 'error');
                        return;
                    }

                    if (!this.formData.phone_number.trim()) {
                        showMessage('手机号码不能为空', 'error');
                        return;
                    }

                    // 验证手机号格式
                    const phoneRegex = /^1[3-9]\d{9}$/;
                    if (!phoneRegex.test(this.formData.phone_number)) {
                        showMessage('请输入正确的手机号码', 'error');
                        return;
                    }

                    if (!this.formData.email.trim()) {
                        showMessage('邮箱不能为空', 'error');
                        return;
                    }

                    // 验证邮箱格式
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(this.formData.email)) {
                        showMessage('请输入正确的邮箱格式', 'error');
                        return;
                    }

                    try {
                        // 准备提交数据
                        const submitData = {
                            name: this.formData.name.trim(),
                            phone_number: this.formData.phone_number.trim(),
                            email: this.formData.email.trim()
                        };

                        // 如果是教师用户且选择了学校，添加学校ID
                        if (this.userRole === 'teacher' && this.schoolSelect) {
                            const schoolId = this.schoolSelect.getValue();
                            if (schoolId) {
                                submitData.school_id = schoolId;
                            }
                        }

                        const response = await fetch('/api/common/edit_profile', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(submitData)
                        });

                        const data = await response.json();

                        if (data.code === 0) {
                            showMessage('个人信息更新成功！', 'success');

                            // 更新原始数据
                            this.originalData = JSON.parse(JSON.stringify(this.formData));
                            if (this.userRole === 'teacher' && this.schoolSelect) {
                                const schoolId = this.schoolSelect.getValue();
                                const schoolText = this.schoolSelect.getText();
                                if (schoolId) {
                                    this.originalData.school_id = schoolId;
                                    this.organizationName = schoolText;
                                    this.originalData.organization_name = schoolText;
                                }
                            }

                            // 先隐藏所有下拉菜单
                            this.hideAllDropdowns();

                            // 退出编辑模式
                            this.isEditing = false;

                            // 销毁学校选择器
                            this.destroySchoolSelect();

                            // 延迟恢复下拉菜单显示
                            this.$nextTick(() => {
                                setTimeout(() => {
                                    this.showAllDropdowns();
                                }, 100);
                            });
                        } else {
                            showMessage(data.message || '个人信息更新失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                hideAllDropdowns() {
                    // 强制隐藏所有下拉菜单
                    $('.custom-select').addClass('force-hidden');
                },

                showAllDropdowns() {
                    // 恢复下拉菜单显示
                    $('.custom-select').removeClass('force-hidden');
                },

                getRoleDisplayName(role) {
                    const roleMap = {
                        'teacher': '教师',
                        'publisher': '出版社',
                        'dealer': '经销商',
                        'admin': '管理员'
                    };
                    return roleMap[role] || '未知';
                },

                destroySchoolSelect() {
                    if (this.schoolSelect) {
                        // 移除事件监听器和DOM元素状态
                        this.schoolSelect.destroy();
                        this.schoolSelect = null;
                    }
                },

                async initSchoolSelect() {
                    // 先销毁之前的实例
                    this.destroySchoolSelect();
                    try {
                        // 初始化学校选择器
                        this.schoolSelect = new CustomSelect('schoolSelectContainer', {
                            placeholder: '请选择学校',
                            onSelect: (value, text) => {
                                // 选择学校后的回调
                                console.log('选择了学校:', value, text);
                            }
                        });

                        // 加载学校数据
                        const response = await fetch('/api/common/get_schools');
                        const data = await response.json();

                        if (data.code === 0) {
                            const schoolOptions = data.data.map(school => ({
                                value: school.id,
                                text: school.name
                            }));

                            this.schoolSelect.setOptions(schoolOptions);

                            // 设置当前选中的学校
                            if (this.selectedSchoolId) {
                                this.schoolSelect.setValue(this.selectedSchoolId);
                            }
                        } else {
                            showMessage('加载学校列表失败', 'error');
                        }
                    } catch (error) {
                        showMessage('加载学校列表失败', 'error');
                    }
                }
            }
        }

        // Alpine.js 教师信息管理组件
        function teacherInfoManager() {
            return {
                isEditing: false,
                loading: true,
                showCard: false,
                userRole: '',
                genderSelect: null,
                formData: {
                    department: '',
                    position: '',
                    title: '',
                    gender: ''
                },
                originalData: {},

                async init() {
                    await this.checkUserRole();
                    if (this.userRole === 'teacher') {
                        await this.loadTeacherInfo();
                    } else {
                        this.loading = false;
                    }
                },

                async checkUserRole() {
                    try {
                        const response = await fetch('/api/common/get_user_info');
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.userRole = data.user_info.role;
                            this.showCard = (this.userRole === 'teacher');
                            console.log('用户角色:', this.userRole, '显示卡片:', this.showCard);
                        } else {
                            console.log('获取用户信息失败:', data.message);
                            this.showCard = false;
                        }
                    } catch (error) {
                        console.error('获取用户角色失败:', error);
                        this.showCard = false;
                    }
                },

                async loadTeacherInfo() {
                    try {
                        const response = await fetch('/api/common/get_teacher_info');
                        const data = await response.json();

                        if (data.status === 'success') {
                            const teacherInfo = data.teacher_info || {};
                            this.formData = {
                                department: teacherInfo.department || '',
                                position: teacherInfo.position || '',
                                title: teacherInfo.title || '',
                                gender: teacherInfo.gender || ''
                            };

                            // 保存原始数据
                            this.originalData = JSON.parse(JSON.stringify(this.formData));
                            this.loading = false;
                        } else {
                            // 如果没有教师信息记录，创建空记录
                            this.formData = {
                                department: '',
                                position: '',
                                title: '',
                                gender: ''
                            };
                            this.originalData = JSON.parse(JSON.stringify(this.formData));
                            this.loading = false;
                        }
                    } catch (error) {
                        showMessage('加载教师信息失败', 'error');
                        this.loading = false;
                    }
                },

                startEdit() {
                    // 先隐藏所有下拉菜单
                    this.hideAllDropdowns();

                    this.isEditing = true;

                    // 初始化性别选择器
                    this.$nextTick(() => {
                        this.showAllDropdowns();
                        this.initGenderSelect();
                    });
                },

                cancelEdit() {
                    // 先隐藏所有下拉菜单
                    this.hideAllDropdowns();

                    // 销毁性别选择器
                    this.destroyGenderSelect();

                    this.isEditing = false;
                    // 恢复原始数据
                    this.formData = JSON.parse(JSON.stringify(this.originalData));

                    // 延迟恢复下拉菜单显示
                    this.$nextTick(() => {
                        setTimeout(() => {
                            this.showAllDropdowns();
                        }, 100);
                    });
                },

                async saveTeacherInfo() {
                    try {
                        const response = await fetch('/api/common/save_teacher_info', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(this.formData)
                        });

                        const data = await response.json();

                        if (data.status === 'success') {
                            showMessage('教师信息保存成功！', 'success');

                            // 更新原始数据
                            this.originalData = JSON.parse(JSON.stringify(this.formData));

                            // 先隐藏所有下拉菜单
                            this.hideAllDropdowns();

                            // 退出编辑模式
                            this.isEditing = false;

                            // 销毁性别选择器
                            this.destroyGenderSelect();

                            // 延迟恢复下拉菜单显示
                            this.$nextTick(() => {
                                setTimeout(() => {
                                    this.showAllDropdowns();
                                }, 100);
                            });
                        } else {
                            showMessage(data.message || '教师信息保存失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                hideAllDropdowns() {
                    // 强制隐藏所有下拉菜单
                    $('.custom-select').addClass('force-hidden');
                },

                showAllDropdowns() {
                    // 恢复下拉菜单显示
                    $('.custom-select').removeClass('force-hidden');
                },

                destroyGenderSelect() {
                    if (this.genderSelect) {
                        this.genderSelect.destroy();
                        this.genderSelect = null;
                    }
                },

                initGenderSelect() {
                    // 先销毁之前的实例
                    this.destroyGenderSelect();

                    try {
                        // 初始化性别选择器
                        this.genderSelect = new CustomSelect('genderSelectContainer', {
                            placeholder: '请选择性别',
                            showSearch: false, // 不显示搜索框
                            onSelect: (value, text) => {
                                this.formData.gender = value;
                                console.log('选择了性别:', value, text);
                            }
                        });

                        // 设置性别选项
                        const genderOptions = [
                            { value: '男', text: '男' },
                            { value: '女', text: '女' }
                        ];

                        this.genderSelect.setOptions(genderOptions);

                        // 设置当前选中的性别
                        if (this.formData.gender) {
                            this.genderSelect.setValue(this.formData.gender);
                        }
                    } catch (error) {
                        console.error('初始化性别选择器失败:', error);
                    }
                }
            }
        }

        // Alpine.js 用户名管理组件
        function usernameManager() {
            return {
                canChange: false,
                permissionChecked: false,
                lastChangeTime: null,
                nextChangeTime: null,
                history: null,
                showEditForm: false,

                init() {
                    // 页面加载时自动检查权限
                    this.checkPermission();
                },

                startUsernameEdit() {
                    if (this.canChange) {
                        this.showEditForm = true;
                    }
                },

                cancelUsernameEdit() {
                    this.showEditForm = false;
                    // 清空输入框
                    const input = document.getElementById('new_username');
                    if (input) input.value = '';
                },

                async saveUsername() {
                    const newUsername = document.getElementById('new_username').value.trim();

                    // 表单验证
                    if (!newUsername) {
                        showMessage('请输入新用户名', 'error');
                        return;
                    }

                    if (newUsername.length < 3 || newUsername.length > 50) {
                        showMessage('用户名长度必须在3-50个字符之间', 'error');
                        return;
                    }

                    // 验证用户名格式
                    const usernameRegex = /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/;
                    if (!usernameRegex.test(newUsername)) {
                        showMessage('用户名只能包含字母、数字、下划线和中文字符', 'error');
                        return;
                    }

                    // 确认修改
                    if (!confirm('确定要修改用户名吗？修改后半年内不可再次修改。')) {
                        return;
                    }

                    try {
                        const response = await fetch('/api/common/change_username', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                new_username: newUsername
                            })
                        });

                        const data = await response.json();

                        if (data.status === 'success') {
                            showMessage('用户名修改成功！', 'success');
                            // 隐藏编辑表单
                            this.cancelUsernameEdit();
                            // 重新检查权限状态
                            setTimeout(() => {
                                this.checkPermission();
                                // 刷新页面以更新显示的当前用户名
                                location.reload();
                            }, 1000);
                        } else {
                            showMessage(data.message || '用户名修改失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                async checkPermission() {
                    try {
                        const response = await fetch('/api/common/check_username_change_permission');
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.canChange = data.can_change;
                            this.lastChangeTime = data.last_change_time;
                            this.nextChangeTime = data.next_change_time;
                            this.permissionChecked = true;

                            // 获取修改历史
                            await this.loadHistory();
                        } else {
                            showMessage(data.message || '检查权限失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                async loadHistory() {
                    try {
                        const response = await fetch('/api/common/get_username_change_history');
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.history = data.history;
                        }
                    } catch (error) {
                        // 静默处理历史记录获取失败
                    }
                }
            }
        }

        // Alpine.js 密码管理组件
        function passwordManager() {
            return {
                currentMode: 'password',
                emailCodeCooldown: 0,

                switchMode(mode) {
                    this.currentMode = mode;
                    // 切换时清空所有表单
                    this.clearAllForms();
                },
                
                clearAllForms() {
                    // 清空旧密码表单
                    document.getElementById('current_password').value = '';
                    document.getElementById('new_password').value = '';
                    document.getElementById('confirm_password').value = '';
                    
                    // 清空邮箱验证码表单
                    document.getElementById('email_verification_code').value = '';
                    document.getElementById('email_new_password').value = '';
                    document.getElementById('email_confirm_password').value = '';
                },
                
                startEmailCodeCooldown() {
                    this.emailCodeCooldown = 60;
                    // 立即更新状态，不等待第一个interval触发
                    const timer = setInterval(() => {
                        this.emailCodeCooldown--;
                        if (this.emailCodeCooldown <= 0) {
                            clearInterval(timer);
                        }
                    }, 1000);
                }
            }
        }
        
        // 消息通知函数 - 按照设计规范实现
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');
            
            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-xl shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' : 
                type === 'error' ? 'border-red-500' : 
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;
            
            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' : 
                        type === 'error' ? 'text-red-500' : 
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' : 
                            type === 'error' ? 'fa-exclamation-circle' : 
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})" 
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600 transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;
            
            container.appendChild(messageEl);
            
            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);
            
            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }
        
        // 移除消息
        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
        

        
        // 发送邮箱验证码
        document.getElementById('sendEmailCodeBtn').addEventListener('click', function() {
            const emailField = document.getElementById('email');
            const email = emailField ? emailField.value.trim() : '';
            
            if (!email) {
                showMessage('请先设置并保存邮箱地址', 'error');
                return;
            }
            
            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('邮箱格式不正确', 'error');
                return;
            }
            
            // 显示加载状态
            const sendBtn = document.getElementById('sendEmailCodeBtn');
            const originalText = sendBtn.innerHTML;
            sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i><span>发送中...</span>';
            sendBtn.disabled = true;
            
            // 调用后端密码重置验证码接口
            $.ajax({
                url: '/api/common/send_reset_code',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    email: email
                }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('验证码已发送到您的邮箱', 'success');
                        // 不再调用resetSendButton，保持按钮禁用状态
                        
                        // 获取Alpine.js组件实例并立即开始倒计时
                        const passwordCard = document.querySelector('[x-data="passwordManager()"]');
                        if (passwordCard && passwordCard.__x) {
                            // 使用Alpine.js v3的新API
                            passwordCard.__x.$data.startEmailCodeCooldown();
                        } else {
                            // 备用方案：直接使用JavaScript倒计时
                            startButtonCooldown();
                        }
                    } else {
                        showMessage(response.message || '验证码发送失败', 'error');
                        resetSendButton();
                    }
                },
                error: function(xhr) {
                    let errorMessage = '网络错误，请稍后重试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    showMessage(errorMessage, 'error');
                    resetSendButton();
                }
            });
            
            // 重置按钮状态的函数
            function resetSendButton() {
                sendBtn.innerHTML = originalText;
                sendBtn.disabled = false;
            }
            
            // 备用倒计时函数
            function startButtonCooldown() {
                let countdown = 60;
                // 立即更新按钮显示倒计时状态
                sendBtn.innerHTML = `<i class="fas fa-paper-plane mr-2"></i><span>${countdown}s</span>`;
                sendBtn.disabled = true; // 确保按钮在倒计时期间不可点击
                
                const timer = setInterval(() => {
                    countdown--;
                    if (countdown < 0) {
                        clearInterval(timer);
                        sendBtn.innerHTML = originalText;
                        sendBtn.disabled = false;
                    } else {
                        // 更新倒计时显示
                        sendBtn.innerHTML = `<i class="fas fa-paper-plane mr-2"></i><span>${countdown}s</span>`;
                    }
                }, 1000);
            }
        });
        
        // 旧密码方式修改密码
        document.getElementById('changePasswordBtn').addEventListener('click', function() {
            const current_password = document.getElementById('current_password').value;
            const new_password = document.getElementById('new_password').value;
            const confirm_password = document.getElementById('confirm_password').value;
            
            // 表单验证
            if (!current_password) {
                showMessage('请输入当前密码', 'error');
                return;
            }
            
            if (!new_password) {
                showMessage('请输入新密码', 'error');
                return;
            }
            
            if (new_password.length < 6) {
                showMessage('新密码长度不能少于6个字符', 'error');
                return;
            }
            
            if (new_password !== confirm_password) {
                showMessage('两次输入的新密码不一致', 'error');
                return;
            }
            
            // 显示加载状态
            const pwdBtn = document.getElementById('changePasswordBtn');
            const originalText = pwdBtn.innerHTML;
            pwdBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i><span>处理中...</span>';
            pwdBtn.disabled = true;
            
            // 提交数据
            $.ajax({
                url: '/api/common/change_password',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    current_password: current_password,
                    new_password: new_password,
                    confirm_password: confirm_password
                }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('密码修改成功！', 'success');
                        // 清空密码输入框
                        document.getElementById('current_password').value = '';
                        document.getElementById('new_password').value = '';
                        document.getElementById('confirm_password').value = '';
                    } else {
                        showMessage(response.message || '密码修改失败', 'error');
                    }
                },
                error: function(xhr) {
                    let errorMessage = '网络错误，请稍后重试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    showMessage(errorMessage, 'error');
                },
                complete: function() {
                    // 还原按钮状态
                    pwdBtn.innerHTML = originalText;
                    pwdBtn.disabled = false;
                }
            });
        });
        
        // 邮箱验证码方式修改密码
        document.getElementById('changePasswordByEmailBtn').addEventListener('click', function() {
            const emailField = document.getElementById('email');
            const email = emailField ? emailField.value.trim() : '';
            const verification_code = document.getElementById('email_verification_code').value.trim();
            const new_password = document.getElementById('email_new_password').value;
            const confirm_password = document.getElementById('email_confirm_password').value;
            
            // 表单验证
            if (!email) {
                showMessage('请先设置并保存邮箱地址', 'error');
                return;
            }
            
            if (!verification_code) {
                showMessage('请输入邮箱验证码', 'error');
                return;
            }
            
            if (verification_code.length !== 6) {
                showMessage('验证码应为6位数字', 'error');
                return;
            }
            
            if (!new_password) {
                showMessage('请输入新密码', 'error');
                return;
            }
            
            if (new_password.length < 6) {
                showMessage('新密码长度不能少于6个字符', 'error');
                return;
            }
            
            if (new_password !== confirm_password) {
                showMessage('两次输入的新密码不一致', 'error');
                return;
            }
            
            // 显示加载状态
            const pwdBtn = document.getElementById('changePasswordByEmailBtn');
            const originalText = pwdBtn.innerHTML;
            pwdBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i><span>处理中...</span>';
            pwdBtn.disabled = true;
            
            // 使用后端的密码重置接口
            $.ajax({
                url: '/api/common/reset_password',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    email: email,
                    code: verification_code,
                    new_password: new_password
                }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('密码修改成功！', 'success');
                        // 清空邮箱验证码表单
                        document.getElementById('email_verification_code').value = '';
                        document.getElementById('email_new_password').value = '';
                        document.getElementById('email_confirm_password').value = '';
                    } else {
                        showMessage(response.message || '密码修改失败', 'error');
                    }
                },
                error: function(xhr) {
                    let errorMessage = '网络错误，请稍后重试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    showMessage(errorMessage, 'error');
                },
                complete: function() {
                    // 还原按钮状态
                    pwdBtn.innerHTML = originalText;
                    pwdBtn.disabled = false;
                }
            });
        });

        // CustomSelect 组件类
        class CustomSelect {
            constructor(containerId, options = {}) {
                this.container = $('#' + containerId);
                this.trigger = this.container.find('.custom-select-trigger');
                this.dropdown = this.container.find('.custom-select-dropdown');
                this.searchInput = this.container.find('.custom-select-search input');
                this.optionsContainer = this.container.find('.custom-select-options');
                this.textSpan = this.trigger.find('.custom-select-text');

                this.options = [];
                this.selectedValue = '';
                this.selectedText = '';
                this.placeholder = options.placeholder || '请选择';
                this.disabled = options.disabled || false;
                this.onSelect = options.onSelect || null;
                this.showSearch = options.showSearch !== false; // 默认显示搜索框

                this.init();
            }

            init() {
                // 点击触发器
                this.trigger.on('click', (e) => {
                    e.stopPropagation();
                    if (!this.disabled) {
                        this.toggle();
                    }
                });

                // 搜索输入 (仅在显示搜索框时绑定)
                if (this.showSearch && this.searchInput.length > 0) {
                    this.searchInput.on('input', (e) => {
                        this.filterOptions(e.target.value);
                    });
                }

                // 点击选项
                this.optionsContainer.on('click', '.custom-select-option:not(.no-results)', (e) => {
                    const option = $(e.target);
                    const value = option.data('value');
                    const text = option.text();
                    this.selectOption(value, text);
                });

                // 点击外部关闭
                $(document).on('click', (e) => {
                    if (!this.container.is(e.target) && this.container.has(e.target).length === 0) {
                        this.close();
                    }
                });

                // 初始化文本
                this.textSpan.text(this.placeholder);
            }

            setOptions(options) {
                this.options = options;
                this.renderOptions();
            }

            setValue(value) {
                const option = this.options.find(opt => opt.value == value);
                if (option) {
                    this.selectOption(value, option.text);
                }
            }

            getValue() {
                return this.selectedValue;
            }

            getText() {
                return this.selectedText;
            }

            reset() {
                this.selectedValue = '';
                this.selectedText = '';
                this.textSpan.text(this.placeholder);
                if (this.showSearch && this.searchInput.length > 0) {
                    this.searchInput.val('');
                }
                this.renderOptions();
                this.close();
            }

            toggle() {
                if (this.container.hasClass('active')) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                this.container.addClass('active');
                if (this.showSearch && this.searchInput.length > 0) {
                    this.searchInput.focus();
                }
            }

            close() {
                this.container.removeClass('active');
            }

            selectOption(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.textSpan.text(text);
                this.close();

                if (this.onSelect) {
                    this.onSelect(value, text);
                }
            }

            filterOptions(searchTerm) {
                const filteredOptions = this.options.filter(option =>
                    option.text.toLowerCase().includes(searchTerm.toLowerCase())
                );
                this.renderOptions(filteredOptions);
            }

            renderOptions(optionsToRender = null) {
                const options = optionsToRender || this.options;
                this.optionsContainer.empty();

                if (options.length === 0) {
                    this.optionsContainer.append('<div class="custom-select-option no-results">无匹配结果</div>');
                    return;
                }

                options.forEach(option => {
                    const optionEl = $(`<div class="custom-select-option" data-value="${option.value}">${option.text}</div>`);
                    if (option.value == this.selectedValue) {
                        optionEl.addClass('selected');
                    }
                    this.optionsContainer.append(optionEl);
                });
            }

            setDisabled(disabled) {
                this.disabled = disabled;
                if (disabled) {
                    this.trigger.addClass('disabled');
                    this.close();
                } else {
                    this.trigger.removeClass('disabled');
                }
            }

            destroy() {
                // 移除事件监听器
                this.trigger.off('click');
                if (this.showSearch && this.searchInput.length > 0) {
                    this.searchInput.off('input');
                }
                this.optionsContainer.off('click');

                // 重置DOM状态
                this.container.removeClass('active');
                this.textSpan.text(this.placeholder);
                if (this.showSearch && this.searchInput.length > 0) {
                    this.searchInput.val('');
                }
                this.optionsContainer.empty();

                // 清理数据
                this.selectedValue = '';
                this.selectedText = '';
                this.options = [];
            }
        }

    </script>
</body>
</html>
