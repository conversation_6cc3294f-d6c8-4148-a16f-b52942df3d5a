<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书展活动管理 - 管理员中心</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <style>
        /* 状态标签样式 */
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-draft {
            background-color: #e5e7eb;
            color: #4b5563;
        }
        .status-published {
            background-color: #dcfce7;
            color: #166534;
        }
        .status-cancelled {
            background-color: #fee2e2;
            color: #b91c1c;
        }
        .status-ended {
            background-color: #f3f4f6;
            color: #1f2937;
        }
        
        /* 动画效果 */
        .animate-fadeIn {
            animation: fadeIn 0.3s ease-in;
        }
        .animate-fadeOut {
            animation: fadeOut 0.3s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
        }
        
        /* 活动卡片悬停效果 */
        .exhibition-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .exhibition-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        /* 模态框背景 */
        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(2px);
        }
        
        /* 消息提示容器 - 确保最高层级 */
        #messageContainer {
            z-index: 9999 !important; 
            pointer-events: none;
        }
        
        /* 消息样式 */
        .message-toast {
            pointer-events: auto;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
            min-width: 250px;
        }
        
        /* 响应式调整 */
        @media (max-width: 640px) {
            .status-tabs {
                overflow-x: auto;
                white-space: nowrap;
                -webkit-overflow-scrolling: touch;
                padding-bottom: 0.5rem;
            }
            .status-tab {
                display: inline-block;
            }
        }

        /* 移动端模态框样式调整 */
        @media (max-width: 768px) {
            .modal-content {
                width: 92% !important;
                max-width: 92% !important;
                max-height: 92% !important;
                margin: 0 auto;
            }
            
            .modal-body {
                max-height: calc(85vh - 130px);
                overflow-y: auto;
            }
        }
        
        /* 模态框内容样式 */
        .modal-header {
            position: sticky;
            top: 0;
            background-color: white;
            z-index: 10;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .modal-body {
            flex-grow: 1;
            overflow-y: auto;
            max-height: calc(80vh - 130px);
        }
        
        .modal-footer {
            position: sticky;
            bottom: 0;
            background-color: white;
            z-index: 10;
            border-top: 1px solid #e5e7eb;
        }
        
        /* 错误信息模态框样式 */
        .error-modal-content {
            max-width: 450px !important;
        }
        
        /* 所有模态框容器 */
        #errorModalContainer, #confirmModalContainer, #detailModalContainer, #participantsModalContainer {
            z-index: 9000;
        }
        
        /* 编辑状态模态框单独设置更高的z-index */
        #statusEditModalContainer {
            z-index: 9100;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-[9999] flex flex-col items-end space-y-2"></div>
    
    <div class="container mx-auto px-4 py-6">
        <header class="mb-6 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">书展活动管理</h1>
                <p class="text-gray-600">管理所有书展活动</p>
            </div>
        </header>
        
        <!-- 标签状态切换 -->
        <div class="bg-white rounded-lg shadow p-4 mb-6">
            <div class="status-tabs flex space-x-2 border-b border-gray-200 pb-3 mb-4">
                <button id="allTab" class="status-tab px-4 py-2 rounded-t-lg font-medium border-b-2 border-blue-500 text-blue-600 bg-blue-50">
                    全部 <span id="allCount" class="inline-flex items-center justify-center w-5 h-5 ml-1 text-xs bg-blue-100 text-blue-600 rounded-full">0</span>
                </button>
                <button id="draftTab" class="status-tab px-4 py-2 rounded-t-lg font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50">
                    草稿 <span id="draftCount" class="inline-flex items-center justify-center w-5 h-5 ml-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                </button>
                <button id="publishedTab" class="status-tab px-4 py-2 rounded-t-lg font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50">
                    已发布 <span id="publishedCount" class="inline-flex items-center justify-center w-5 h-5 ml-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                </button>
                <button id="cancelledTab" class="status-tab px-4 py-2 rounded-t-lg font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50">
                    已取消 <span id="cancelledCount" class="inline-flex items-center justify-center w-5 h-5 ml-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                </button>
                <button id="endedTab" class="status-tab px-4 py-2 rounded-t-lg font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50">
                    已结束 <span id="endedCount" class="inline-flex items-center justify-center w-5 h-5 ml-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                </button>
            </div>
            
            <!-- 筛选和搜索区域 -->
            <div class="flex flex-col gap-4">
                <div class="flex flex-col sm:flex-row sm:flex-wrap gap-4">
                    <div class="relative w-full sm:w-48">
                        <label for="dateFilter" class="block text-sm font-medium text-gray-700 mb-1">时间筛选</label>
                        <select id="dateFilter" class="block w-full bg-white border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-8 shadow-sm">
                            <option value="all">全部时间</option>
                            <option value="upcoming">即将开始</option>
                            <option value="ongoing">进行中</option>
                            <option value="past">已结束</option>
                            <option value="custom">自定义时间</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700" style="top: 22px;">
                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                        </div>
                    </div>
                    
                    <div class="relative w-full sm:w-48">
                        <label for="schoolFilter" class="block text-sm font-medium text-gray-700 mb-1">学校筛选</label>
                        <select id="schoolFilter" class="block w-full bg-white border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-8 shadow-sm">
                            <option value="all">全部学校</option>
                            <!-- 这里将通过JS动态添加学校选项 -->
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700" style="top: 22px;">
                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                        </div>
                    </div>
                    
                    <div id="customDateContainer" class="hidden w-full sm:w-auto sm:flex sm:items-end">
                        <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                                <input type="date" id="startDate" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
                            </div>
                            <span class="text-center self-end pb-2">至</span>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                                <input type="date" id="endDate" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center w-full">
                    <input type="text" id="searchInput" placeholder="搜索书展主题..." 
                           class="border border-gray-300 rounded-l-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm w-full">
                    <button id="searchBtn" class="bg-blue-500 text-white px-4 py-2 rounded-r-md hover:bg-blue-600 shadow-sm">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 活动列表容器 -->
        <div id="exhibitionsContainer" class="space-y-4">
            <!-- 这里将通过JS动态添加活动卡片 -->
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-spinner fa-spin text-2xl mb-3"></i>
                <p>加载中，请稍候...</p>
            </div>
        </div>
        
        <!-- 分页控件 -->
        <div class="flex justify-between items-center mt-6 bg-white rounded-lg shadow p-2">
            <button id="prevPageBtn" class="px-4 py-2 border-r border-gray-200 text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                <i class="fas fa-chevron-left mr-1"></i>上一页
            </button>
            <div id="pageNumbers" class="flex"></div>
            <button id="nextPageBtn" class="px-4 py-2 border-l border-gray-200 text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                下一页<i class="fas fa-chevron-right ml-1"></i>
            </button>
        </div>
    </div>

    <!-- 错误信息模态框 -->
    <div id="errorModalContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
        <div class="error-modal-content bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">提示</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#errorModalContainer').addClass('hidden')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <p id="errorModalContent" class="text-gray-600"></p>
            </div>
            <div class="px-4 py-3 bg-gray-50 text-right rounded-b-lg">
                <button type="button" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600" onclick="$('#errorModalContainer').addClass('hidden')">确定</button>
            </div>
        </div>
    </div>
    
    <!-- 确认模态框 -->
    <div id="confirmModalContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h3 id="confirmModalTitle" class="text-lg font-semibold text-gray-800">确认操作</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#confirmModalContainer').addClass('hidden')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <p id="confirmModalContent" class="text-gray-600"></p>
            </div>
            <div class="px-4 py-3 bg-gray-50 text-right rounded-b-lg flex justify-end space-x-2">
                <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400" onclick="$('#confirmModalContainer').addClass('hidden')">取消</button>
                <button id="confirmModalButton" type="button" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">确认</button>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div id="detailModalContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
        <div class="modal-content bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
            <div class="modal-header p-4 flex justify-between items-center">
                <h3 id="detailModalTitle" class="text-xl font-semibold text-gray-800">书展详情</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#detailModalContainer').addClass('hidden')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body p-6 overflow-y-auto" id="detailModalBody">
                <!-- 详情内容将通过JS动态加载 -->
            </div>
            <div class="modal-footer p-4 flex justify-end gap-3">
                <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300" onclick="$('#detailModalContainer').addClass('hidden')">关闭</button>
                <button id="editStatusBtn" type="button" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">修改状态</button>
                <button id="viewParticipantsBtn" type="button" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">查看参展人员</button>
                <button id="deleteExhibitionBtn" type="button" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">删除书展</button>
            </div>
        </div>
    </div>

    <!-- 状态编辑模态框 -->
    <div id="statusEditModalContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">修改书展状态</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#statusEditModalContainer').addClass('hidden')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <label for="exhibitionStatus" class="block text-sm font-medium text-gray-700 mb-1">选择状态</label>
                    <select id="exhibitionStatus" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="draft">草稿</option>
                        <option value="published">已发布</option>
                        <option value="cancelled">已取消</option>
                        <option value="ended">已结束</option>
                    </select>
                </div>
                <p class="text-sm text-gray-500 italic">
                    状态说明:<br>
                    - 草稿: 书展未公开，只有管理员可见<br>
                    - 已发布: 书展公开可见，可以接受报名<br>
                    - 已取消: 书展已取消，不再接受报名<br>
                    - 已结束: 书展已结束，不再接受报名
                </p>
            </div>
            <div class="px-4 py-3 bg-gray-50 text-right rounded-b-lg flex justify-end space-x-2">
                <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400" onclick="$('#statusEditModalContainer').addClass('hidden')">取消</button>
                <button id="saveStatusBtn" type="button" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">保存</button>
            </div>
        </div>
    </div>

    <!-- 参展人员模态框 -->
    <div id="participantsModalContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
            <div class="modal-header p-4 flex justify-between items-center">
                <h3 class="text-xl font-semibold text-gray-800">参展人员名单</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#participantsModalContainer').addClass('hidden')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body p-6 overflow-y-auto" id="participantsModalBody">
                <!-- 参展人员列表将通过JS动态加载 -->
            </div>
            <div class="modal-footer p-4 flex justify-end">
                <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300" onclick="$('#participantsModalContainer').addClass('hidden')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 引入jQuery和AJAX -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // 在document ready外部定义这些变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 1;
        let currentTab = 'all';
        let currentExhibitionId = null;

        $(document).ready(function() {
            // 移除变量声明，只保留初始化
            currentPage = 1;
            pageSize = 10;
            totalPages = 1;
            currentTab = 'all';
            currentExhibitionId = null;
            
            // 绑定标签点击事件
            $('.status-tab').click(function() {
                // 移除所有标签的活动状态
                $('.status-tab').removeClass('border-b-2 border-blue-500 text-blue-600 bg-blue-50').addClass('text-gray-500');
                
                // 添加当前标签的活动状态
                $(this).addClass('border-b-2 border-blue-500 text-blue-600 bg-blue-50').removeClass('text-gray-500');
                
                // 获取标签ID并加载相应数据
                currentTab = $(this).attr('id').replace('Tab', '');
                currentPage = 1;
                loadExhibitions();
            });
            
            // 绑定自定义日期筛选显示/隐藏
            $('#dateFilter').change(function() {
                if ($(this).val() === 'custom') {
                    $('#customDateContainer').removeClass('hidden');
                } else {
                    $('#customDateContainer').addClass('hidden');
                }
                
                // 重新加载数据
                currentPage = 1;
                loadExhibitions();
            });
            
            // 绑定学校筛选变化事件
            $('#schoolFilter').change(function() {
                currentPage = 1;
                loadExhibitions();
            });
            
            // 绑定搜索按钮点击事件
            $('#searchBtn').click(function() {
                currentPage = 1;
                loadExhibitions();
            });
            
            // 绑定自定义日期筛选变化事件
            $('#startDate, #endDate').change(function() {
                if ($('#dateFilter').val() === 'custom') {
                    currentPage = 1;
                    loadExhibitions();
                }
            });
            
            // 绑定分页按钮点击事件
            $('#prevPageBtn').click(function() {
                if (currentPage > 1) {
                    currentPage--;
                    loadExhibitions();
                }
            });
            
            $('#nextPageBtn').click(function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    loadExhibitions();
                }
            });
            
            // 加载学校列表
            loadSchools();
            
            // 加载初始数据
            loadExhibitions();
        });

        // 加载学校列表
        function loadSchools() {
            $.ajax({
                url: '/api/common/get_schools',
                type: 'GET',
                data: { has_exhibitions: true },
                success: function(response) {
                    if (response.code === 0 && response.data) {
                        let options = '<option value="all">全部学校</option>';
                        response.data.forEach(school => {
                            options += `<option value="${school.id}">${school.name}</option>`;
                        });
                        $('#schoolFilter').html(options);
                    }
                }
            });
        }
        
        // 加载书展列表
        function loadExhibitions() {
            // 显示加载中提示
            $('#exhibitionsContainer').html(`
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-spinner fa-spin text-2xl mb-3"></i>
                    <p>加载中，请稍候...</p>
                </div>
            `);
            
            // 组装请求参数
            const params = {
                page: currentPage,
                limit: pageSize,
                tab: currentTab,
                search: $('#searchInput').val()
            };
            
            // 处理日期筛选
            const dateFilter = $('#dateFilter').val();
            if (dateFilter === 'custom') {
                params.start_date = $('#startDate').val();
                params.end_date = $('#endDate').val();
            }
            
            // 处理学校筛选
            const schoolId = $('#schoolFilter').val();
            if (schoolId && schoolId !== 'all') {
                params.school_id = schoolId;
            }
            
            // 发送AJAX请求
            $.ajax({
                url: '/api/admin/get_exhibitions',
                type: 'GET',
                data: params,
                success: function(response) {
                    if (response.code === 0) {
                        // 渲染书展列表
                        renderExhibitions(response.data);
                        
                        // 更新状态数量
                        updateStatusCounts(response.data.status_counts);
                        
                        // 渲染分页
                        renderPagination(response.data.total);
                    } else {
                        // 显示错误信息
                        showErrorModal(response.message || '获取书展列表失败');
                    }
                },
                error: function() {
                    showErrorModal('网络错误，请稍后再试');
                }
            });
        }
        
        // 渲染书展列表
        function renderExhibitions(data) {
            const exhibitions = data.exhibitions;
            
            if (exhibitions.length === 0) {
                $('#exhibitionsContainer').html(`
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-search text-2xl mb-3"></i>
                        <p>没有找到符合条件的书展活动</p>
                    </div>
                `);
                return;
            }
            
            let html = '';
            
            exhibitions.forEach(exhibition => {
                // 获取状态样式
                const statusClass = getStatusClass(exhibition.status);
                const statusText = getStatusText(exhibition.status);
                
                // 构建卡片HTML
                html += `
                    <div class="exhibition-card bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow">
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3">
                            <div class="flex items-center mb-2 sm:mb-0">
                                <h3 class="text-lg font-medium text-gray-800 mr-3">${exhibition.title}</h3>
                                <span class="status-badge ${statusClass} mr-1">${statusText}</span>
                            </div>
                            <div class="flex space-x-2 mt-2 sm:mt-0">
                                <button class="view-exhibition-btn bg-blue-50 text-blue-600 px-3 py-1 rounded-md hover:bg-blue-100 transition-colors" data-id="${exhibition.id}">
                                    <i class="fas fa-eye mr-1"></i>查看
                                </button>
                                <button class="edit-status-btn bg-green-50 text-green-600 px-3 py-1 rounded-md hover:bg-green-100 transition-colors" data-id="${exhibition.id}">
                                    <i class="fas fa-edit mr-1"></i>修改状态
                                </button>
                                <button class="delete-exhibition-btn bg-red-50 text-red-600 px-3 py-1 rounded-md hover:bg-red-100 transition-colors" data-id="${exhibition.id}">
                                    <i class="fas fa-trash-alt mr-1"></i>删除
                                </button>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-3">
                            <div>
                                <p class="text-sm text-gray-500">发起单位</p>
                                <p class="text-gray-800">${exhibition.school_name}</p>
                                ${exhibition.school_address ? `<p class="text-xs text-gray-600">地址: ${exhibition.school_address}</p>` : ''}
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">开始时间</p>
                                <p class="text-gray-800">${exhibition.start_time}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">结束时间</p>
                                <p class="text-gray-800">${exhibition.end_time}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">报名截止</p>
                                <p class="text-gray-800">${exhibition.registration_deadline}</p>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap justify-between items-center text-sm">
                            <div class="flex items-center text-gray-500">
                                <span class="mr-4"><i class="fas fa-map-marker-alt mr-1"></i>${exhibition.location}</span>
                                ${exhibition.school_address ? `<span class="mr-4"><i class="fas fa-university mr-1"></i>${exhibition.school_address}</span>` : ''}
                            </div>
                            <div class="flex items-center text-gray-500 mt-2 sm:mt-0">
                                <span class="mr-4"><i class="fas fa-user mr-1"></i>联系人: ${exhibition.contact_name}</span>
                                <span class="mr-4"><i class="fas fa-building mr-1"></i>机构: ${exhibition.registrations_count || 0}</span>
                                <span class="mr-4"><i class="fas fa-users mr-1"></i>参展人: ${exhibition.participants_count || 0}</span>
                                <span><i class="fas fa-clock mr-1"></i>创建于 ${exhibition.created_at}</span>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            $('#exhibitionsContainer').html(html);
            
            // 绑定按钮点击事件
            $('.view-exhibition-btn').click(function() {
                const id = $(this).data('id');
                viewExhibitionDetail(id);
            });
            
            $('.edit-status-btn').click(function() {
                const id = $(this).data('id');
                showStatusEditModal(id);
            });
            
            $('.delete-exhibition-btn').click(function() {
                const id = $(this).data('id');
                showDeleteConfirm(id);
            });
        }
        
        // 更新状态计数
        function updateStatusCounts(counts) {
            $('#allCount').text(counts.all || 0);
            $('#draftCount').text(counts.draft || 0);
            $('#publishedCount').text(counts.published || 0);
            $('#cancelledCount').text(counts.cancelled || 0);
            $('#endedCount').text(counts.ended || 0);
        }
        
        // 渲染分页
        function renderPagination(total) {
            totalPages = Math.ceil(total / pageSize);
            
            // 禁用或启用上一页、下一页按钮
            $('#prevPageBtn').prop('disabled', currentPage <= 1);
            $('#nextPageBtn').prop('disabled', currentPage >= totalPages);
            
            // 生成页码
            let pageHtml = '';
            const maxPageButtons = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
            let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
            
            if (endPage - startPage + 1 < maxPageButtons) {
                startPage = Math.max(1, endPage - maxPageButtons + 1);
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentPage;
                pageHtml += `
                    <button class="page-number px-4 py-2 border-r border-gray-200 ${isActive ? 'bg-blue-50 text-blue-600 font-medium' : 'hover:bg-gray-50 text-gray-600'}" data-page="${i}">
                        ${i}
                    </button>
                `;
            }
            
            $('#pageNumbers').html(pageHtml);
            
            // 绑定页码点击事件
            $('.page-number').click(function() {
                currentPage = parseInt($(this).data('page'));
                loadExhibitions();
            });
        }
        
        // 获取状态样式类
        function getStatusClass(status) {
            const statusMap = {
                'draft': 'bg-gray-100 text-gray-800',
                'published': 'bg-green-100 text-green-800',
                'cancelled': 'bg-red-100 text-red-800',
                'ended': 'bg-gray-100 text-gray-800'
            };
            
            return statusMap[status] || 'bg-gray-100 text-gray-800';
        }
        
        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'draft': '草稿',
                'published': '已发布',
                'cancelled': '已取消',
                'ended': '已结束'
            };
            
            return statusMap[status] || '未知';
        }
        
        // 显示错误模态框
        function showErrorModal(message) {
            $('#errorModalContent').text(message);
            $('#errorModalContainer').removeClass('hidden');
        }
        
        // 显示确认删除模态框
        function showDeleteConfirm(id) {
            currentExhibitionId = id;
            $('#confirmModalTitle').text('确认删除');
            $('#confirmModalContent').text('您确定要删除此书展活动吗？此操作不可恢复，相关的报名信息也将被删除。');
            $('#confirmModalButton').text('确认删除').removeClass().addClass('px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600');
            
            // 绑定确认按钮点击事件
            $('#confirmModalButton').off('click').on('click', function() {
                deleteExhibition(currentExhibitionId);
            });
            
            $('#confirmModalContainer').removeClass('hidden');
        }
        
        // 查看书展详情
        function viewExhibitionDetail(id) {
            $.ajax({
                url: '/api/admin/get_exhibition_detail',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    if (response.code === 0) {
                        renderExhibitionDetail(response.data);
                        $('#detailModalContainer').removeClass('hidden');
                        
                        // 保存当前书展ID
                        currentExhibitionId = id;
                        
                        // 绑定按钮事件
                        $('#editStatusBtn').off('click').on('click', function() {
                            showStatusEditModal(currentExhibitionId, response.data.status);
                        });
                        
                        $('#viewParticipantsBtn').off('click').on('click', function() {
                            viewExhibitionParticipants(currentExhibitionId);
                        });
                        
                        $('#deleteExhibitionBtn').off('click').on('click', function() {
                            showDeleteConfirm(currentExhibitionId);
                        });
                    } else {
                        showErrorModal(response.message || '获取详情失败');
                    }
                },
                error: function() {
                    showErrorModal('网络错误，请稍后再试');
                }
            });
        }
        
        // 渲染书展详情
        function renderExhibitionDetail(exhibition) {
            // 设置模态框标题
            $('#detailModalTitle').text(exhibition.title);
            
            // 获取状态样式
            const statusClass = getStatusClass(exhibition.status);
            const statusText = getStatusText(exhibition.status);
            
            // 构建基本信息HTML
            let html = `
                <div class="space-y-6">
                    <div>
                        <span class="status-badge ${statusClass} mb-2">${statusText}</span>
                        <h3 class="text-xl font-medium text-gray-800">${exhibition.title}</h3>
                        ${exhibition.logo_url ? `
                            <div class="flex justify-center mt-4 mb-2">
                                <img src="${exhibition.logo_url}" alt="活动Logo" class="object-contain max-h-48 border rounded p-2">
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h4 class="font-medium text-gray-700 mb-2">基本信息</h4>
                        <div class="space-y-2">
                            <div class="flex">
                                <span class="text-gray-500 w-24">发起单位:</span>
                                <span class="text-gray-800">${exhibition.school_name}</span>
                            </div>
                            ${exhibition.school_address ? `
                            <div class="flex">
                                <span class="text-gray-500 w-24">学校地址:</span>
                                <span class="text-gray-800">${exhibition.school_address}</span>
                            </div>
                            ` : ''}
                            <div class="flex">
                                <span class="text-gray-500 w-24">开始时间:</span>
                                <span class="text-gray-800">${exhibition.start_time}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">结束时间:</span>
                                <span class="text-gray-800">${exhibition.end_time}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">报名截止:</span>
                                <span class="text-gray-800">${exhibition.registration_deadline}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">创建时间:</span>
                                <span class="text-gray-800">${exhibition.created_at}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">更新时间:</span>
                                <span class="text-gray-800">${exhibition.updated_at || '无'}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">发起人账号:</span>
                                <span class="text-gray-800">${exhibition.initiator_username || '未知'}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h4 class="font-medium text-gray-700 mb-2">发起人信息</h4>
                        <div class="space-y-2">
                            <div class="flex">
                                <span class="text-gray-500 w-24">姓名:</span>
                                <span class="text-gray-800">${exhibition.initiator.name}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">电话:</span>
                                <span class="text-gray-800">${exhibition.initiator.phone}</span>
                            </div>
                            ${exhibition.initiator.department ? `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">部门:</span>
                                    <span class="text-gray-800">${exhibition.initiator.department}</span>
                                </div>
                            ` : ''}
                            ${exhibition.initiator.position ? `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">职务:</span>
                                    <span class="text-gray-800">${exhibition.initiator.position}</span>
                                </div>
                            ` : ''}
                            ${exhibition.initiator.email ? `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">邮箱:</span>
                                    <span class="text-gray-800">${exhibition.initiator.email}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    
                    ${exhibition.description ? `
                        <div class="bg-gray-50 p-4 rounded-lg mb-4">
                            <h4 class="font-medium text-gray-700 mb-2">书展介绍</h4>
                            <p class="text-gray-800 whitespace-pre-line">${exhibition.description}</p>
                        </div>
                    ` : ''}
                    
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h4 class="font-medium text-gray-700 mb-2">活动地点和要求</h4>
                        <div class="space-y-2">
                            <div class="flex">
                                <span class="text-gray-500 w-24">地点:</span>
                                <span class="text-gray-800">${exhibition.location}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">允许停车:</span>
                                <span class="text-gray-800">${exhibition.allows_parking ? '是' : '否'}</span>
                            </div>
                            
                            ${exhibition.requires_campus_registration ? `
                                <div class="mt-2">
                                    <p class="text-gray-500 font-medium">进校报备要求:</p>
                                    <div class="bg-white p-3 rounded border border-gray-200 mt-1">
                                        <p class="text-gray-800 whitespace-pre-line">${exhibition.registration_requirements || '无特殊要求'}</p>
                                        ${exhibition.registration_qrcode ? `
                                            <div class="flex justify-center mt-2">
                                                <img src="${exhibition.registration_qrcode}" alt="报备二维码" class="max-h-48 object-contain border rounded p-2">
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            ` : `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">进校报备:</span>
                                    <span class="text-gray-800">不需要报备</span>
                                </div>
                            `}
                            
                            ${exhibition.requirements ? `
                                <div class="mt-2">
                                    <p class="text-gray-500 font-medium">其他要求:</p>
                                    <div class="bg-white p-3 rounded border border-gray-200 mt-1">
                                        <p class="text-gray-800 whitespace-pre-line">${exhibition.requirements}</p>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">报名统计</h4>
                        <div class="text-gray-800">
                            ${(() => {
                                let totalRegistrations = exhibition.registrations ? exhibition.registrations.length : 0;
                                let activeRegistrations = 0;
                                let cancelledRegistrations = 0;
                                let totalParticipants = 0;
                                
                                if (exhibition.registrations && exhibition.registrations.length > 0) {
                                    exhibition.registrations.forEach(reg => {
                                        if (reg.status === 'registered') {
                                            activeRegistrations++;
                                            totalParticipants += (reg.participants ? reg.participants.length : 0);
                                        } else if (reg.status === 'cancelled') {
                                            cancelledRegistrations++;
                                        }
                                    });
                                }
                                
                                return `
                                    <div class="space-y-2">
                                        <p>共有 <span class="font-semibold text-blue-600">${totalRegistrations}</span> 个机构报名参展</p>
                                        <div class="flex flex-wrap gap-6 text-sm">
                                            <p>有效报名: <span class="font-semibold text-green-600">${activeRegistrations}</span> 个</p>
                                            <p>已取消报名: <span class="font-semibold text-red-600">${cancelledRegistrations}</span> 个</p>
                                            <p>参展人员: <span class="font-semibold text-blue-600">${totalParticipants}</span> 人</p>
                                        </div>
                                    </div>
                                `;
                            })()}
                            
                            ${exhibition.registrations && exhibition.registrations.length > 0 ? `
                                <div class="mt-3">
                                    <p class="text-sm text-gray-500 mb-2">点击"查看参展人员"按钮查看详细报名信息</p>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
            
            // 设置详情内容
            $('#detailModalBody').html(html);
        }
        
        // 显示状态编辑模态框
        function showStatusEditModal(id, currentStatus) {
            currentExhibitionId = id;
            
            // 设置当前状态
            $('#exhibitionStatus').val(currentStatus);
            
            // 绑定保存按钮事件
            $('#saveStatusBtn').off('click').on('click', function() {
                const newStatus = $('#exhibitionStatus').val();
                changeExhibitionStatus(currentExhibitionId, newStatus);
            });
            
            // 显示模态框
            $('#statusEditModalContainer').removeClass('hidden');
        }
        
        // 修改书展状态
        function changeExhibitionStatus(id, status) {
            $.ajax({
                url: '/api/admin/change_exhibition_status',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    id: id,
                    status: status
                }),
                success: function(response) {
                    $('#statusEditModalContainer').addClass('hidden');
                    
                    if (response.code === 0) {
                        // 修改成功，显示成功消息
                        showMessage(response.message || '状态已更新', 'success');
                        
                        // 刷新当前展览详情
                        viewExhibitionDetail(id);
                        
                        // 重新加载列表
                        loadExhibitions();
                    } else {
                        // 修改失败，显示错误信息
                        showErrorModal(response.message || '修改状态失败');
                    }
                },
                error: function() {
                    $('#statusEditModalContainer').addClass('hidden');
                    showErrorModal('网络错误，请稍后再试');
                }
            });
        }
        
        // 查看参展人员
        function viewExhibitionParticipants(id) {
            console.log('查看参展人员ID:', id);
            $('#participantsModalBody').html(`
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin text-blue-500 text-2xl mb-2"></i>
                    <p class="text-gray-500">加载参展人员数据中...</p>
                </div>
            `);
            $('#participantsModalContainer').removeClass('hidden');
            
            $.ajax({
                url: '/api/admin/get_exhibition_participants',
                type: 'GET',
                data: { exhibition_id: id },
                success: function(response) {
                    console.log('参展人员数据:', response);
                    if (response.code === 0) {
                        renderParticipants(response.data);
                    } else {
                        showErrorModal(response.message || '获取参展人员失败');
                        $('#participantsModalContainer').addClass('hidden');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('AJAX错误:', textStatus, errorThrown);
                    showErrorModal('网络错误，请稍后再试');
                    $('#participantsModalContainer').addClass('hidden');
                }
            });
        }
        
        // 渲染参展人员列表
        function renderParticipants(registrations) {
            console.log('渲染参展人员:', registrations);
            
            if (!registrations || registrations.length === 0) {
                $('#participantsModalBody').html(`
                    <div class="text-center py-4">
                        <p class="text-gray-500">暂无参展机构报名</p>
                    </div>
                `);
                return;
            }
            
            // 统计信息
            let totalRegistered = 0;
            let totalCancelled = 0;
            let totalParticipants = 0;
            
            registrations.forEach(reg => {
                if (reg.registration_status === 'registered') {
                    totalRegistered++;
                    totalParticipants += (reg.participant_count || 0);
                } else if (reg.registration_status === 'cancelled') {
                    totalCancelled++;
                }
            });
            
            let html = `
                <div class="space-y-6">
                    <div class="flex flex-wrap gap-4 bg-blue-50 p-3 rounded-lg">
                        <p class="text-sm text-gray-600">共有 <span class="font-medium text-blue-600">${totalRegistered}</span> 个单位有效报名 | <span class="font-medium text-orange-600">${totalCancelled}</span> 个单位已取消</p>
                        <p class="text-sm text-gray-600">总计 <span class="font-medium text-blue-600">${totalParticipants}</span> 名参展人员</p>
                    </div>
            `;
            
            registrations.forEach((registration, index) => {
                const participantCount = registration.participant_count || 0;
                const isRegistered = registration.registration_status === 'registered';
                const statusClass = isRegistered ? 'text-green-600' : 'text-red-600';
                const statusText = isRegistered ? '已报名' : '已取消';
                const cardClass = isRegistered ? 'bg-gray-50' : 'bg-gray-50 border border-red-200';
                
                html += `
                    <div class="${cardClass} p-4 rounded-lg">
                        <h4 class="font-medium text-gray-800 mb-2">
                            ${index + 1}. ${registration.company_name || '未知单位'}
                            <span class="text-sm ${statusClass} ml-2">(${statusText})</span>
                        </h4>
                        <div class="text-sm text-gray-500 mb-3">
                            <span class="mr-4">账号: ${registration.username}</span>
                            <span class="mr-4">联系人: ${registration.contact_name || registration.publisher_name}</span>
                            ${registration.contact_phone ? `<span class="mr-4">联系电话: ${registration.contact_phone}</span>` : ''}
                            <span>报名时间: ${registration.registration_time}</span>
                        </div>
                        
                        <h5 class="font-medium text-gray-700 mt-4 mb-2">参展人员 (${participantCount}人)</h5>
                        <div class="bg-white p-3 rounded border border-gray-200">
                            ${registration.participants && registration.participants.length > 0 ? `
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead>
                                        <tr>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">手机号</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系人</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        ${registration.participants.map(participant => `
                                            <tr>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-800">${participant.name}</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-600">${participant.phone}</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-600">${participant.role || '-'}</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-600">
                                                    ${participant.is_contact ? '<span class="text-green-600 font-medium">是</span>' : '否'}
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            ` : `<p class="text-gray-500">暂无参展人员信息</p>`}
                        </div>
                    </div>
                `;
            });
            
            html += `
                <div class="bg-blue-50 p-3 rounded-lg mt-4">
                    <p class="text-blue-800 font-medium">统计信息</p>
                    <p class="text-blue-700 text-sm mt-1">共有 ${totalRegistered} 个有效报名单位，${totalCancelled} 个已取消单位，总计 ${totalParticipants} 名参展人员</p>
                </div>
            </div>
            `;
            
            $('#participantsModalBody').html(html);
        }
        
        // 删除书展
        function deleteExhibition(id) {
            $.ajax({
                url: '/api/admin/delete_exhibition',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ id: id }),
                success: function(response) {
                    $('#confirmModalContainer').addClass('hidden');
                    
                    if (response.code === 0) {
                        // 删除成功，显示成功消息
                        showMessage(response.message || '书展已成功删除', 'success');
                        
                        // 重新加载书展列表
                        loadExhibitions();
                    } else {
                        // 删除失败，显示错误信息
                        showErrorModal(response.message || '删除书展失败');
                    }
                },
                error: function() {
                    $('#confirmModalContainer').addClass('hidden');
                    showErrorModal('网络错误，请稍后再试');
                }
            });
        }
        
        // 显示消息提示
        function showMessage(message, type = 'success') {
            const id = Date.now();
            const typeClass = type === 'success' ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200';
            const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';
            
            const messageHtml = `
                <div id="message-${id}" class="message-toast ${typeClass} px-4 py-3 rounded-lg shadow-md border mb-3 animate-fadeIn">
                    <div class="flex items-center">
                        <i class="fas fa-${icon} mr-2"></i>
                        <span>${message}</span>
                    </div>
                </div>
            `;
            
            $('#messageContainer').append(messageHtml);
            
            // 3秒后自动移除
            setTimeout(function() {
                $(`#message-${id}`).removeClass('animate-fadeIn').addClass('animate-fadeOut');
                setTimeout(function() {
                    $(`#message-${id}`).remove();
                }, 300);
            }, 3000);
        }

        // 格式化日期
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
    </script>
</body>
</html> 