<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报备申请管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <link rel="stylesheet" href="/static/css/jquery-ui.css">
    <script src="/static/jquery.js"></script>
    <script src="/static/js/jquery-ui.min.js"></script>
    <style>
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
        }
        .status-pending {
            background-color: #ffc107;
            color: #212529;
        }
        .status-approved {
            background-color: #28a745;
            color: white;
        }
        .status-rejected {
            background-color: #dc3545;
            color: white;
        }
        .status-conflict {
            background-color: #dc3545;
            color: white;
        }
        .has-conflict {
            position: relative;
        }
        .conflict-banner {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            background-color: #dc3545;
            color: white;
            padding: 2px 10px;
            font-size: 12px;
            display: flex;
            align-items: center;
        }
        .tab-active {
            border-bottom: 2px solid #3b82f6;
            color: #2563eb;
            font-weight: 500;
        }
        .request-card {
            position: relative;
            transition: all 0.3s ease;
        }
        .request-card:hover {
            background-color: #f8f9fa;
        }
        .conflict-reports-container {
            padding: 15px;
            max-height: 500px;
            overflow-y: auto;
        }
        .conflict-reports-list {
            margin-top: 15px;
        }
        .conflict-report-item {
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .conflict-report-item:hover {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .conflict-report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eaeaea;
        }
        .conflict-report-details p {
            margin: 5px 0;
            font-size: 14px;
        }
        .ui-dialog {
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            overflow: visible;
        }
        .ui-dialog .ui-dialog-titlebar {
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 8px 8px 0 0;
            padding: 10px 15px;
        }
        .ui-dialog .ui-dialog-content {
            padding: 15px;
            max-height: 70vh !important;
            overflow-y: auto;
        }
        .ui-dialog-buttonpane {
            border-top: 1px solid #e0e0e0;
            background: #f9f9f9;
            margin: 0;
            padding: 10px 15px;
        }
        .ui-button {
            background: #ef4444;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        .ui-button:hover {
            background: #dc2626;
        }
        .ui-widget-overlay {
            background: rgba(0, 0, 0, 0.5);
            opacity: 1;
        }
        /* 批量操作按钮样式 */
        .batch-actions {
            margin-bottom: 1rem;
            padding: 0.75rem;
            background-color: #f8f9fa;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .batch-actions button {
            margin-left: 0.5rem;
        }
        
        /* 经销商单位分组样式 */
        .dealer-group {
            margin-bottom: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .dealer-group-header {
            padding: 0.75rem;
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .dealer-group-content {
            background-color: white;
        }
        
        /* 加载动画 */
        .spinner {
            border: 3px solid rgba(0, 0, 0, 0.1);
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border-left-color: #09f;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 复选框样式 */
        .report-checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        .dealer-checkbox {
            width: 18px;
            height: 18px;
            margin-right: 8px;
            cursor: pointer;
        }
        
        /* 全选复选框样式 */
        .select-all-container {
            display: flex;
            align-items: center;
        }
        
        #select-all-checkbox {
            width: 18px;
            height: 18px;
            margin-right: 8px;
            cursor: pointer;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 搜索容器样式 */
        .search-container {
            position: relative;
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 子标签样式 */
        .sub-tab-active {
            background: white !important;
            color: #2563eb !important;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 主内容区 -->
    <div class="flex-1 flex flex-col overflow-hidden">
        <!-- 顶部操作栏 -->
        <div class="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <!-- Tab栏切换 -->
                    <div class="flex bg-slate-100 rounded-lg p-1">
                        <button id="pendingTab"
                                class="flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all bg-white text-blue-600 shadow-sm">
                            <i class="fas fa-clock mr-2"></i>待处理申请
                        </button>
                        <button id="reviewedTab"
                                class="flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all text-slate-600">
                            <i class="fas fa-check-circle mr-2"></i>已处理申请
                        </button>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 搜索框 -->
                    <div class="search-container">
                        <input type="text" id="searchInput"
                               placeholder="搜索样书名称、作者、ISBN..."
                               class="w-80 h-12 pl-4 pr-12 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                        <button id="searchBtn"
                                class="absolute right-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <button id="refreshBtn"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>刷新
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 滚动内容区 -->
        <div class="flex-1 overflow-y-auto custom-scrollbar p-6">
            <!-- 已处理申请子标签 -->
            <div id="reviewedSubTabs" class="mb-6 hidden">
                <div class="flex bg-slate-100 rounded-lg p-1 w-fit">
                    <button class="sub-tab sub-tab-active text-sm font-medium py-2 px-3 rounded-md transition-all bg-white text-blue-600 shadow-sm" data-status="all">
                        全部
                    </button>
                    <button class="sub-tab text-sm font-medium py-2 px-3 rounded-md transition-all text-slate-600" data-status="approved">
                        已通过
                    </button>
                    <button class="sub-tab text-sm font-medium py-2 px-3 rounded-md transition-all text-slate-600" data-status="rejected">
                        已拒绝
                    </button>
                </div>
            </div>

            <!-- 待处理申请列表 -->
            <div id="pendingList" class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden">
                <div class="p-6 text-center text-slate-500">
                    <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                </div>
            </div>

            <!-- 已处理申请列表 -->
            <div id="reviewedList" class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden hidden">
                <div class="p-6 text-center text-slate-500">
                    <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                </div>
            </div>

            <!-- 分页控件 -->
            <div id="pagination" class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
                <!-- 信息显示区域 -->
                <div class="flex items-center">
                    <p class="text-sm text-gray-700 mr-4">
                        第 <span id="currentPage" class="font-medium">1</span> 页，
                        共 <span id="totalPages" class="font-medium">1</span> 页，
                        共 <span id="totalCount" class="font-medium">0</span> 条
                    </p>
                </div>

                <!-- 分页按钮区域 -->
                <div class="flex gap-1">
                    <!-- 首页按钮 -->
                    <button id="firstBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="sr-only">首页</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <!-- 上一页按钮 -->
                    <button id="prevBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        上一页
                    </button>

                    <!-- 页码按钮容器 -->
                    <div id="pageNumbers" class="flex gap-1">
                        <!-- 页码将通过JavaScript动态生成 -->
                    </div>

                    <!-- 下一页按钮 -->
                    <button id="nextBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        下一页
                    </button>

                    <!-- 末页按钮 -->
                    <button id="lastBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="sr-only">末页</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modalContainer" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 id="modalTitle" class="text-xl font-semibold text-slate-800">模态框标题</h3>
                    <button id="closeModalBtn"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <div id="modalBody" class="p-6 overflow-y-auto max-h-[70vh] custom-scrollbar">
                    <!-- 模态框内容 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 flex flex-col space-y-2"></div>
    
    <script>
        $(document).ready(function() {
            // 全局变量和初始配置
            let currentTab = 'pending';
            let currentProcessedTab = 'all';
            let currentPage = 1;
            let totalPages = 1;
            let searchKeyword = '';
            
            // DOM元素
            const pendingTab = $('#pendingTab');
            const reviewedTab = $('#reviewedTab');
            const reviewedSubTabs = $('#reviewedSubTabs');
            const subTabs = $('.sub-tab');
            const pendingList = $('#pendingList');
            const reviewedList = $('#reviewedList');
            const pagination = $('#pagination');
            const searchInput = $('#searchInput');
            const searchBtn = $('#searchBtn');
            const refreshBtn = $('#refreshBtn');
            const modalContainer = $('#modalContainer');
            const modalTitle = $('#modalTitle');
            const modalBody = $('#modalBody');
            const closeModalBtn = $('#closeModalBtn');
            const messageContainer = $('#messageContainer');
            
            // 将报备按经销商单位分组
            function groupReportsByDealer(reports) {
                const grouped = {};
                
                reports.forEach(report => {
                    const dealerUnit = report.dealer_company_name || '未知单位';
                    
                    if (!grouped[dealerUnit]) {
                        grouped[dealerUnit] = [];
                    }
                    
                    grouped[dealerUnit].push(report);
                });
                
                return grouped;
            }
            
            // 更新批量操作按钮状态
            function updateBatchButtonsState() {
                const checkedCount = $('.report-checkbox:checked').length;
                $('#batchApprovePending, #batchRejectPending').prop('disabled', checkedCount === 0);
            }
            
            // 获取选中的报备ID
            function getSelectedReportIds() {
                return $('.report-checkbox:checked').map(function() {
                    return $(this).data('id');
                }).get();
            }
            
            // 初始化页面
            // 初始加载
            loadPendingReports();
            
            // 标签切换事件
            pendingTab.click(function() {
                currentTab = 'pending';
                currentPage = 1;

                // 更新tab样式
                pendingTab.removeClass('text-slate-600').addClass('bg-white text-blue-600 shadow-sm');
                reviewedTab.removeClass('bg-white text-blue-600 shadow-sm').addClass('text-slate-600');

                // 切换内容显示
                pendingList.removeClass('hidden');
                reviewedList.addClass('hidden');
                reviewedSubTabs.addClass('hidden');

                loadPendingReports();
            });
            
            reviewedTab.click(function() {
                currentTab = 'reviewed';
                currentPage = 1;
                currentProcessedTab = 'all';

                // 更新tab样式
                reviewedTab.removeClass('text-slate-600').addClass('bg-white text-blue-600 shadow-sm');
                pendingTab.removeClass('bg-white text-blue-600 shadow-sm').addClass('text-slate-600');

                // 切换内容显示
                reviewedList.removeClass('hidden');
                pendingList.addClass('hidden');
                reviewedSubTabs.removeClass('hidden');

                // 重置子标签状态
                subTabs.removeClass('sub-tab-active bg-white text-blue-600 shadow-sm').addClass('text-slate-600');
                $(`.sub-tab[data-status="all"]`).removeClass('text-slate-600').addClass('sub-tab-active bg-white text-blue-600 shadow-sm');

                loadProcessedReports();
            });
            
            // 子标签切换事件
            subTabs.click(function() {
                currentProcessedTab = $(this).data('status');
                currentPage = 1;

                // 重置所有子标签样式
                subTabs.removeClass('sub-tab-active bg-white text-blue-600 shadow-sm').addClass('text-slate-600');

                // 设置当前选中的子标签样式
                $(this).removeClass('text-slate-600').addClass('sub-tab-active bg-white text-blue-600 shadow-sm');

                loadProcessedReports();
            });
            
            // 搜索事件
            searchBtn.click(function() {
                searchKeyword = searchInput.val().trim();
                currentPage = 1;
                if (currentTab === 'pending') {
                    loadPendingReports();
                } else {
                    loadProcessedReports();
                }
            });
            
            searchInput.keypress(function(e) {
                if (e.which === 13) {
                    searchKeyword = searchInput.val().trim();
                    currentPage = 1;
                    if (currentTab === 'pending') {
                        loadPendingReports();
                    } else {
                        loadProcessedReports();
                    }
                }
            });

            // 刷新按钮事件
            refreshBtn.click(function() {
                refreshData();
            });
            
            // 分页事件
            $('#firstBtn').click(function() {
                if (currentPage !== 1) {
                    currentPage = 1;
                    if (currentTab === 'pending') {
                        loadPendingReports();
                    } else {
                        loadProcessedReports();
                    }
                }
            });

            $('#prevBtn').click(function() {
                if (currentPage > 1) {
                    currentPage--;
                    if (currentTab === 'pending') {
                        loadPendingReports();
                    } else {
                        loadProcessedReports();
                    }
                }
            });

            $('#nextBtn').click(function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    if (currentTab === 'pending') {
                        loadPendingReports();
                    } else {
                        loadProcessedReports();
                    }
                }
            });

            $('#lastBtn').click(function() {
                if (currentPage !== totalPages) {
                    currentPage = totalPages;
                    if (currentTab === 'pending') {
                        loadPendingReports();
                    } else {
                        loadProcessedReports();
                    }
                }
            });
            
            // 关闭模态框
            closeModalBtn.click(function() {
                closeModal();
            });
            
            // 关闭模态框函数
            function closeModal() {
                modalContainer.addClass('hidden');
                modalBody.html('');
            }

            // 刷新数据
            function refreshData() {
                if (currentTab === 'pending') {
                    loadPendingReports();
                } else {
                    loadProcessedReports();
                }
            }
            
            // 加载待处理报备
            function loadPendingReports() {
                pendingList.html('<div class="p-4 text-center text-gray-500"><i class="fas fa-spinner fa-spin mr-2"></i>加载中...</div>');
                
                $.ajax({
                    url: '/api/publisher/get_pending_reports',
                    type: 'GET',
                    data: {
                        search: searchKeyword,
                        page: currentPage,
                        limit: 10
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            const reports = response.data;
                            console.log("获取到的待处理报备数据:", reports);
                            totalPages = Math.ceil(response.count / 10);
                            
                            if (reports.length === 0) {
                                pendingList.html('<div class="p-4 text-center text-gray-500">暂无待处理的报备申请</div>');
                            } else {
                                // 按经销商单位分组
                                const groupedReports = groupReportsByDealer(reports);
                                let html = '';
                                
                                // 添加批量操作按钮
                                html += `
                                    <div class="p-4 border-b border-gray-200">
                                        <div class="flex justify-between items-center">
                                            <div class="flex items-center">
                                                <input type="checkbox" id="selectAllPending" class="mr-2">
                                                <label for="selectAllPending" class="text-sm font-medium">全选</label>
                                            </div>
                                            <div class="flex space-x-2">
                                                <button id="batchApprovePending" class="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50" disabled>
                                                    <i class="fas fa-check mr-1"></i>批量通过
                                                </button>
                                                <button id="batchRejectPending" class="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50" disabled>
                                                    <i class="fas fa-times mr-1"></i>批量拒绝
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                `;
                                
                                // 生成分组的报备卡片
                                Object.keys(groupedReports).forEach(dealerUnit => {
                                    const dealerReports = groupedReports[dealerUnit];
                                    
                                    html += `
                                        <div class="dealer-group mb-4">
                                            <div class="bg-gray-100 p-3 flex justify-between items-center">
                                                <div class="flex items-center">
                                                    <input type="checkbox" class="select-dealer-group mr-2" data-dealer="${dealerUnit}">
                                                    <h3 class="font-medium">${dealerUnit}</h3>
                                                </div>
                                                <span class="text-sm text-gray-500">共${dealerReports.length}条申请</span>
                                            </div>
                                            <div class="dealer-reports">
                                    `;
                                    
                                    dealerReports.forEach(report => {
                                    html += generateReportCard(report, 'pending', reports);
                                    });
                                    
                                    html += `
                                            </div>
                                        </div>
                                    `;
                                });
                                
                                pendingList.html(html);
                                
                                // 绑定全选事件
                                $('#selectAllPending').on('change', function() {
                                    const isChecked = $(this).prop('checked');
                                    $('.report-checkbox').prop('checked', isChecked);
                                    updateBatchButtonsState();
                                });
                                
                                // 绑定分组选择事件
                                $('.select-dealer-group').on('change', function() {
                                    const isChecked = $(this).prop('checked');
                                    const dealerUnit = $(this).data('dealer');
                                    $(`.report-checkbox[data-dealer="${dealerUnit}"]`).prop('checked', isChecked);
                                    updateBatchButtonsState();
                                });
                                
                                // 绑定单个复选框事件
                                $('.report-checkbox').on('change', function() {
                                    updateBatchButtonsState();
                                    
                                    // 更新分组复选框状态
                                    const dealerUnit = $(this).data('dealer');
                                    const totalInGroup = $(`.report-checkbox[data-dealer="${dealerUnit}"]`).length;
                                    const checkedInGroup = $(`.report-checkbox[data-dealer="${dealerUnit}"]:checked`).length;
                                    
                                    $(`.select-dealer-group[data-dealer="${dealerUnit}"]`).prop(
                                        'checked', 
                                        checkedInGroup > 0 && checkedInGroup === totalInGroup
                                    );
                                    
                                    // 更新全选复选框状态
                                    const totalCheckboxes = $('.report-checkbox').length;
                                    const checkedCheckboxes = $('.report-checkbox:checked').length;
                                    
                                    $('#selectAllPending').prop(
                                        'checked',
                                        checkedCheckboxes > 0 && checkedCheckboxes === totalCheckboxes
                                    );
                                });
                                
                                // 绑定批量通过按钮事件
                                $('#batchApprovePending').on('click', function() {
                                    if ($(this).prop('disabled')) return;
                                    
                                    const selectedIds = getSelectedReportIds();
                                    batchApproveReports(selectedIds);
                                });
                                
                                // 绑定批量拒绝按钮事件
                                $('#batchRejectPending').on('click', function() {
                                    if ($(this).prop('disabled')) return;
                                    
                                    const selectedIds = getSelectedReportIds();
                                    showBatchRejectModal(selectedIds);
                                });
                                
                                // 绑定操作按钮事件
                                $('.approve-btn').click(function() {
                                    const reportId = $(this).data('id');
                                    approveReport(reportId);
                                });
                                
                                $('.reject-btn').click(function() {
                                    const reportId = $(this).data('id');
                                    showRejectModal(reportId);
                                });
                                
                                $('.view-detail-btn').click(function() {
                                    const reportId = $(this).data('id');
                                    viewReportDetail(reportId);
                                });
                                
                                // 绑定冲突按钮事件
                                $('.view-conflict-btn').click(function() {
                                    const reportId = $(this).data('id');
                                    // 获取报备详情以显示冲突信息
                                    $.ajax({
                                        url: '/api/publisher/get_report_detail',
                                        type: 'GET',
                                        data: { id: reportId },
                                        success: function(response) {
                                            if (response.code === 0) {
                                                const report = response.data;
                                                console.log("冲突按钮点击 - 报备详情:", report);
                                                
                                                // 如果有冲突报备数组，显示冲突报备
                                                if (report.conflict_reports && report.conflict_reports.length > 0) {
                                                    viewConflictReports(report.conflict_reports, reportId);
                                                } 
                                                // 如果只有冲突原因，显示冲突原因
                                                else if (report.conflict_reason) {
                                                    // 显示一个带有冲突原因的简单对话框
                                                    showSimpleConflictDialog(report.conflict_reason, reportId);
                                                }
                                                else {
                                                    showToast('无法获取冲突信息', 'warning');
                                                }
                                            } else {
                                                showToast(response.message, 'error');
                                            }
                                        },
                                        error: function() {
                                            showToast('网络错误，请稍后重试', 'error');
                                        }
                                    });
                                });
                            }
                            
                            updatePagination(response.count);
                        } else {
                            pendingList.html(`<div class="p-4 text-center text-red-500">加载失败: ${response.message}</div>`);
                        }
                    },
                    error: function() {
                        pendingList.html('<div class="p-4 text-center text-red-500">网络错误，请稍后重试</div>');
                    }
                });
            }
            
            // 加载已处理报备
            function loadProcessedReports() {
                reviewedList.html('<div class="p-4 text-center text-gray-500"><i class="fas fa-spinner fa-spin mr-2"></i>加载中...</div>');
                
                $.ajax({
                    url: '/api/publisher/get_processed_reports',
                    type: 'GET',
                    data: {
                        search: searchKeyword,
                        status: currentProcessedTab,
                        page: currentPage,
                        limit: 10
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            // 确保reports是一个数组
                            let reports = response.data;
                            if (!Array.isArray(reports)) {
                                console.error("API返回的报备数据不是数组:", reports);
                                reports = [];
                            }
                            
                            console.log("获取到的已处理报备数据:", reports);
                            
                            // 调试 - 检查第一个报备的状态
                            if (reports.length > 0) {
                                console.log("第一个报备状态详情:", {
                                    报备ID: reports[0].id,
                                    状态: reports[0].status,
                                    状态类型: typeof reports[0].status,
                                    完整数据: reports[0]
                                });
                            }
                            
                            totalPages = Math.ceil(response.count / 10);
                            
                            if (reports.length === 0) {
                                reviewedList.html('<div class="p-4 text-center text-gray-500">暂无已处理的报备申请</div>');
                            } else {
                                // 确保每个报备对象都有有效的状态
                                reports.forEach(report => {
                                    if (!report.status) {
                                        // 为没有状态的报备提供默认值
                                        if (currentProcessedTab === 'approved') {
                                            report.status = 'approved';
                                        } else if (currentProcessedTab === 'rejected') {
                                            report.status = 'rejected';
                                        } else {
                                            // 根据其他线索推断状态
                                            report.status = report.reason ? 'rejected' : 'approved';
                                        }
                                        console.log(`报备 ${report.id} 状态修正为: ${report.status}`);
                                    }
                                });
                                
                                // 按经销商单位分组
                                const groupedReports = groupReportsByDealer(reports);
                                let html = '';
                                
                                // 生成分组的报备卡片
                                Object.keys(groupedReports).forEach(dealerUnit => {
                                    const dealerReports = groupedReports[dealerUnit];
                                    
                                    html += `
                                        <div class="dealer-group mb-4">
                                            <div class="bg-gray-100 p-3 flex justify-between items-center">
                                                <h3 class="font-medium">${dealerUnit}</h3>
                                                <span class="text-sm text-gray-500">共${dealerReports.length}条申请</span>
                                            </div>
                                            <div class="dealer-reports">
                                    `;
                                    
                                    dealerReports.forEach(report => {
                                    html += generateReportCard(report, 'reviewed', reports);
                                    });
                                    
                                    html += `
                                            </div>
                                        </div>
                                    `;
                                });
                                
                                reviewedList.html(html);
                                
                                // 绑定操作按钮事件
                                $('.revoke-btn').click(function() {
                                    const reportId = $(this).data('id');
                                    revokeReport(reportId);
                                });
                                
                                $('.view-detail-btn').click(function() {
                                    const reportId = $(this).data('id');
                                    viewReportDetail(reportId);
                                });
                                
                                $('.view-conflict-btn').click(function() {
                                    const reportId = $(this).data('id');
                                    // 获取报备详情以显示冲突信息
                                    $.ajax({
                                        url: '/api/publisher/get_report_detail',
                                        type: 'GET',
                                        data: { id: reportId },
                                        success: function(response) {
                                            if (response.code === 0) {
                                                const report = response.data;
                                                console.log("冲突按钮点击 - 报备详情:", report);
                                                
                                                // 如果有冲突报备数组，显示冲突报备
                                                if (report.conflict_reports && report.conflict_reports.length > 0) {
                                                    viewConflictReports(report.conflict_reports, reportId);
                                                } 
                                                // 如果只有冲突原因，显示冲突原因
                                                else if (report.conflict_reason) {
                                                    // 显示一个带有冲突原因的简单对话框
                                                    showSimpleConflictDialog(report.conflict_reason, reportId);
                                                }
                                                else {
                                                    showToast('无法获取冲突信息', 'warning');
                                                }
                                            } else {
                                                showToast(response.message, 'error');
                                            }
                                        },
                                        error: function() {
                                            showToast('网络错误，请稍后重试', 'error');
                                        }
                                    });
                                });
                            }
                            
                            updatePagination(response.count);
                        } else {
                            reviewedList.html(`<div class="p-4 text-center text-red-500">加载失败: ${response.message}</div>`);
                        }
                    },
                    error: function() {
                        reviewedList.html('<div class="p-4 text-center text-red-500">网络错误，请稍后重试</div>');
                    }
                });
            }
            
            // 生成报备卡片HTML
            function generateReportCard(report, type, allReports = []) {
                // 调试输出报备数据
                console.log(`生成${type}类型报备卡片:`, report);
                
                // 计算显示状态和冲突
                let displayStatus;
                
                // 简化冲突检测逻辑，主要检查conflict_reason字段
                const hasConflict = !!report.conflict_reason;
                
                console.log(`报备 ${report.id} 冲突检测结果:`, {
                    conflict_reason: report.conflict_reason,
                    检测结果: hasConflict
                });
                
                // 处理状态
                displayStatus = report.status || 'pending';
                
                console.log(`报备 ${report.id} 最终状态: ${displayStatus}`);
                
                // 为待处理且有冲突的报备创建特殊状态
                // 只在待处理页面显示冲突状态标签
                const statusKey = (hasConflict && displayStatus === 'pending' && type === 'pending') ? 
                    'pending_conflict' : displayStatus;
                
                const statusMap = {
                    'pending': '<span class="status-badge status-pending"><i class="fas fa-clock mr-1"></i>待处理</span>',
                    'pending_conflict': '<span class="status-badge status-pending"><i class="fas fa-clock mr-1"></i>待处理</span><span class="status-badge status-conflict ml-2"><i class="fas fa-exclamation-triangle mr-1"></i>冲突</span>',
                    'approved': '<span class="status-badge status-approved"><i class="fas fa-check-circle mr-1"></i>已通过</span>',
                    'rejected': '<span class="status-badge status-rejected"><i class="fas fa-times-circle mr-1"></i>已拒绝</span>'
                };
                
                // 确保有状态标签，默认为"未知"
                const statusHtml = statusMap[statusKey] || `<span class="status-badge bg-gray-500 text-white"><i class="fas fa-question-circle mr-1"></i>未知(${statusKey})</span>`;
                
                // 给存在冲突的卡片添加特殊样式 - 但仅在待处理页面
                const conflictClass = (hasConflict && type === 'pending') ? 'border-l-4 border-red-500 has-conflict pt-8' : '';
                
                // 获取经销商单位
                const dealerUnit = report.dealer_company_name || '未知单位';
                
                let html = `
                    <div class="request-card border-b border-gray-200 p-4 hover:bg-gray-50 ${conflictClass} relative">
                        ${(hasConflict && type === 'pending') ? 
                          `<div class="conflict-banner"><i class="fas fa-exclamation-triangle mr-1"></i>存在冲突报备</div>` : ''}
                        <div class="flex justify-between items-start">
                            ${type === 'pending' ? 
                              `<div class="flex items-start">
                                  <input type="checkbox" class="report-checkbox mt-1 mr-3" data-id="${report.id}" data-dealer="${dealerUnit}">
                              </div>` : ''}
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <h3 class="text-lg font-medium mr-2">${report.sample_name}</h3>
                                    ${statusHtml}
                                    </div>
                                <div class="text-sm text-gray-600 mb-1">
                                    <span class="mr-4"><i class="fas fa-building mr-1"></i>单位: ${dealerUnit}</span>
                                    <span><i class="fas fa-calendar-alt mr-1"></i>申请时间: ${report.created_at}</span>
                                    </div>
                                <div class="text-sm text-gray-600 mb-1">
                                    <span class="mr-4"><i class="fas fa-book mr-1"></i>ISBN: ${report.isbn || '无'}</span>
                                    <span><i class="fas fa-user-edit mr-1"></i>作者: ${report.author || '无'}</span>
                                </div>
                                <div class="text-sm text-gray-600 mb-1">
                                    <span class="mr-4"><i class="fas fa-school mr-1"></i>学校: ${report.school_name}</span>
                                </div>
                            </div>
                            <div class="flex flex-col space-y-2">
                `;
                
                if (type === 'pending') {
                    html += `
                                <button class="approve-btn px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600" data-id="${report.id}">
                                    <i class="fas fa-check mr-1"></i>批准
                                </button>
                                <button class="reject-btn px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600" data-id="${report.id}">
                                    <i class="fas fa-times mr-1"></i>拒绝
                                </button>
                                <button class="view-detail-btn px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600" data-id="${report.id}">
                                    <i class="fas fa-eye mr-1"></i>详情
                                </button>
                    `;
                    
                    // 添加冲突按钮（对待处理状态也显示）
                    if (hasConflict) {
                        html += `
                                <button class="view-conflict-btn px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 font-bold" data-id="${report.id}">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>查看冲突
                                </button>
                        `;
                    }
                } else {
                    html += `
                                <button class="revoke-btn px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600" data-id="${report.id}">
                                    <i class="fas fa-undo mr-1"></i>撤销
                                </button>
                                <button class="view-detail-btn px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600" data-id="${report.id}">
                                    <i class="fas fa-eye mr-1"></i>详情
                                </button>
                    `;
                    
                    if (report.conflict_reports && report.conflict_reports.length > 0) {
                        html += `
                                <button class="view-conflict-btn px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 font-bold" data-id="${report.id}">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>查看冲突
                                </button>
                        `;
                    }
                }
                
                html += `
                            </div>
                        </div>
                    </div>
                `;
                
                return html;
            }
            
            // 页码生成函数
            function getPageNumbers(currentPage, totalPages) {
                const pageNumbers = [];

                if (totalPages <= 7) {
                    // 总页数不超过7页，显示所有页码
                    for (let i = 1; i <= totalPages; i++) {
                        pageNumbers.push(i);
                    }
                } else {
                    // 总页数超过7页，使用省略号
                    pageNumbers.push(1);

                    if (currentPage <= 4) {
                        // 当前页在前部
                        pageNumbers.push(2, 3, 4, 5);
                        pageNumbers.push('...');
                        pageNumbers.push(totalPages);
                    } else if (currentPage >= totalPages - 3) {
                        // 当前页在后部
                        pageNumbers.push('...');
                        pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
                        pageNumbers.push(totalPages);
                    } else {
                        // 当前页在中部
                        pageNumbers.push('...');
                        pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                        pageNumbers.push('...');
                        pageNumbers.push(totalPages);
                    }
                }

                return pageNumbers;
            }

            // 渲染页码按钮
            function renderPageNumbers(containerSelector, currentPage, totalPages, clickHandler) {
                const container = $(containerSelector);
                container.empty();

                const pageNumbers = getPageNumbers(currentPage, totalPages);

                pageNumbers.forEach(pageNumber => {
                    if (pageNumber === '...') {
                        // 省略号
                        container.append(`
                            <span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700">
                                ...
                            </span>
                        `);
                    } else {
                        // 页码按钮
                        const isActive = pageNumber === currentPage;
                        const activeClass = isActive ? 'bg-blue-50 text-blue-600 border-blue-500' : 'bg-white text-gray-700';

                        container.append(`
                            <button data-page="${pageNumber}"
                                    class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md hover:bg-gray-50 ${activeClass}">
                                ${pageNumber}
                            </button>
                        `);
                    }
                });

                // 绑定页码点击事件
                container.off('click', 'button[data-page]').on('click', 'button[data-page]', function() {
                    const page = parseInt($(this).data('page'));
                    if (page && page !== currentPage) {
                        clickHandler(page);
                    }
                });
            }

            // 更新分页信息
            function updatePagination(totalCount = 0) {
                $('#currentPage').text(currentPage);
                $('#totalPages').text(totalPages);
                $('#totalCount').text(totalCount);

                // 更新按钮状态
                $('#firstBtn').prop('disabled', currentPage <= 1);
                $('#prevBtn').prop('disabled', currentPage <= 1);
                $('#nextBtn').prop('disabled', currentPage >= totalPages);
                $('#lastBtn').prop('disabled', currentPage >= totalPages);

                // 渲染页码
                renderPageNumbers('#pageNumbers', currentPage, totalPages, function(page) {
                    currentPage = page;
                    if (currentTab === 'pending') {
                        loadPendingReports();
                    } else {
                        loadProcessedReports();
                    }
                });

                pagination.toggle(totalPages > 1);
            }
            
            // 批准报备
            function approveReport(reportId) {
                $.ajax({
                    url: '/api/publisher/approve_report',
                    type: 'POST',
                    data: { id: reportId },
                    success: function(response) {
                        if (response.code === 0) {
                            showToast('报备申请已批准', 'success');
                            loadPendingReports();
                        } else {
                            showToast(response.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 显示拒绝模态框
            function showRejectModal(reportId) {
                modalTitle.text('拒绝报备申请');
                modalBody.html(`
                    <form id="rejectForm">
                    <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="reason">
                                拒绝原因
                            </label>
                            <textarea id="reason" name="reason" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" rows="4" placeholder="请输入拒绝原因..."></textarea>
                    </div>
                        <div class="flex justify-end">
                            <button type="button" id="cancelRejectBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 mr-2">
                            取消
                        </button>
                            <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600">
                                确认拒绝
                        </button>
                    </div>
                    </form>
                `);
                
                modalContainer.removeClass('hidden');
                
                // 取消按钮
                $('#cancelRejectBtn').click(closeModal);
                
                // 提交表单
                $('#rejectForm').submit(function(e) {
                    e.preventDefault();
                    
                    const reason = $('#reason').val().trim();
                    
                    $.ajax({
                        url: '/api/publisher/reject_report',
                        type: 'POST',
                        data: { 
                            id: reportId,
                            reason: reason
                        },
                        success: function(response) {
                            if (response.code === 0) {
                                showToast('报备申请已拒绝', 'success');
                                closeModal();
                                loadPendingReports();
                            } else {
                                showToast(response.message, 'error');
                            }
                        },
                        error: function() {
                            showToast('网络错误，请稍后重试', 'error');
                        }
                    });
                });
            }
            
            // 撤销处理
            function revokeReport(reportId) {
                $.ajax({
                    url: '/api/publisher/revoke_report',
                    type: 'POST',
                    data: { id: reportId },
                    success: function(response) {
                        if (response.code === 0) {
                            showToast('处理已撤销', 'success');
                            loadProcessedReports();
                        } else {
                            showToast(response.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 查看报备详情
            function viewReportDetail(reportId) {
                $.ajax({
                    url: '/api/publisher/get_report_detail',
                    method: 'GET',
                    data: { id: reportId },
                    success: function(response) {
                        // 检查响应格式并正确处理
                        let isSuccess = false;
                        let reportData = null;
                        let errorMsg = '';
                        
                        console.log("获取到的报备详情响应:", response);
                        
                        // 兼容不同的响应格式
                        if (response.success === true) {
                            isSuccess = true;
                            reportData = response.data || response.report;
                        } else if (response.code === 0) {
                            isSuccess = true;
                            reportData = response.data || response.report;
                        } else {
                            errorMsg = response.message || '未知错误';
                        }
                        
                        if (isSuccess && reportData) {
                            let report = reportData;
                            
                            // 设置模态框标题
                            modalTitle.text('样书报备详情');
                            
                            // 根据状态选择状态图标颜色
                            const statusColorClass = {
                                'pending': 'text-yellow-500',
                                'approved': 'text-green-500',
                                'rejected': 'text-red-500'
                            }[report.status] || 'text-blue-500';
                            
                            // 构建详情HTML - 一行一个信息
                            let detailHtml = `
                                <div class="p-4">
                                    <div class="flex items-center mb-6 bg-gray-50 p-3 rounded-lg border-l-4 ${statusColorClass.replace('text-', 'border-')}">
                                        <div class="mr-3 ${statusColorClass} text-xl">
                                            <i class="fas ${report.status === 'approved' ? 'fa-check-circle' : (report.status === 'rejected' ? 'fa-times-circle' : 'fa-clock')}"></i>
                                                </div>
                                                <div>
                                            <h3 class="text-lg font-semibold">报备状态: ${getStatusText(report.status)}</h3>
                                            <p class="text-sm text-gray-500">更新时间: ${report.processed_at || report.created_at}</p>
                                                </div>
                                                </div>
                                    
                                    <div class="bg-white rounded-lg border shadow-sm overflow-hidden mb-6">
                                        <div class="bg-gray-50 px-4 py-3 border-b">
                                            <h4 class="font-medium text-gray-700">样书信息</h4>
                                                </div>
                                        <div class="p-4">
                                            <p class="mb-3"><span class="text-gray-600 font-medium">样书名称:</span> <span class="text-gray-900">${report.sample_name || '未知'}</span></p>
                                            <p class="mb-3"><span class="text-gray-600 font-medium">ISBN号:</span> <span class="text-gray-900">${report.isbn || '未知'}</span></p>
                                            <p class="mb-3"><span class="text-gray-600 font-medium">编者:</span> <span class="text-gray-900">${report.author || '未知'}</span></p>
                                        </div>
                                    </div>
                                    
                                    <div class="bg-white rounded-lg border shadow-sm overflow-hidden mb-6">
                                        <div class="bg-gray-50 px-4 py-3 border-b">
                                            <h4 class="font-medium text-gray-700">报备信息</h4>
                                                </div>
                                        <div class="p-4">
                                            <p class="mb-3"><span class="text-gray-600 font-medium">报备人单位:</span> <span class="text-gray-900">${report.dealer_company_name || '未知单位'}</span></p>
                                            <p class="mb-3"><span class="text-gray-600 font-medium">报备院校:</span> <span class="text-gray-900">${report.school_name}</span></p>
                                            <p class="mb-3"><span class="text-gray-600 font-medium">报备时间:</span> <span class="text-gray-900">${report.created_at}</span></p>
                                            ${report.processed_by ? `<p class="mb-3"><span class="text-gray-600 font-medium">处理人:</span> <span class="text-gray-900">${report.processed_by}</span></p>` : ''}
                                                </div>
                                                </div>
                                    
                                    ${(report.conflict_reason || report.attachment) ? `
                                    <div class="bg-white rounded-lg border border-orange-200 shadow-sm overflow-hidden mb-6">
                                        <div class="bg-orange-50 px-4 py-3 border-b border-orange-200">
                                            <h4 class="font-medium text-orange-700">冲突处理信息</h4>
                                                </div>
                                        <div class="p-4">
                                    ${report.conflict_reason ? `
                                            <div class="mb-3">
                                                <p class="text-gray-600 font-medium mb-1">冲突处理理由:</p>
                                                <p class="bg-orange-50 p-3 rounded text-gray-700">${report.conflict_reason}</p>
                                                </div>
                                    ` : ''}
                                    
                                            ${report.attachment ? `
                                    <div>
                                                <p class="text-gray-600 font-medium mb-1">附件:</p>
                                                <a href="${report.attachment}" target="_blank" class="inline-flex items-center px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                                                    <i class="fas fa-download mr-2"></i>下载附件
                                                </a>
                                            </div>
                                            ` : ''}
                                        </div>
                                    </div>
                                    ` : ''}
                                    
                                    ${report.status === 'rejected' && report.reason ? `
                                    <div class="bg-white rounded-lg border border-red-200 shadow-sm overflow-hidden mb-6">
                                        <div class="bg-red-50 px-4 py-3 border-b border-red-200">
                                            <h4 class="font-medium text-red-700">拒绝原因</h4>
                                                </div>
                                        <div class="p-4">
                                            <p class="bg-red-50 p-3 rounded text-red-700">${report.reason}</p>
                                        </div>
                                    </div>
                                    ` : ''}
                                    
                                    ${report.status === 'pending' ? `
                                    <div class="flex justify-end space-x-3 mt-4">
                                        <button class="detail-approve-btn px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors shadow-sm flex items-center" data-id="${report.id}">
                                            <i class="fas fa-check mr-2"></i>通过
                                        </button>
                                        <button class="detail-reject-btn px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors shadow-sm flex items-center" data-id="${report.id}">
                                            <i class="fas fa-times mr-2"></i>拒绝
                                        </button>
                                    </div>` : ''}
                                                </div>
                            `;
                            
                            // 检查是否从冲突报备查看详情
                            const isFromConflict = Boolean($('#conflict-modal-container:visible').length);
                            
                            if (isFromConflict) {
                                // 创建新的嵌套模态框
                                if ($('#nested-modal-container').length == 0) {
                                    $('body').append(`
                                        <div id="nested-modal-container" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-[60]">
                                            <div class="bg-white rounded-lg shadow-lg w-full max-w-md">
                                                <div class="flex justify-between items-center border-b p-4">
                                                    <h3 id="nested-modal-title" class="text-lg font-medium">样书报备详情</h3>
                                                    <button id="close-nested-modal-btn" class="text-gray-500 hover:text-gray-700">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                <div id="nested-modal-body" class="p-4 max-h-[80vh] overflow-y-auto">
                                                    <!-- 嵌套模态框内容 -->
                                                </div>
                                            </div>
                                        </div>
                                    `);
                                    
                                    // 绑定关闭嵌套模态框事件
                                    $('#close-nested-modal-btn').click(function() {
                                        $('#nested-modal-container').addClass('hidden');
                                        
                                        // 刷新冲突列表 - 需要重新获取报备详情以更新冲突信息
                                        if (window.currentConflictData && window.currentConflictData.reportId) {
                                            $.ajax({
                                                url: '/api/publisher/get_report_detail',
                                                type: 'GET',
                                                data: { id: window.currentConflictData.reportId },
                                                success: function(response) {
                                                    if (response.code === 0 && response.data && response.data.conflict_reports) {
                                                        // 更新冲突数据并刷新列表
                                                        viewConflictReports(response.data.conflict_reports, window.currentConflictData.reportId);
                                                    }
                                                }
                                            });
                                        }
                                    });
                                }
                                
                                // 显示嵌套模态框
                                $('#nested-modal-body').html(detailHtml);
                                $('#nested-modal-container').removeClass('hidden');
                                
                                // 绑定详情页面中的通过/拒绝按钮事件
                                if (report.status === 'pending') {
                                    $('#nested-modal-body .detail-approve-btn').click(function() {
                                        const reportId = $(this).data('id');
                                        approveReport(reportId);
                                        $('#nested-modal-container').addClass('hidden');
                                        
                                        // 刷新冲突列表
                                        if (window.currentConflictData && window.currentConflictData.reportId) {
                                            setTimeout(function() {
                                                $.ajax({
                                                    url: '/api/publisher/get_report_detail',
                                                    type: 'GET',
                                                    data: { id: window.currentConflictData.reportId },
                                                    success: function(response) {
                                                        if (response.code === 0 && response.data && response.data.conflict_reports) {
                                                            // 更新冲突数据并刷新列表
                                                            viewConflictReports(response.data.conflict_reports, window.currentConflictData.reportId);
                                                        }
                                                    }
                                                });
                                            }, 500);
                                        }
                                    });
                                    
                                    $('#nested-modal-body .detail-reject-btn').click(function() {
                                        const reportId = $(this).data('id');
                                        
                                        // 隐藏嵌套模态框，但不关闭
                                        $('#nested-modal-container').addClass('hidden');
                                        
                                        // 创建拒绝模态框，z-index比冲突模态框高
                                        if ($('#reject-modal-container').length == 0) {
                                            $('body').append(`
                                                <div id="reject-modal-container" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-[70]">
                                                    <div class="bg-white rounded-lg shadow-lg w-full max-w-md">
                                                        <div class="flex justify-between items-center border-b p-4">
                                                            <h3 id="reject-modal-title" class="text-lg font-medium">拒绝报备申请</h3>
                                                            <button id="close-reject-modal-btn" class="text-gray-500 hover:text-gray-700">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                        </div>
                                                        <div id="reject-modal-body" class="p-4 max-h-[80vh] overflow-y-auto">
                                                            <form id="reject-nested-form">
                                                                <div class="mb-4">
                                                                    <label class="block text-gray-700 text-sm font-bold mb-2" for="nested-reason">
                                                                        拒绝原因
                                                                    </label>
                                                                    <textarea id="nested-reason" name="reason" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" rows="4" placeholder="请输入拒绝原因..."></textarea>
                                    </div>
                                                                <div class="flex justify-end">
                                                                    <button type="button" id="cancel-nested-reject-btn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 mr-2">
                                                                        取消
                                                                    </button>
                                                                    <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600">
                                                                        确认拒绝
                                                                    </button>
                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            `);
                                            
                                            // 绑定关闭拒绝模态框事件
                                            $('#close-reject-modal-btn, #cancel-nested-reject-btn').click(function() {
                                                $('#reject-modal-container').addClass('hidden');
                                                // 恢复嵌套模态框
                                                $('#nested-modal-container').removeClass('hidden');
                                            });
                                            
                                            // 绑定拒绝表单提交事件
                                            $('#reject-nested-form').submit(function(e) {
                                                e.preventDefault();
                                                
                                                const reason = $('#nested-reason').val().trim();
                                                
                                                $.ajax({
                                                    url: '/api/publisher/reject_report',
                                                    type: 'POST',
                                                    data: { 
                                                        id: reportId,
                                                        reason: reason
                                                    },
                                                    success: function(response) {
                                                        if (response.code === 0) {
                                                            showToast('报备申请已拒绝', 'success');
                                                            $('#reject-modal-container').addClass('hidden');
                                                            $('#nested-modal-container').addClass('hidden');
                                                            
                                                            // 刷新冲突列表
                                                            if (window.currentConflictData && window.currentConflictData.reportId) {
                                                                setTimeout(function() {
                                                                    $.ajax({
                                                                        url: '/api/publisher/get_report_detail',
                                                                        type: 'GET',
                                                                        data: { id: window.currentConflictData.reportId },
                                                                        success: function(response) {
                                                                            if (response.code === 0 && response.data && response.data.conflict_reports) {
                                                                                // 更新冲突数据并刷新列表
                                                                                viewConflictReports(response.data.conflict_reports, window.currentConflictData.reportId);
                                                                            }
                                                                        }
                                                                    });
                                                                }, 500);
                                                            }
                        } else {
                                                            showToast(response.message, 'error');
                        }
                    },
                    error: function() {
                                                        showToast('网络错误，请稍后重试', 'error');
                                                    }
                                                });
                                            });
                                        }
                                        
                                        // 显示拒绝模态框
                                        $('#reject-modal-container').removeClass('hidden');
                                    });
                                }
                            } else {
                                // 显示普通模态框
                                modalBody.html(detailHtml);
                                modalContainer.removeClass('hidden');
                                
                                // 绑定详情页面中的通过/拒绝按钮事件
                                if (report.status === 'pending') {
                                    $('.detail-approve-btn').click(function() {
                                        const reportId = $(this).data('id');
                                        approveReport(reportId);
                                        closeModal();
                                    });
                                    
                                    $('.detail-reject-btn').click(function() {
                                        const reportId = $(this).data('id');
                                        showRejectModal(reportId);
                                        closeModal();
                                    });
                                }
                            }
                        } else {
                            alert('获取报备详情失败：' + errorMsg);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX错误:", {xhr: xhr, status: status, error: error});
                        alert('网络错误，请稍后再试');
                    }
                });
            }
            
            // 获取状态文本
            function getStatusText(status) {
                if (!status) return '未知状态';
                
                const statusMap = {
                    'pending': '待处理',
                    'approved': '已通过',
                    'rejected': '已拒绝'
                };
                return statusMap[status] || status;
            }

            // 显示模态框
            function showModal(content) {
                // 创建模态框
                const modal = $(`
                    <div class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
                        <div class="bg-white rounded-lg shadow-lg w-4/5 max-w-4xl max-h-3/4 overflow-auto">
                            ${content}
                            <div class="p-4 border-t border-gray-200 flex justify-end">
                                <button class="modal-close-btn px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                                    关闭
                                    </button>
                                </div>
                            </div>
                        </div>
                `);
                
                // 添加到文档中
                $('body').append(modal);
                
                // 绑定关闭事件
                modal.find('.modal-close-btn').on('click', function() {
                    modal.remove();
                });
            }

            // 显示加载状态
            function showLoading(message = '加载中...') {
                if ($('#loading-overlay').length === 0) {
                    const loading = $(`
                        <div id="loading-overlay" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
                            <div class="bg-white p-4 rounded-lg shadow-lg flex items-center">
                                <div class="spinner mr-3"></div>
                                <p id="loading-message">${message}</p>
                            </div>
                        </div>
                    `);
                    $('body').append(loading);
                } else {
                    $('#loading-message').text(message);
                    $('#loading-overlay').removeClass('hidden');
                }
            }

            // 隐藏加载状态
            function hideLoading() {
                $('#loading-overlay').remove();
            }

            // 显示提示
            function showToast(message, type = 'info') {
                const iconMap = {
                    'success': '<i class="fas fa-check-circle mr-2"></i>',
                    'error': '<i class="fas fa-times-circle mr-2"></i>',
                    'warning': '<i class="fas fa-exclamation-triangle mr-2"></i>',
                    'info': '<i class="fas fa-info-circle mr-2"></i>'
                };
                
                const colorMap = {
                    'success': 'bg-green-500',
                    'error': 'bg-red-500',
                    'warning': 'bg-yellow-500',
                    'info': 'bg-blue-500'
                };
                
                const toast = $(`
                    <div class="fixed top-4 right-4 z-[9999] ${colorMap[type]} text-white px-4 py-2 rounded shadow-lg flex items-center">
                        ${iconMap[type]}
                        <span>${message}</span>
                                    </div>
                `);
                
                $('body').append(toast);
                
                // 3秒后自动消失
                setTimeout(function() {
                    toast.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            }

            // 查看冲突详情
            function showConflictDetail(reportId) {
                // 显示加载状态
                showLoading('加载冲突详情...');
                
                // AJAX请求获取冲突报备详情
                $.ajax({
                    url: `/publisher/conflict_detail/${reportId}`,
                    type: 'GET',
                    success: function(response) {
                        hideLoading();
                        if (response.success) {
                            displayConflictDetail(response.report, response.conflict_reports);
                        } else {
                            showToast(response.message || '获取冲突详情失败', 'error');
                        }
                    },
                    error: function(error) {
                        hideLoading();
                        console.error('获取冲突详情失败:', error);
                        showToast('获取冲突详情失败，请检查网络连接后重试', 'error');
                    }
                });
            }
            
            // 显示冲突详情模态框
            function displayConflictDetail(report, conflictReports) {
                // 构建冲突详情HTML
                let conflictHtml = '';
                
                conflictReports.forEach(conflict => {
                    conflictHtml += `
                        <div class="border-b border-gray-200 py-3">
                            <div class="grid grid-cols-2 gap-2">
                        <div>
                                    <p class="text-gray-600">经销商:</p>
                                    <p class="font-semibold">${conflict.dealer_name}</p>
                                </div>
                                    <div>
                                    <p class="text-gray-600">联系电话:</p>
                                    <p class="font-semibold">${conflict.dealer_phone}</p>
                                    </div>
                                    <div>
                                    <p class="text-gray-600">单位:</p>
                                    <p class="font-semibold">${conflict.dealer_company_name || '无'}</p>
                                    </div>
                                    <div>
                                    <p class="text-gray-600">申请时间:</p>
                                    <p class="font-semibold">${conflict.created_at}</p>
                                    </div>
                                    <div>
                                    <p class="text-gray-600">状态:</p>
                                    <p class="font-semibold">${getStatusText(conflict.status)}</p>
                                    </div>
                                </div>
                            <div class="mt-2">
                                <p class="text-gray-600">冲突原因:</p>
                                <p class="font-semibold text-red-600">${conflict.conflict_reason}</p>
                            </div>
                        </div>
                    `;
                });
                
                const modalContent = `
                    <div class="p-6">
                        <h2 class="text-xl font-bold mb-4">冲突详情</h2>
                        <div class="mb-4 border-b border-gray-300 pb-4">
                            <h3 class="text-lg font-semibold mb-2">当前报备</h3>
                            <div class="grid grid-cols-2 gap-2">
                        <div>
                                    <p class="text-gray-600">教材名称:</p>
                                    <p class="font-semibold">${report.sample_name}</p>
                                    </div>
                                    <div>
                                    <p class="text-gray-600">学校:</p>
                                    <p class="font-semibold">${report.school_name}</p>
                                    </div>
                                    <div>
                                    <p class="text-gray-600">经销商:</p>
                                    <p class="font-semibold">${report.dealer_name}</p>
                                    </div>
                                    <div>
                                    <p class="text-gray-600">联系电话:</p>
                                    <p class="font-semibold">${report.dealer_phone}</p>
                                    </div>
                                    </div>
                                </div>
                        <h3 class="text-lg font-semibold mb-2">冲突报备列表</h3>
                        <div class="max-h-96 overflow-y-auto">
                            ${conflictHtml}
                            </div>
                                        </div>
                                    `;
                
                // 显示模态框
                showModal(modalContent);
            }

            // 批量操作按钮的事件处理
            $('#batchApproveBtn').click(function() {
                const selectedReports = getSelectedReports();
                if (selectedReports.length === 0) {
                    alert('请选择至少一个报备');
                    return;
                }
                
                if (confirm(`确定要批量通过选中的 ${selectedReports.length} 个报备申请吗？`)) {
                    batchApproveReports(selectedReports);
                }
            });
            
            $('#batchRejectBtn').click(function() {
                const selectedReports = getSelectedReports();
                if (selectedReports.length === 0) {
                    alert('请选择至少一个报备');
                        return;
                    }
                    
                if (confirm(`确定要批量拒绝选中的 ${selectedReports.length} 个报备申请吗？`)) {
                    batchRejectReports(selectedReports);
                }
            });
            
            // 获取选中的报备ID
            function getSelectedReports() {
                const selectedCheckboxes = $('.report-checkbox:checked');
                return selectedCheckboxes.map(function() {
                    return $(this).data('report-id');
                }).get();
            }
            
            // 批量通过报备
            function batchApproveReports(reportIds) {
                    $.ajax({
                    url: '/api/publisher/batch_process_reports',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ 
                        report_ids: reportIds,
                        action: 'approve'
                    }),
                        success: function(response) {
                        if (response.success) {
                            alert('批量通过成功');
                            loadPendingReports(); // 重新加载待处理报备
                            } else {
                            alert('批量通过失败：' + response.message);
                            }
                        },
                        error: function() {
                        alert('网络错误，请稍后再试');
                        }
                });
            }
            
            // 批量拒绝报备
            function batchRejectReports(reportIds) {
                $.ajax({
                    url: '/api/publisher/batch_process_reports',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ 
                        report_ids: reportIds,
                        action: 'reject'
                    }),
                    success: function(response) {
                        if (response.success) {
                            alert('批量拒绝成功');
                            loadPendingReports(); // 重新加载待处理报备
                        } else {
                            alert('批量拒绝失败：' + response.message);
                        }
                    },
                    error: function() {
                        alert('网络错误，请稍后再试');
                    }
                });
            }

            // 显示批量拒绝模态框
            function showBatchRejectModal(reportIds) {
                modalTitle.text('批量拒绝报备申请');
                            modalBody.html(`
                    <form id="batchRejectForm">
                                    <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="reason">
                                拒绝原因
                                        </label>
                            <textarea id="reason" name="reason" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" rows="4" placeholder="请输入拒绝原因..."></textarea>
                                    </div>
                                    <div class="mb-4">
                        <p class="text-sm text-gray-500">将拒绝 ${reportIds.length} 个报备申请</p>
                                    </div>
                                    <div class="flex justify-end">
                            <button type="button" id="cancelBatchRejectBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 mr-2">
                                            取消
                                        </button>
                            <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600">
                                确认拒绝
                                        </button>
                                    </div>
                                </form>
                            `);
                            
                            modalContainer.removeClass('hidden');
                            
                            // 取消按钮
                $('#cancelBatchRejectBtn').click(closeModal);
                            
                            // 提交表单
                $('#batchRejectForm').submit(function(e) {
                                e.preventDefault();
                                
                    const reason = $('#reason').val().trim();
                                
                                $.ajax({
                        url: '/api/publisher/batch_process_reports',
                                    type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ 
                            report_ids: reportIds,
                            action: 'reject',
                            reason: reason
                        }),
                                    success: function(response) {
                            if (response.success) {
                                alert('报备申请已批量拒绝');
                                            closeModal();
                                loadPendingReports();
                        } else {
                                alert('批量拒绝失败：' + response.message);
                        }
                    },
                    error: function() {
                            alert('网络错误，请稍后再试');
                                    }
                                });
                            });
            }
            
            // 显示冲突报备列表
            function viewConflictReports(conflictReports, reportId) {
                // 修改模态框ID，用于区分冲突模态框和普通模态框
                if ($('#conflict-modal-container').length == 0) {
                    $('body').append(`
                        <div id="conflict-modal-container" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
                            <div class="bg-white rounded-lg shadow-lg w-full max-w-md">
                                <div class="flex justify-between items-center border-b p-4">
                                    <h3 id="conflict-modal-title" class="text-lg font-medium">冲突报备详情</h3>
                                    <button id="close-conflict-modal-btn" class="text-gray-500 hover:text-gray-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div id="conflict-modal-body" class="p-4 max-h-[80vh] overflow-y-auto">
                                    <!-- 冲突模态框内容 -->
                                </div>
                            </div>
                        </div>
                    `);
                    
                    // 绑定关闭冲突模态框事件
                    $('#close-conflict-modal-btn').click(function() {
                        $('#conflict-modal-container').addClass('hidden');
                        
                        // 如果有嵌套的模态框，也一并关闭
                        $('#nested-modal-container').addClass('hidden');
                        $('#reject-modal-container').addClass('hidden');
                        
                        // 清除冲突数据
                        window.currentConflictData = null;
                    });
                }
                
                // 设置模态框标题
                $('#conflict-modal-title').text('冲突报备详情');
                
                // 保存冲突数据供后续使用
                window.currentConflictData = {
                    conflictReports: conflictReports,
                    reportId: reportId
                };
                
                // 构建冲突报备列表
                let content = `
                    <div class="p-4">
                        <div class="p-2 mb-4 bg-red-100 text-red-700 rounded-lg">
                            <p class="font-bold"><i class="fas fa-exclamation-circle mr-2"></i>有${conflictReports.length}个冲突报备</p>
                            <p class="text-sm mt-1">这些报备与当前申请使用了相同的样书和学校名称，请仔细审核</p>
                        </div>
                        <div class="space-y-4">
                `;
                
                conflictReports.forEach(report => {
                    const statusMap = {
                        'pending': { text: '待处理', class: 'bg-yellow-100 text-yellow-800' },
                        'approved': { text: '已通过', class: 'bg-green-100 text-green-800' },
                        'rejected': { text: '已拒绝', class: 'bg-red-100 text-red-800' }
                    };
                    
                    const status = statusMap[report.status] || { text: report.status || '未知', class: 'bg-gray-100 text-gray-800' };
                    
                    content += `
                        <div class="border rounded-lg overflow-hidden">
                            <div class="bg-gray-50 p-3 flex justify-between items-center border-b">
                                <span class="font-medium">${report.dealer_company_name || '未知单位'}</span>
                                <span class="px-2 py-1 rounded text-xs ${status.class}">${status.text}</span>
                            </div>
                            <div class="p-3">
                                <p class="mb-2"><span class="text-gray-600">报备时间:</span> ${report.created_at || '未知'}</p>
                                <p class="mb-2"><span class="text-gray-600">报备院校:</span> ${report.school_name || '未知'}</p>
                                <p class="mb-2"><span class="text-gray-600">报备样书:</span> ${report.sample_name || '未知'}</p>
                                <div class="mt-2 text-right">
                                    <button class="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 view-conflict-detail-btn" data-id="${report.id}">
                                        <i class="fas fa-eye mr-1"></i>查看详情
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                content += `
                        </div>
                        <div class="mt-4 text-right">
                            <button id="closeConflictBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                关闭
                            </button>
                        </div>
                    </div>
                `;
                
                // 显示冲突模态框
                $('#conflict-modal-body').html(content);
                $('#conflict-modal-container').removeClass('hidden');
                
                // 关闭按钮
                $('#closeConflictBtn').click(function() {
                    $('#conflict-modal-container').addClass('hidden');
                    // 如果有嵌套的模态框，也一并关闭
                    $('#nested-modal-container').addClass('hidden');
                    $('#reject-modal-container').addClass('hidden');
                    
                    // 清除冲突数据
                    window.currentConflictData = null;
                });
                
                // 绑定冲突详情按钮事件
                $('.view-conflict-detail-btn').click(function() {
                    const conflictId = $(this).data('id');
                    viewReportDetail(conflictId);
                });
            }
            
            // 显示简单的冲突原因对话框
            function showSimpleConflictDialog(conflictReason, reportId) {
                // 获取冲突报备详情
                $.ajax({
                    url: '/api/publisher/get_report_detail',
                    type: 'GET',
                    data: { id: reportId },
                    success: function(response) {
                        if (response.code === 0) {
                            const report = response.data;
                            
                            // 设置模态框标题
                            modalTitle.text('冲突信息');
                            
                            // 构建详细的冲突信息内容
                            let content = `
                                <div class="p-4">
                                    <div class="p-3 mb-4 bg-red-100 text-red-700 rounded-lg border-l-4 border-red-500">
                                        <p class="font-bold"><i class="fas fa-exclamation-circle mr-2"></i>报备存在冲突</p>
                                    </div>
                                    
                                    <div class="bg-white rounded-lg border shadow-sm overflow-hidden mb-4">
                                        <div class="bg-gray-50 px-4 py-3 border-b">
                                            <h4 class="font-medium text-gray-700">冲突处理理由</h4>
                                        </div>
                                        <div class="p-4">
                                            <p class="bg-gray-50 p-3 rounded text-gray-700">${conflictReason || '未提供冲突处理理由'}</p>
                                        </div>
                                    </div>`;
                            
                            // 如果有附件，添加附件下载区域
                            if (report.attachment) {
                                content += `
                                    <div class="bg-white rounded-lg border shadow-sm overflow-hidden mb-4">
                                        <div class="bg-gray-50 px-4 py-3 border-b">
                                            <h4 class="font-medium text-gray-700">附件</h4>
                                        </div>
                                        <div class="p-4">
                                            <a href="${report.attachment}" target="_blank" class="inline-flex items-center px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                                                <i class="fas fa-download mr-2"></i>下载附件
                                            </a>
                                        </div>
                                    </div>`;
                            }
                            
                            // 如果有冲突报备列表，添加冲突报备列表区域
                            if (report.conflict_reports && report.conflict_reports.length > 0) {
                                content += `
                                    <div class="bg-white rounded-lg border shadow-sm overflow-hidden mb-4">
                                        <div class="bg-gray-50 px-4 py-3 border-b">
                                            <h4 class="font-medium text-gray-700">冲突报备列表</h4>
                                        </div>
                                        <div class="p-4">
                                            <div class="space-y-3">`;
                                
                                // 遍历冲突报备列表
                                report.conflict_reports.forEach(conflict => {
                                    const statusMap = {
                                        'pending': { text: '待处理', class: 'bg-yellow-100 text-yellow-800' },
                                        'approved': { text: '已通过', class: 'bg-green-100 text-green-800' },
                                        'rejected': { text: '已拒绝', class: 'bg-red-100 text-red-800' }
                                    };
                                    
                                    const status = statusMap[conflict.status] || { text: conflict.status || '未知', class: 'bg-gray-100 text-gray-800' };
                                    
                                    content += `
                                        <div class="border rounded-lg overflow-hidden">
                                            <div class="bg-gray-50 p-3 flex justify-between items-center border-b">
                                                <span class="font-medium">${conflict.dealer_company_name || '未知单位'}</span>
                                                <span class="px-2 py-1 rounded text-xs ${status.class}">${status.text}</span>
                                            </div>
                                            <div class="p-3">
                                                <p class="mb-2"><span class="text-gray-600">报备时间:</span> ${conflict.created_at || '未知'}</p>
                                                <p class="mb-2"><span class="text-gray-600">报备院校:</span> ${conflict.school_name || '未知'}</p>
                                                <p class="mb-2"><span class="text-gray-600">报备样书:</span> ${conflict.sample_name || '未知'}</p>
                                                <div class="mt-2 text-right">
                                                    <button class="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 view-conflict-detail-btn" data-id="${conflict.id}">
                                                        <i class="fas fa-eye mr-1"></i>查看详情
                                                    </button>
                                                </div>
                                            </div>
                                        </div>`;
                                });
                                
                                content += `
                                            </div>
                                        </div>
                                    </div>`;
                            }
                            
                            // 添加关闭按钮
                            content += `
                                <div class="flex justify-end">
                                    <button id="closeSimpleConflictBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                        关闭
                                    </button>
                                </div>
                            </div>`;
                            
                            // 显示模态框
                            modalBody.html(content);
                            modalContainer.removeClass('hidden');
                            
                            // 绑定关闭按钮事件
                            $('#closeSimpleConflictBtn').click(closeModal);
                            
                            // 绑定冲突报备详情查看按钮事件
                            $('.view-conflict-detail-btn').click(function() {
                                const conflictId = $(this).data('id');
                                viewReportDetail(conflictId);
                            });
                        } else {
                            // 如果获取详情失败，显示简单的错误信息
                            showSimpleConflictErrorDialog(conflictReason);
                        }
                    },
                    error: function() {
                        // 如果网络错误，显示简单的错误信息
                        showSimpleConflictErrorDialog(conflictReason);
                    }
                });
            }
            
            // 显示简单的冲突错误对话框（作为后备）
            function showSimpleConflictErrorDialog(conflictReason) {
                // 设置模态框标题
                modalTitle.text('冲突信息');
                
                // 构建简单对话框内容
                let content = `
                    <div class="p-4">
                        <div class="p-3 mb-4 bg-red-100 text-red-700 rounded-lg border-l-4 border-red-500">
                            <p class="font-bold"><i class="fas fa-exclamation-circle mr-2"></i>报备存在冲突</p>
                        </div>
                        
                        <div class="bg-white rounded-lg border shadow-sm overflow-hidden mb-4">
                            <div class="bg-gray-50 px-4 py-3 border-b">
                                <h4 class="font-medium text-gray-700">冲突处理理由</h4>
                            </div>
                            <div class="p-4">
                                <p class="bg-gray-50 p-3 rounded text-gray-700">${conflictReason || '未提供冲突处理理由'}</p>
                            </div>
                        </div>
                        
                        <div class="flex justify-end">
                            <button id="closeSimpleConflictBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                                关闭
                            </button>
                        </div>
                    </div>
                `;
                
                // 显示模态框
                modalBody.html(content);
                modalContainer.removeClass('hidden');
                
                // 绑定关闭按钮事件
                $('#closeSimpleConflictBtn').click(closeModal);
            }
            
            // 初始加载
            loadPendingReports();
        });
    </script>
</body>
</html>