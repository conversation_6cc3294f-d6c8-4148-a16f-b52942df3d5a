from flask import Blueprint, jsonify, session, request
from app.config import get_db_connection
import os
import uuid
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta
import json
import pymysql

dealer_bp = Blueprint('dealer', __name__)

UPLOAD_FOLDER = 'app/static/upload'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'doc', 'docx'}

# 获取样书详情
@dealer_bp.route('/get_sample_detail', methods=['GET'])
def get_sample_detail():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    sample_id = request.args.get('sample_id')
    if not sample_id:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info, 
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.discount_info, sb.attachment_link,
                       sb.color_system, sb.sample_download_url, sb.online_reading_url,
                       sb.courseware, sb.table_of_contents, sb.resources, sb.resource_download_url,
                       sb.publisher_name, d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ') 
                        FROM sample_book_features sbf 
                        JOIN book_features bf ON sbf.feature_id = bf.id 
                        WHERE sbf.sample_id = sb.id) as feature_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE sb.id = %s
            """
            cursor.execute(sql, (sample_id,))
            sample = cursor.fetchone()
            
            if not sample:
                return jsonify({"code": 1, "message": "样书不存在"})

            # 格式化日期字段
            if sample.get('publication_date'):
                sample['publication_date'] = sample['publication_date'].strftime('%Y-%m-%d')

            return jsonify({"code": 0, "data": sample})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书详情失败: {str(e)}"})
    finally:
        connection.close()

# 获取经销商信息
@dealer_bp.route('/get_dealer_info', methods=['GET'])
def get_dealer_info():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    user_id = session.get('user_id')
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT name, phone_number FROM dealers WHERE user_id = %s
            """
            cursor.execute(sql, (user_id,))
            dealer = cursor.fetchone()
            
            if not dealer:
                return jsonify({"code": 1, "message": "经销商信息不存在"})
            
            # 如果dealers表中没有记录，使用users表中的信息
            if not dealer.get('dealer_name'):
                dealer['dealer_name'] = dealer.get('name')
            if not dealer.get('dealer_phone'):
                dealer['dealer_phone'] = dealer.get('phone_number')
            
            return jsonify({"code": 0, "data": dealer})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取经销商信息失败: {str(e)}"})
    finally:
        connection.close()

# 获取当前用户ID
@dealer_bp.route('/get_current_user', methods=['GET'])
def get_current_user():
    """获取当前登录用户ID"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        return jsonify({'success': True, 'user_id': user_id})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 获取学校列表
@dealer_bp.route('/get_schools', methods=['GET'])
def get_schools():
    search = request.args.get('search', '')
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = "SELECT id, name FROM schools"
            if search:
                sql += " WHERE name LIKE %s"
                cursor.execute(sql, (f'%{search}%',))
            else:
                cursor.execute(sql)
            
            schools = cursor.fetchall()
            return jsonify({"code": 0, "data": schools})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取学校列表失败: {str(e)}"})
    finally:
        connection.close()

# 获取经销商地址列表
@dealer_bp.route('/get_addresses', methods=['GET'])
def get_addresses():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    user_id = session.get('user_id')
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT address_id, name, phone_number, address
                FROM shipping_addresses
                WHERE teacher_id = %s
                ORDER BY address_id DESC
            """
            cursor.execute(sql, (user_id,))
            addresses = cursor.fetchall()
            
            return jsonify({"code": 0, "data": addresses})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取地址列表失败: {str(e)}"})
    finally:
        connection.close()

# 添加新地址
@dealer_bp.route('/add_address', methods=['POST'])
def add_address():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    user_id = session.get('user_id')
    name = request.form.get('name')
    phone_number = request.form.get('phone_number')
    address = request.form.get('address')
    
    if not all([name, phone_number, address]):
        return jsonify({"code": 1, "message": "请填写完整信息"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                INSERT INTO shipping_addresses (teacher_id, name, phone_number, address)
                VALUES (%s, %s, %s, %s)
            """
            cursor.execute(sql, (user_id, name, phone_number, address))
            connection.commit()
            
            return jsonify({"code": 0, "message": "地址添加成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"添加地址失败: {str(e)}"})
    finally:
        connection.close()

# 提交报备申请
@dealer_bp.route('/submit_report', methods=['POST'])
def submit_report():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    user_id = session.get('user_id')
    
    # 检查请求内容类型
    if request.is_json:
        # 处理 JSON 数据
        data = request.get_json()
        sample_ids = data.get('sample_ids', [])
        school_id = data.get('school_id')
        dealer_name = data.get('dealer_name')
        dealer_phone = data.get('dealer_phone')
    else:
        # 处理表单数据
        sample_ids = request.form.getlist('sample_ids')
        school_id = request.form.get('school_id')
        dealer_name = request.form.get('dealer_name')
        dealer_phone = request.form.get('dealer_phone')
    
    if not sample_ids:
        return jsonify({"code": 1, "message": "请选择要报备的样书"})
    
    if not school_id:
        return jsonify({"code": 1, "message": "请选择推广院校"})
    
    connection = get_db_connection()
    try:
        # 获取学校名称
        with connection.cursor() as cursor:
            sql = "SELECT name FROM schools WHERE id = %s"
            cursor.execute(sql, (school_id,))
            school = cursor.fetchone()
            
            if not school:
                return jsonify({"code": 1, "message": "所选学校不存在"})
            
            school_name = school['name']
        
        # 检查是否有冲突或重复的报备
        success_count = 0
        failed_count = 0
        conflict_samples = []
        duplicate_samples = []
        
        for sample_id in sample_ids:
            with connection.cursor() as cursor:
                # 检查是否已经有该样书在该学校的有效报备（其他经销商的）
                check_conflict_sql = """
                    SELECT pr.id, u.name as dealer_name
                    FROM promotion_reports pr
                    JOIN users u ON pr.dealer_id = u.user_id
                    WHERE pr.sample_book_id = %s 
                    AND pr.school_name = %s 
                    AND pr.status IN ('pending', 'approved')
                    AND pr.dealer_id != %s
                """
                cursor.execute(check_conflict_sql, (sample_id, school_name, user_id))
                existing_conflict = cursor.fetchone()
                
                # 检查是否已经有该样书在该学校的有效报备（当前经销商的）
                check_duplicate_sql = """
                    SELECT pr.id
                    FROM promotion_reports pr
                    WHERE pr.sample_book_id = %s 
                    AND pr.school_name = %s 
                    AND pr.status IN ('pending', 'approved')
                    AND pr.dealer_id = %s
                """
                cursor.execute(check_duplicate_sql, (sample_id, school_name, user_id))
                existing_duplicate = cursor.fetchone()
                
                if existing_conflict:
                    # 存在冲突
                    failed_count += 1
                    
                    # 获取样书信息
                    cursor.execute("SELECT name FROM sample_books WHERE id = %s", (sample_id,))
                    sample = cursor.fetchone()
                    
                    conflict_samples.append({
                        "sample_id": sample_id,
                        "sample_name": sample['name'] if sample else "未知样书",
                        # "dealer_name": existing_conflict['dealer_name'],
                        "report_id": existing_conflict['id']
                    })
                elif existing_duplicate:
                    # 存在重复
                    failed_count += 1
                    
                    # 获取样书信息
                    cursor.execute("SELECT name FROM sample_books WHERE id = %s", (sample_id,))
                    sample = cursor.fetchone()
                    
                    duplicate_samples.append({
                        "sample_id": sample_id,
                        "sample_name": sample['name'] if sample else "未知样书",
                        "report_id": existing_duplicate['id']
                    })
                else:
                    # 无冲突也无重复，创建报备
                    insert_sql = """
                        INSERT INTO promotion_reports 
                        (dealer_id, sample_book_id, school_name, status)
                        VALUES (%s, %s, %s, 'pending')
                    """
                    cursor.execute(insert_sql, (user_id, sample_id, school_name))
                    success_count += 1
        
        # 更新经销商信息，如果经销商信息不存在，则创建
        if dealer_name and dealer_phone:
            with connection.cursor() as cursor:
                update_sql = """
                    INSERT INTO dealers (user_id, name, phone_number)
                    VALUES (%s, %s, %s)
                    ON DUPLICATE KEY UPDATE name = VALUES(name), phone_number = VALUES(phone_number)
                """
                cursor.execute(update_sql, (user_id, dealer_name, dealer_phone))
        
        connection.commit()
        
        if conflict_samples or duplicate_samples:
            message = []
            if conflict_samples:
                message.append(f"{len(conflict_samples)}本样书存在冲突")
            if duplicate_samples:
                message.append(f"{len(duplicate_samples)}本样书已报备过")
            if success_count > 0:
                message.append(f"{success_count}本样书报备成功")
            
            return jsonify({
                "code": 2,  # 部分成功
                "message": message,
                "success_count": success_count,
                "failed_count": failed_count,
                "conflicts": conflict_samples,
                "duplicates": duplicate_samples,
                "school_name": school_name
            })
        else:
            return jsonify({
                "code": 0,
                "message": "报备申请提交成功",
                "success_count": success_count
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"提交报备申请失败: {str(e)}"})
    finally:
        connection.close()

# 提交冲突处理申请
@dealer_bp.route('/submit_conflict_report', methods=['POST'])
def submit_conflict_report():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    user_id = session.get('user_id')
    
    # 获取表单数据
    sample_ids = request.form.getlist('sample_ids[]')
    school_id = request.form.get('school_id')
    conflict_reason = request.form.get('conflict_reason')
    dealer_name = request.form.get('dealer_name')
    dealer_phone = request.form.get('dealer_phone')
    
    if not all([sample_ids, school_id, conflict_reason]):
        return jsonify({"code": 1, "message": "请填写完整信息"})
    
    # 处理附件上传
    attachment_path = None
    if 'attachment' in request.files:
        file = request.files['attachment']
        if file and file.filename:
            filename = secure_filename(file.filename)
            # 生成唯一文件名
            unique_filename = f"{uuid.uuid4()}_{filename}"
            # 确保上传目录存在
            os.makedirs(UPLOAD_FOLDER, exist_ok=True)
            file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
            file.save(file_path)
            attachment_path = f"/static/upload/{unique_filename}"
    else:
        return jsonify({"code": 1, "message": "请上传证明材料"})
    
    connection = get_db_connection()
    try:
        # 获取学校名称
        with connection.cursor() as cursor:
            sql = "SELECT name FROM schools WHERE id = %s"
            cursor.execute(sql, (school_id,))
            school = cursor.fetchone()
            
            if not school:
                return jsonify({"code": 1, "message": "所选学校不存在"})
            
            school_name = school['name']
        
        # 更新经销商信息
        if dealer_name and dealer_phone:
            with connection.cursor() as cursor:
                update_sql = """
                    INSERT INTO dealers (user_id, name, phone_number)
                    VALUES (%s, %s, %s)
                    ON DUPLICATE KEY UPDATE name = VALUES(name), phone_number = VALUES(phone_number)
                """
                cursor.execute(update_sql, (user_id, dealer_name, dealer_phone))
        
        # 检查是否有重复的报备
        success_count = 0
        failed_count = 0
        duplicate_samples = []
        
        for sample_id in sample_ids:
            with connection.cursor() as cursor:
                # 检查是否已经有该样书在该学校的有效报备（当前经销商的）
                check_duplicate_sql = """
                    SELECT pr.id
                    FROM promotion_reports pr
                    WHERE pr.sample_book_id = %s 
                    AND pr.school_name = %s 
                    AND pr.status IN ('pending', 'approved')
                    AND pr.dealer_id = %s
                """
                cursor.execute(check_duplicate_sql, (sample_id, school_name, user_id))
                existing_duplicate = cursor.fetchone()
                
                if existing_duplicate:
                    # 存在重复
                    failed_count += 1
                    
                    # 获取样书信息
                    cursor.execute("SELECT name FROM sample_books WHERE id = %s", (sample_id,))
                    sample = cursor.fetchone()
                    
                    duplicate_samples.append({
                        "sample_id": sample_id,
                        "sample_name": sample['name'] if sample else "未知样书",
                        "report_id": existing_duplicate['id']
                    })
                else:
                    # 无重复，创建带有冲突理由的报备
                    insert_sql = """
                        INSERT INTO promotion_reports 
                        (dealer_id, sample_book_id, school_name, status, conflict_reason, attachment)
                        VALUES (%s, %s, %s, 'pending', %s, %s)
                    """
                    cursor.execute(insert_sql, (user_id, sample_id, school_name, conflict_reason, attachment_path))
                    success_count += 1
        
        connection.commit()
        
        if duplicate_samples:
            message = []
            message.append(f"{len(duplicate_samples)}本样书已报备过")
            if success_count > 0:
                message.append(f"{success_count}本样书冲突处理申请提交成功")
            
            return jsonify({
                "code": 2,  # 部分成功
                "message": message,
                "success_count": success_count,
                "failed_count": failed_count,
                "duplicates": duplicate_samples
            })
        else:
            return jsonify({
                "code": 0,
                "message": "冲突处理申请提交成功",
                "success_count": success_count
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"提交冲突处理申请失败: {str(e)}"})
    finally:
        connection.close()

# 获取经销商的报备列表（分页和搜索功能）
@dealer_bp.route('/get_reports', methods=['GET'])
def get_reports():
    """
    获取报备列表，按照出版社分组排序
    请求参数:
        page: 页码，默认1
        limit: 每页数量，默认10
        status: 状态筛选（all, pending, approved, rejected, completed)
        search: 搜索关键词
        publisher_sort_order: 出版社排序顺序（逗号分隔）
    返回:
        报备列表、总数、当前页、出版社排序顺序
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        status = request.args.get('status', 'all')
        search = request.args.get('search', '')
        publisher_sort_order = request.args.get('publisher_sort_order', '')
        
        # 解析出版社排序顺序
        publisher_order_list = []
        if publisher_sort_order:
            publisher_order_list = publisher_sort_order.split(',')
        
        # 验证页码和限制
        if page < 1:
            page = 1
        if limit < 1 or limit > 50:
            limit = 10
    
        offset = (page - 1) * limit
        
        # 获取当前用户ID
        user_id = session.get('user_id')
        
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 构建SQL查询条件
        where_clauses = ["pr.dealer_id = %s"]
        params = [user_id]
            
        if status != 'all':
            where_clauses.append("pr.status = %s")
            params.append(status)
        
        if search:
            where_clauses.append("(sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s OR pr.school_name LIKE %s)")
            search_param = f'%{search}%'
            params.extend([search_param, search_param, search_param, search_param])
            
        # 构建完整的SQL查询
        where_sql = " AND ".join(where_clauses)
            
        # 先获取所有满足条件的出版社及其报备数量
        publisher_query = f"""
        SELECT 
            COALESCE(sb.publisher_name, '未知出版社') as publisher_name, 
            COUNT(*) as report_count
        FROM promotion_reports pr
        JOIN sample_books sb ON pr.sample_book_id = sb.id
        WHERE {where_sql}
        GROUP BY sb.publisher_name
        ORDER BY sb.publisher_name
        """
        cursor.execute(publisher_query, params)
        publishers = cursor.fetchall()
        
        # 如果没有传入出版社排序顺序，或者排序顺序与当前的不匹配，重新生成
        current_publishers = [p['publisher_name'] or '未知出版社' for p in publishers]
        if not publisher_order_list or set(publisher_order_list) != set(current_publishers):
            publisher_order_list = current_publishers
        
        # 使用 FIELD 函数根据出版社排序顺序对结果进行排序
        # 如果出版社列表不为空
        order_clause = ""
        if publisher_order_list:
            # 确保所有排序值都不为None
            publisher_order_list = [p or '未知出版社' for p in publisher_order_list if p is not None]
            if publisher_order_list:
                placeholders = ', '.join(['%s'] * len(publisher_order_list))
                # 将出版社排序顺序添加到参数列表
                order_params = publisher_order_list.copy()
                order_clause = f"ORDER BY FIELD(COALESCE(sb.publisher_name, '未知出版社'), {placeholders}), pr.created_at DESC"
            else:
                order_params = []
                order_clause = "ORDER BY COALESCE(sb.publisher_name, '未知出版社'), pr.created_at DESC"
        else:
            order_params = []
            order_clause = "ORDER BY COALESCE(sb.publisher_name, '未知出版社'), pr.created_at DESC"
        
        # 查询报备列表，包含样书的费率信息
        query = f"""
        SELECT
            pr.id, pr.sample_book_id, pr.school_name, pr.status, pr.created_at, pr.updated_at,
            pr.reason, pr.conflict_reason, pr.attachment, pr.expiry_date, pr.promotion_status,
            sb.name as sample_name, sb.author, COALESCE(sb.publisher_name, '未知出版社') as publisher_name, sb.isbn,
            sb.shipping_discount, sb.settlement_discount, sb.promotion_rate
        FROM promotion_reports pr
        JOIN sample_books sb ON pr.sample_book_id = sb.id
        WHERE {where_sql}
        {order_clause}
        LIMIT %s, %s
        """
        
        all_params = params + order_params + [offset, limit]
        cursor.execute(query, all_params)
        reports = cursor.fetchall()
            
        # 查询总数
        count_query = f"""
        SELECT COUNT(*) as total
        FROM promotion_reports pr
        JOIN sample_books sb ON pr.sample_book_id = sb.id
        WHERE {where_sql}
        """
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        
        # 获取经销商的加点值
        customer_level, point_value = get_dealer_level_and_point(cursor, user_id)
        
        # 调整报告中的费率
        if point_value is not None:
            for report in reports:
                # 调整结算折扣
                if report.get('settlement_discount') is not None:
                    settlement_discount = float(report['settlement_discount'])
                    point_value_decimal = point_value / 100.0
                    report['settlement_discount'] = round(settlement_discount + point_value_decimal, 4)
                
                # 调整推广费率
                if report.get('promotion_rate') is not None:
                    promotion_rate = float(report['promotion_rate'])
                    point_value_decimal = point_value / 100.0
                    report['promotion_rate'] = round(max(0, promotion_rate - point_value_decimal), 4)
                elif report.get('shipping_discount') is not None and report.get('settlement_discount') is not None:
                    # 如果没有设置推广费率，则默认为发货折扣-结算折扣
                    shipping_discount = float(report['shipping_discount'])
                    settlement_discount = float(report['settlement_discount'])
                    report['promotion_rate'] = round(shipping_discount - settlement_discount, 4)
        
        # 格式化时间
        for report in reports:
            if report.get('created_at'):
                report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if report.get('updated_at'):
                report['updated_at'] = report['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "code": 0,
            "data": {
                "reports": reports,
                "total": total,
                "current_page": page,
                "publisher_sort_order": ','.join(publisher_order_list)
            }
        })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报备列表失败: {str(e)}"})

# 获取报备详情
@dealer_bp.route('/get_report_detail', methods=['GET'])
def get_report_detail():
    """
    获取报备详情
    请求参数:
        id: 报备ID
    返回:
        报备详情
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    report_id = request.args.get('id')
    if not report_id:
        return jsonify({"code": 1, "message": "未提供报备ID"})
    
    user_id = session.get('user_id')
    
    connection = get_db_connection()
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询报备详情，确保只能查看自己的报备
            query = """
                SELECT
                pr.id, pr.sample_book_id, pr.school_name, pr.status, pr.created_at, pr.updated_at,
                pr.reason, pr.conflict_reason, pr.attachment, pr.expiry_date, pr.promotion_status,
                sb.name as sample_name, sb.author, sb.publisher_name, sb.isbn, sb.publisher_id,
                sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                s.id as school_id
                FROM promotion_reports pr
            JOIN sample_books sb ON pr.sample_book_id = sb.id
            LEFT JOIN schools s ON pr.school_name = s.name
            WHERE pr.id = %s AND pr.dealer_id = %s
            """
            cursor.execute(query, (report_id, user_id))
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"code": 1, "message": "报备不存在或无权查看"})
            
            # 获取经销商的加点值
            customer_level, point_value = get_dealer_level_and_point(cursor, user_id)
            
            # 调整报告中的费率
            if point_value is not None:
                # 调整结算折扣
                if report.get('settlement_discount') is not None:
                    settlement_discount = float(report['settlement_discount'])
                    point_value_decimal = point_value / 100.0
                    report['settlement_discount'] = round(settlement_discount + point_value_decimal, 4)
                
                # 调整推广费率
                if report.get('promotion_rate') is not None:
                    promotion_rate = float(report['promotion_rate'])
                    point_value_decimal = point_value / 100.0
                    report['promotion_rate'] = round(max(0, promotion_rate - point_value_decimal), 4)
                elif report.get('shipping_discount') is not None and report.get('settlement_discount') is not None:
                    # 如果没有设置推广费率，则默认为发货折扣-结算折扣
                    shipping_discount = float(report['shipping_discount'])
                    settlement_discount = float(report['settlement_discount'])
                    report['promotion_rate'] = round(shipping_discount - settlement_discount, 4)
            
            # 格式化时间
            if report.get('created_at'):
                report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if report.get('updated_at'):
                report['updated_at'] = report['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
            
            return jsonify({"code": 0, "data": report})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报备详情失败: {str(e)}"})
    finally:
        connection.close()

# 撤销报备申请
@dealer_bp.route('/revoke_report', methods=['POST'])
def revoke_report():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    user_id = session.get('user_id')
    report_id = request.form.get('id')
    
    if not report_id:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查报备是否存在且属于当前用户
            check_sql = "SELECT * FROM promotion_reports WHERE id = %s AND dealer_id = %s"
            cursor.execute(check_sql, (report_id, user_id))
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"code": 1, "message": "报备不存在或无权操作"})
            
            # 只能撤销待处理的报备
            if report['status'] != 'pending':
                return jsonify({"code": 1, "message": "只能撤销待审核的报备"})
            
            # 删除报备
            delete_sql = "DELETE FROM promotion_reports WHERE id = %s"
            cursor.execute(delete_sql, (report_id,))
            connection.commit()
            
            return jsonify({"code": 0, "message": "报备已撤销"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"撤销报备失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/delete_address', methods=['POST'])
def delete_address():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    address_id = request.form.get('address_id')
    
    if not address_id:
        return jsonify({"code": 1, "message": "地址ID为必填项"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查地址是否存在且属于当前用户
            sql = "SELECT * FROM shipping_addresses WHERE address_id = %s AND teacher_id = %s"
            cursor.execute(sql, (address_id, session['user_id']))
            address = cursor.fetchone()
            
            if not address:
                return jsonify({"code": 1, "message": "地址不存在或无权删除"})
            
            # 检查该地址是否被用于未完成的报备申请
            sql = """
                SELECT COUNT(*) as count 
                FROM promotion_reports 
                WHERE address_id = %s AND status IN ('pending', 'approved')
            """
            cursor.execute(sql, (address_id,))
            result = cursor.fetchone()
            
            if result and result['count'] > 0:
                return jsonify({"code": 1, "message": "该地址已被用于进行中的报备申请，无法删除"})
            
            # 删除地址
            sql = "DELETE FROM shipping_addresses WHERE address_id = %s AND teacher_id = %s"
            cursor.execute(sql, (address_id, session['user_id']))
            connection.commit()
            return jsonify({"code": 0, "message": "地址删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除地址失败: {str(e)}"})
    finally:
        connection.close()

# 获取所有特色选项
@dealer_bp.route('/get_book_features', methods=['GET'])
def get_book_features():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询特色选项
            sql = """
                SELECT id, name, description, created_at
                FROM book_features
                ORDER BY id
            """
            cursor.execute(sql)
            features = cursor.fetchall()
            
            return jsonify({"code": 0, "data": features})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取特色选项失败: {str(e)}"})
    finally:
        connection.close()

# 获取出版社列表
@dealer_bp.route('/get_publishers', methods=['GET'])
def get_publishers():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询出版社公司表的出版社出版社公司名称
            sql = """
            SELECT name FROM publisher_companies
            """
            cursor.execute(sql)
            publishers = cursor.fetchall()

            return jsonify({"code": 0, "data": publishers})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取出版社列表失败: {str(e)}"})
    finally:
        connection.close()

# 获取国家规划级别
@dealer_bp.route('/get_national_regulation_levels', methods=['GET'])
def get_national_regulation_levels():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询国家规划级别
            sql = """
                SELECT id, name, description, created_at
                FROM national_regulation_levels
                ORDER BY id
            """
            cursor.execute(sql)
            levels = cursor.fetchall()
            
            return jsonify({"code": 0, "data": levels})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取国家规划级别失败: {str(e)}"})
    finally:
        connection.close()

# 获取省级规划级别
@dealer_bp.route('/get_provincial_regulation_levels', methods=['GET'])
def get_provincial_regulation_levels():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询省级规划级别
            sql = """
                SELECT id, name, province, description, created_at
                FROM provincial_regulation_levels
                ORDER BY id
            """
            cursor.execute(sql)
            levels = cursor.fetchall()
            
            return jsonify({"code": 0, "data": levels})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取省级规划级别失败: {str(e)}"})
    finally:
        connection.close()

# 完整的filter_samples函数
@dealer_bp.route('/filter_samples', methods=['GET'])
def filter_samples():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    # 获取筛选参数
    search = request.args.get('search', '')
    
    # 解析JSON筛选条件
    try:
        levels = json.loads(request.args.get('levels', '[]'))
        types = json.loads(request.args.get('types', '[]'))
        ranks = json.loads(request.args.get('ranks', '[]'))
        national_levels = json.loads(request.args.get('national_levels', '[]'))
        provincial_levels = json.loads(request.args.get('provincial_levels', '[]'))
        publishers = json.loads(request.args.get('publishers', '[]'))
        features = json.loads(request.args.get('features', '[]'))
    except json.JSONDecodeError:
        return jsonify({"code": 1, "message": "筛选条件格式不正确"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建基础SQL查询
            sql = """
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info, 
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.attachment_link, 
                       sb.sample_download_url, sb.resource_download_url, sb.online_reading_url,
                       sb.publisher_name, d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ') 
                        FROM sample_book_features sbf 
                        JOIN book_features bf ON sbf.feature_id = bf.id 
                        WHERE sbf.sample_id = sb.id) as feature_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE 1=1
            """
            
            params = []
            
            # 添加搜索条件
            if search:
                sql += """ AND (
                    sb.name LIKE %s OR 
                    sb.author LIKE %s OR 
                    sb.isbn LIKE %s OR 
                    sb.publisher_name LIKE %s
                )"""
                params.extend([f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'])
            
            # 添加学校层次筛选
            if levels and len(levels) > 0:
                placeholders = ', '.join(['%s'] * len(levels))
                sql += f" AND (sb.level IN ({placeholders}))"
                params.extend(levels)
            
            # 添加图书类型筛选
            if types and len(types) > 0:
                placeholders = ', '.join(['%s'] * len(types))
                sql += f" AND (sb.book_type IN ({placeholders}))"
                params.extend(types)
            
            # 添加规划级别筛选
            if ranks and len(ranks) > 0:
                rank_conditions = []
                for rank in ranks:
                    if rank == '国家规划':
                        rank_conditions.append("sb.national_regulation = 1")
                    elif rank == '省级规划':
                        rank_conditions.append("sb.provincial_regulation = 1")
                    elif rank == '普通教材':
                        rank_conditions.append("(sb.national_regulation = 0 AND sb.provincial_regulation = 0)")
                
                if rank_conditions:
                    sql += " AND (" + " OR ".join(rank_conditions) + ")"
            
            # 添加国家规划级别筛选
            if national_levels and len(national_levels) > 0:
                placeholders = ', '.join(['%s'] * len(national_levels))
                sql += f" AND (sb.national_regulation_level_id IN ({placeholders}))"
                params.extend(national_levels)
            
            # 添加省级规划级别筛选
            if provincial_levels and len(provincial_levels) > 0:
                placeholders = ', '.join(['%s'] * len(provincial_levels))
                sql += f" AND (sb.provincial_regulation_level_id IN ({placeholders}))"
                params.extend(provincial_levels)
            
            # 添加出版社筛选
            if publishers and len(publishers) > 0:
                placeholders = ', '.join(['%s'] * len(publishers))
                sql += f" AND (sb.publisher_name IN ({placeholders}))"
                params.extend(publishers)
            
            # 添加特色标签筛选
            if features and len(features) > 0:
                placeholders = ', '.join(['%s'] * len(features))
                sql += f" AND EXISTS (SELECT 1 FROM sample_book_features sbf WHERE sbf.sample_id = sb.id AND sbf.feature_id IN ({placeholders}))"
                params.extend(features)
            
            # 添加排序
            sql += " ORDER BY sb.name"
            
            # 执行查询
            cursor.execute(sql, params)
            samples = cursor.fetchall()
            
            return jsonify({"code": 0, "data": samples})
    except Exception as e:
        return jsonify({"code": 1, "message": f"筛选样书失败: {str(e)}"})
    finally:
        connection.close()

# 获取所有样书
@dealer_bp.route('/get_all_samples', methods=['GET'])
def get_all_samples():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    search = request.args.get('search', '')
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info, 
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.attachment_link,
                       sb.sample_download_url, sb.resource_download_url, sb.online_reading_url,
                       sb.publisher_name, d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ') 
                        FROM sample_book_features sbf 
                        JOIN book_features bf ON sbf.feature_id = bf.id 
                        WHERE sbf.sample_id = sb.id) as feature_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE (
                    sb.name LIKE %s OR 
                    sb.author LIKE %s OR 
                    sb.isbn LIKE %s OR
                    sb.publisher_name LIKE %s
                )
                ORDER BY sb.name
            """
            cursor.execute(sql, (f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'))
            samples = cursor.fetchall()
            
            return jsonify({"code": 0, "data": samples})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书失败: {str(e)}"})
    finally:
        connection.close()

# 获取已通过审批的报备列表（用于创建订单）
@dealer_bp.route('/get_approved_reports', methods=['GET'])
def get_approved_reports():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    dealer_id = session.get('user_id')
    publisher_id = request.args.get('publisher_id', '')
    school_id = request.args.get('school_id', '')
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件
            conditions = ["pr.dealer_id = %s", "pr.status = 'approved'"]
            params = [dealer_id]
            
            if publisher_id:
                conditions.append("sb.publisher_id = %s")
                params.append(publisher_id)
            
            if school_id:
                conditions.append("(SELECT id FROM schools WHERE name = pr.school_name LIMIT 1) = %s")
                params.append(school_id)
            
            where_clause = " AND ".join(conditions)
            
            # 查询已通过审批的报备列表
            sql = f"""
                SELECT
                    pr.id,
                    pr.sample_book_id,
                    pr.school_name,
                    pr.created_at,
                    sb.name as sample_name,
                    sb.publisher_id,
                    sb.publisher_name,
                    (SELECT name FROM publisher_companies WHERE id = (SELECT publisher_company_id FROM users WHERE user_id = sb.publisher_id)) as publisher_company_name,
                    (SELECT id FROM schools WHERE name = pr.school_name LIMIT 1) as school_id
                FROM promotion_reports pr
                LEFT JOIN sample_books sb ON pr.sample_book_id = sb.id
                WHERE {where_clause}
                ORDER BY pr.created_at DESC
            """
            cursor.execute(sql, params)
            reports = cursor.fetchall()
            
            return jsonify({
                "code": 0,
                "data": reports
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取已通过报备列表失败: {str(e)}"})
    finally:
        connection.close()

# 根据报备ID获取报备详情及相关样书
@dealer_bp.route('/get_report_books', methods=['GET'])
def get_report_books():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    dealer_id = session.get('user_id')
    report_id = request.args.get('report_id')
    
    if not report_id:
        return jsonify({"code": 1, "message": "报备ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证报备是否有效且属于该经销商
            sql = """
                SELECT
                    pr.status,
                    pr.school_name,
                    pr.sample_book_id,
                    sb.name as sample_name,
                    sb.publisher_id,
                    sb.publisher_name,
                    sb.price,
                    (SELECT name FROM publisher_companies WHERE id = (SELECT publisher_company_id FROM users WHERE user_id = sb.publisher_id)) as publisher_company_name,
                    sb.isbn,
                    sb.author,
                    (SELECT id FROM schools WHERE name = pr.school_name LIMIT 1) as school_id
                FROM promotion_reports pr
                LEFT JOIN sample_books sb ON pr.sample_book_id = sb.id
                LEFT JOIN schools s ON pr.school_name = s.name
                WHERE pr.id = %s AND pr.dealer_id = %s
            """
            cursor.execute(sql, (report_id, dealer_id))
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"code": 1, "message": "未找到有效的报备或该报备不属于您"})
            
            if report['status'] != 'approved':
                return jsonify({"code": 1, "message": "只能为已审批通过的报备提交订单"})
            
            return jsonify({
                "code": 0,
                "data": report
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报备详情失败: {str(e)}"})
    finally:
        connection.close()

# 提交订单
@dealer_bp.route('/submit_order', methods=['POST'])
def submit_order():
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        if session.get('role') != 'dealer':
            return jsonify({"code": 1, "message": "只有经销商才能提交订单"})
        
        dealer_id = session.get('user_id')
        
        data = request.json
        
        # 支持新的单样书订单格式
        if 'sample_id' in data:
            # 新格式：单样书订单
            sample_id = data.get('sample_id')
            school_id = data.get('school_id')
            shipped_quantity = data.get('shipped_quantity', data.get('quantity', 1))  # 兼容旧字段
            returned_quantity = data.get('returned_quantity', 0)
            unit_price = data.get('unit_price')
            remark = data.get('remark', '')
            
            if not sample_id:
                return jsonify({"code": 1, "message": "样书ID不能为空"})
            
            if not school_id:
                return jsonify({"code": 1, "message": "学校ID不能为空"})
            
            if not shipped_quantity or shipped_quantity <= 0:
                return jsonify({"code": 1, "message": "发货量必须大于0"})
            
            if returned_quantity < 0:
                return jsonify({"code": 1, "message": "退货量不能为负数"})
            
            if returned_quantity >= shipped_quantity:
                return jsonify({"code": 1, "message": "退货量不能大于等于发货量"})
            
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    # 获取样书信息，包括出版社ID和价格
                    sql = """
                        SELECT sb.id, sb.name, sb.price, sb.publisher_id, sb.publisher_name,
                               u.user_id as publisher_user_id
                        FROM sample_books sb
                        LEFT JOIN users u ON sb.publisher_id = u.user_id
                        WHERE sb.id = %s
                    """
                    cursor.execute(sql, (sample_id,))
                    sample = cursor.fetchone()
                    
                    if not sample:
                        return jsonify({"code": 1, "message": "样书不存在"})
                    
                    # 获取学校名称
                    sql = "SELECT name FROM schools WHERE id = %s"
                    cursor.execute(sql, (school_id,))
                    school = cursor.fetchone()
                    
                    if not school:
                        return jsonify({"code": 1, "message": "学校不存在"})
                    
                    school_name = school['name']
                    
                    # 生成订单编号
                    import random
                    current_time = datetime.now()
                    current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')
                    timestamp = current_time.strftime('%Y%m%d%H%M%S')
                    random_num = ''.join([str(random.randint(0, 9)) for _ in range(4)])
                    order_number = f"DO{timestamp}{random_num}"

                    # 获取样书价格，如果没有传入单价则使用样书默认价格
                    if unit_price is None:
                        unit_price = sample['price'] if sample and sample['price'] else 0

                    # 查找是否有匹配的报备（提交时间早于订单上传时间）
                    promotion_report_id = None
                    report_status = None
                    report_sql = """
                        SELECT id, status, created_at FROM promotion_reports
                        WHERE dealer_id = %s AND sample_book_id = %s AND school_name = %s
                        AND created_at < %s
                        ORDER BY created_at DESC LIMIT 1
                    """
                    cursor.execute(report_sql, (dealer_id, sample_id, school_name, current_time_str))
                    report = cursor.fetchone()
                    if report:
                        promotion_report_id = report['id']
                        report_status = report['status']
                    
                    # 检查是否存在重复订单（相同样书、相同学校、半年内）
                    duplicate_check_sql = """
                        SELECT id, shipped_quantity, returned_quantity, unit_price, order_number,
                               created_at, matched_order_id, reconciliation_status, remark
                        FROM order_items
                        WHERE book_id = %s AND school_name = %s AND from_dealer = 1
                        AND effective = 1 AND created_at > DATE_SUB(NOW(), INTERVAL 6 MONTH)
                        ORDER BY created_at DESC
                        LIMIT 1
                    """
                    cursor.execute(duplicate_check_sql, (sample_id, school_name))
                    existing_order = cursor.fetchone()
                    
                    # 检查是否需要用户确认累加
                    force_accumulate = data.get('force_accumulate', False)
                    
                    if existing_order and not force_accumulate:
                        # 存在重复订单，返回确认信息
                        return jsonify({
                            "code": 2,  # 特殊代码表示需要用户确认
                            "message": "检测到相同样书和学校的订单",
                            "data": {
                                "duplicate_order": {
                                    "id": existing_order['id'],
                                    "order_number": existing_order['order_number'],
                                    "shipped_quantity": existing_order['shipped_quantity'],
                                    "returned_quantity": existing_order['returned_quantity'],
                                    "unit_price": existing_order['unit_price'],
                                    "created_at": existing_order['created_at'].strftime('%Y-%m-%d %H:%M:%S'),
                                    "reconciliation_status": existing_order['reconciliation_status']
                                },
                                "new_order": {
                                    "shipped_quantity": shipped_quantity,
                                    "returned_quantity": returned_quantity,
                                    "unit_price": unit_price
                                },
                                "sample_name": sample['name'],
                                "school_name": school_name
                            }
                        })
                    
                    # 如果用户确认累加或不存在重复订单，则创建或更新订单
                    if existing_order and force_accumulate:
                        # 更新现有订单，累加数量
                        new_shipped_quantity = existing_order['shipped_quantity'] + shipped_quantity
                        new_returned_quantity = existing_order['returned_quantity'] + returned_quantity

                        # 处理备注合并
                        existing_remark = existing_order.get('remark', '') or ''
                        new_remark = remark or ''

                        if new_remark:
                            if existing_remark:
                                # 如果原订单有备注，将新备注添加到原备注后面，使用换行符连接
                                merged_remark = f"{existing_remark}\n新增订单备注：{new_remark}"
                            else:
                                # 如果原订单没有备注，直接标注为新增订单备注
                                merged_remark = f"新增订单备注：{new_remark}"
                        else:
                            # 如果新订单没有备注，保持原备注不变
                            merged_remark = existing_remark

                        update_sql = """
                            UPDATE order_items
                            SET shipped_quantity = %s, returned_quantity = %s, unit_price = %s,
                                dealer_quantity = %s, remark = %s,
                                updated_at = %s
                            WHERE id = %s
                        """

                        current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')

                        cursor.execute(update_sql, (
                            new_shipped_quantity,
                            new_returned_quantity,
                            unit_price,  # 使用最新的单价
                            new_shipped_quantity,  # 更新dealer_quantity
                            merged_remark,  # 合并后的备注
                            current_time_str,
                            existing_order['id']
                        ))
                        
                        dealer_order_id = existing_order['id']
                        order_number = existing_order['order_number']
                        
                        # 添加累加历史记录
                        history_sql = """
                            INSERT INTO order_reconciliation_history
                            (order_id, user_id, user_role, action_type, new_quantity, old_quantity, remark)
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(history_sql, (
                            dealer_order_id,
                            dealer_id,
                            'dealer',
                            'modify',
                            new_shipped_quantity,
                            existing_order['shipped_quantity'],
                            f'订单数量累加'
                        ))
                        
                        # 如果原订单已经匹配了，需要重新评估匹配状态
                        if existing_order['matched_order_id']:
                            # 将对账状态重置为预结算，需要重新确认
                            reset_status_sql = """
                                UPDATE order_items 
                                SET reconciliation_status = 'pre_settlement', 
                                    publisher_confirm_status = 'unconfirmed'
                                WHERE id = %s
                            """
                            cursor.execute(reset_status_sql, (dealer_order_id,))
                            
                            # 同时更新匹配的出版社订单状态
                            update_publisher_sql = """
                                UPDATE order_items 
                                SET reconciliation_status = 'pre_settlement',
                                    dealer_quantity = %s
                                WHERE id = %s
                            """
                            cursor.execute(update_publisher_sql, (new_shipped_quantity, existing_order['matched_order_id']))
                            
                            # 更新匹配记录状态
                            update_match_sql = """
                                UPDATE order_matches
                                SET reconciliation_status = 'pre_settlement'
                                WHERE dealer_order_id = %s
                            """
                            cursor.execute(update_match_sql, (dealer_order_id,))
                            
                    else:
                        # 创建新的经销商订单项
                        sql = """
                            INSERT INTO order_items
                            (book_id, school_name, shipped_quantity, returned_quantity, unit_price, promotion_report_id,
                             from_dealer, effective, order_number, remark, created_at,
                             dealer_quantity, dealer_confirm_status, reconciliation_status, payment_status)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        
                        # 默认经销商订单确认自己的数量
                        dealer_quantity = shipped_quantity
                        dealer_confirm_status = 'confirmed'
                        
                        cursor.execute(sql, (
                            sample_id, 
                            school_name, 
                            shipped_quantity, 
                            returned_quantity,
                            unit_price,
                            promotion_report_id,
                            1,  # from_dealer=1表示来自经销商的订单
                            1,  # effective=1表示有效状态
                            order_number, 
                            remark, 
                            current_time_str,
                            dealer_quantity,
                            dealer_confirm_status,
                            'pre_settlement',  # 默认为预结算状态
                            0  # 默认为未支付状态
                        ))
                        
                        # 获取刚插入的订单ID
                        dealer_order_id = cursor.lastrowid
                    
                    # 始终插入对账历史记录（无论是否匹配到出版社订单）
                    if not (existing_order and force_accumulate):  # 新订单才插入创建记录
                        history_sql = """
                            INSERT INTO order_reconciliation_history
                            (order_id, user_id, user_role, action_type, new_quantity, remark)
                            VALUES (%s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(history_sql, (
                            dealer_order_id,
                            dealer_id,
                            'dealer',
                            'upload',
                            shipped_quantity,
                            '经销商上传单样书订单'
                        ))
                    
                    # 根据报备状态处理订单匹配
                    matching_order = None
                    if promotion_report_id:  # 有报备的订单才能进行自动对账
                        sample_publisher_id = sample['publisher_id']

                        if report_status == 'successful':
                            # 如果报备是推广成功状态，检查出版社端符合条件的订单进行对账
                            match_sql = """
                                SELECT oi.id, oi.shipped_quantity, oi.publisher_quantity, oi.publisher_confirm_status,
                                       oi.promotion_report_id, sb.publisher_id, sb.name as book_name
                                FROM order_items oi
                                INNER JOIN sample_books sb ON oi.book_id = sb.id
                                WHERE oi.book_id = %s AND oi.school_name = %s AND oi.from_dealer = 0
                                AND oi.effective = 1 AND oi.matched_order_id IS NULL
                                AND sb.publisher_id = %s
                                ORDER BY oi.created_at DESC
                                LIMIT 1
                            """
                            cursor.execute(match_sql, (sample_id, school_name, sample_publisher_id))
                            matching_order = cursor.fetchone()

                            if matching_order:
                                publisher_order_id = matching_order['id']
                                publisher_quantity = matching_order['shipped_quantity']

                                # 判断实销数量是否一致
                                if shipped_quantity == publisher_quantity:
                                    reconciliation_status = 'pending_payment'
                                    message_suffix = '，数量一致，已进入待支付状态'
                                else:
                                    reconciliation_status = 'pre_settlement'
                                    message_suffix = '，数量不一致，进入对账状态'

                                # 创建订单匹配记录
                                match_insert_sql = """
                                    INSERT INTO order_matches
                                    (publisher_order_id, dealer_order_id, reconciliation_status)
                                    VALUES (%s, %s, %s)
                                """
                                cursor.execute(match_insert_sql, (publisher_order_id, dealer_order_id, reconciliation_status))

                                # 更新双方订单的匹配ID和对账状态
                                update_dealer_sql = """
                                    UPDATE order_items SET
                                    matched_order_id = %s,
                                    publisher_quantity = %s,
                                    reconciliation_status = %s
                                    WHERE id = %s
                                """
                                cursor.execute(update_dealer_sql, (
                                    publisher_order_id,
                                    publisher_quantity,
                                    reconciliation_status,
                                    dealer_order_id
                                ))

                                update_publisher_sql = """
                                    UPDATE order_items SET
                                    matched_order_id = %s,
                                    dealer_quantity = %s,
                                    dealer_confirm_status = %s,
                                    reconciliation_status = %s
                                    WHERE id = %s
                                """
                                cursor.execute(update_publisher_sql, (
                                    dealer_order_id,
                                    shipped_quantity,
                                    'confirmed',
                                    reconciliation_status,
                                    publisher_order_id
                                ))

                                # 更新订单匹配记录状态
                                update_match_sql = """
                                    UPDATE order_matches
                                    SET reconciliation_status = %s
                                    WHERE publisher_order_id = %s AND dealer_order_id = %s
                                """
                                cursor.execute(update_match_sql, (reconciliation_status, publisher_order_id, dealer_order_id))

                                # 添加自动匹配的对账历史记录
                                match_history_sql = """
                                    INSERT INTO order_reconciliation_history
                                    (order_id, user_id, user_role, action_type, new_quantity, remark)
                                    VALUES (%s, %s, %s, %s, %s, %s)
                                """
                                cursor.execute(match_history_sql, (
                                    dealer_order_id,
                                    dealer_id,
                                    'dealer',
                                    'confirm',
                                    shipped_quantity,
                                    f'自动匹配出版社订单（基于推广成功报备）{message_suffix}'
                                ))

                        elif report_status == 'approved':
                            # 如果报备是已通过状态，订单进入预结算状态，等待出版社上传订单时进行对碰
                            pass  # 不需要特殊处理，保持预结算状态
                            

                    
                    # 检测推荐成功
                    check_recommendation_success(connection, order_number)
                    
                    # 完成事务提交
                    connection.commit()
                    
                    # 根据匹配情况和对账状态返回不同消息
                    if matching_order:
                        if reconciliation_status == 'pending_payment':
                            return jsonify({
                                'success': True,
                                'message': '订单提交成功，已匹配到出版社订单，数量一致，已进入待支付状态',
                                'order_number': order_number,
                                'need_reconciliation': False,
                                'order_id': dealer_order_id,
                                'reconciliation_status': 'pending_payment'
                            })
                        else:
                            return jsonify({
                                'success': True,
                                'message': '订单提交成功，已匹配到出版社订单，数量不一致，需要对账',
                                'order_number': order_number,
                                'need_reconciliation': True,
                                'order_id': dealer_order_id,
                                'publisher_quantity': matching_order['shipped_quantity'],
                                'dealer_quantity': shipped_quantity
                            })
                    else:
                        if promotion_report_id and report_status == 'approved':
                            return jsonify({'success': True, 'message': '订单提交成功，进入预结算状态，等待出版社上传订单进行对碰', 'order_number': order_number})
                        else:
                            return jsonify({'success': True, 'message': '订单提交成功', 'order_number': order_number})
                    
            except Exception as e:
                connection.rollback()
                return jsonify({'success': False, 'message': '创建订单失败: ' + str(e)})
            finally:
                connection.close()
        
        else:
            # 原有格式：多样书订单（保持向后兼容）
            publisher_id = data.get('publisher_id')
            school_id = data.get('school_id')
            report_id = data.get('report_id')  # 现在报备ID是可选的
            items = data.get('items', [])
            remark = data.get('remark', '')
            
            if not school_id:
                return jsonify({"code": 1, "message": "学校ID不能为空"})
            
            if not items or len(items) == 0:
                return jsonify({"code": 1, "message": "订单项目不能为空"})
            
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    # 获取学校名称
                    sql = "SELECT name FROM schools WHERE id = %s"
                    cursor.execute(sql, (school_id,))
                    school = cursor.fetchone()
                    
                    if not school:
                        return jsonify({"code": 1, "message": "所选学校不存在"})
                    
                    school_name = school['name']
                    
                    # 生成订单编号
                    import random
                    current_time = datetime.now()
                    current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')
                    timestamp = current_time.strftime('%Y%m%d%H%M%S')
                    random_num = ''.join([str(random.randint(0, 9)) for _ in range(4)])
                    order_number = f"DO{timestamp}{random_num}"
                    
                    # 添加订单项目
                    success_count = 0
                    matched_orders = []  # 存储匹配到的订单信息
                    
                    # 检查是否需要用户确认累加（多本书订单支持）
                    force_accumulate = data.get('force_accumulate', False)
                    duplicate_items = []  # 存储检测到的重复项
                    
                    # 首先检查所有订单项是否有重复
                    for item in items:
                        sample_id = item.get('sample_id')
                        shipped_quantity = item.get('shipped_quantity', item.get('quantity', 0))  # 兼容旧字段
                        returned_quantity = item.get('returned_quantity', 0)
                        unit_price = item.get('unit_price')
                        
                        if not sample_id or shipped_quantity <= 0:
                            continue
                        
                        if returned_quantity < 0:
                            continue
                        
                        if returned_quantity >= shipped_quantity:
                            continue
                        
                        # 获取样书信息
                        sample_sql = """
                            SELECT sb.id, sb.name, sb.price, sb.publisher_id, sb.publisher_name
                            FROM sample_books sb
                            WHERE sb.id = %s
                        """
                        cursor.execute(sample_sql, (sample_id,))
                        sample = cursor.fetchone()
                        
                        if not sample:
                            continue
                        
                        # 获取样书价格，如果没有传入单价则使用样书默认价格
                        if unit_price is None:
                            unit_price = sample['price'] if sample and sample['price'] else 0
                        
                        # 检查是否存在重复订单（相同样书、相同学校、半年内）
                        duplicate_check_sql = """
                            SELECT id, shipped_quantity, returned_quantity, unit_price, order_number,
                                   created_at, matched_order_id, reconciliation_status, remark
                            FROM order_items
                            WHERE book_id = %s AND school_name = %s AND from_dealer = 1
                            AND effective = 1 AND created_at > DATE_SUB(NOW(), INTERVAL 6 MONTH)
                            ORDER BY created_at DESC
                            LIMIT 1
                        """
                        cursor.execute(duplicate_check_sql, (sample_id, school_name))
                        existing_order = cursor.fetchone()
                        
                        if existing_order and not force_accumulate:
                            # 发现重复订单，添加到重复列表
                            duplicate_items.append({
                                "sample_id": sample_id,
                                "sample_name": sample['name'],
                                "duplicate_order": {
                                    "id": existing_order['id'],
                                    "order_number": existing_order['order_number'],
                                    "shipped_quantity": existing_order['shipped_quantity'],
                                    "returned_quantity": existing_order['returned_quantity'],
                                    "unit_price": existing_order['unit_price'],
                                    "created_at": existing_order['created_at'].strftime('%Y-%m-%d %H:%M:%S'),
                                    "reconciliation_status": existing_order['reconciliation_status']
                                },
                                "new_order": {
                                    "shipped_quantity": shipped_quantity,
                                    "returned_quantity": returned_quantity,
                                    "unit_price": unit_price
                                }
                            })
                    
                    # 如果有重复项且未强制累加，返回确认信息
                    if duplicate_items and not force_accumulate:
                        return jsonify({
                            "code": 2,  # 特殊代码表示需要用户确认
                            "message": f"检测到{len(duplicate_items)}个重复订单项目",
                            "data": {
                                "duplicate_items": duplicate_items,
                                "school_name": school_name,
                                "total_items": len(items)
                            }
                        })

                    for item in items:
                        sample_id = item.get('sample_id')
                        shipped_quantity = item.get('shipped_quantity', item.get('quantity', 0))  # 兼容旧字段
                        returned_quantity = item.get('returned_quantity', 0)
                        unit_price = item.get('unit_price')
                        
                        if not sample_id or shipped_quantity <= 0:
                            continue
                        
                        if returned_quantity < 0:
                            continue
                        
                        if returned_quantity >= shipped_quantity:
                            continue
                        
                        # 获取样书价格，如果没有传入单价则使用样书默认价格
                        if unit_price is None:
                            price_sql = "SELECT price FROM sample_books WHERE id = %s"
                            cursor.execute(price_sql, (sample_id,))
                            sample = cursor.fetchone()
                            unit_price = sample['price'] if sample and sample['price'] else 0
                        
                        # 如果强制累加，检查是否存在重复订单进行累加
                        if force_accumulate:
                            duplicate_check_sql = """
                                SELECT id, shipped_quantity, returned_quantity, unit_price, order_number,
                                       created_at, matched_order_id, reconciliation_status, remark
                                FROM order_items
                                WHERE book_id = %s AND school_name = %s AND from_dealer = 1
                                AND effective = 1 AND created_at > DATE_SUB(NOW(), INTERVAL 6 MONTH)
                                ORDER BY created_at DESC
                                LIMIT 1
                            """
                            cursor.execute(duplicate_check_sql, (sample_id, school_name))
                            existing_order = cursor.fetchone()

                            if existing_order:
                                # 更新现有订单，累加数量
                                new_shipped_quantity = existing_order['shipped_quantity'] + shipped_quantity
                                new_returned_quantity = existing_order['returned_quantity'] + returned_quantity

                                # 处理备注合并
                                existing_remark = existing_order.get('remark', '') or ''
                                new_remark = remark or ''

                                if new_remark:
                                    if existing_remark:
                                        # 如果原订单有备注，将新备注添加到原备注后面，使用换行符连接
                                        merged_remark = f"{existing_remark}\n新增订单备注：{new_remark}"
                                    else:
                                        # 如果原订单没有备注，直接标注为新增订单备注
                                        merged_remark = f"新增订单备注：{new_remark}"
                                else:
                                    # 如果新订单没有备注，保持原备注不变
                                    merged_remark = existing_remark

                                update_sql = """
                                    UPDATE order_items
                                    SET shipped_quantity = %s, returned_quantity = %s, unit_price = %s,
                                        dealer_quantity = %s, remark = %s,
                                        updated_at = %s
                                    WHERE id = %s
                                """

                                current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')

                                cursor.execute(update_sql, (
                                    new_shipped_quantity,
                                    new_returned_quantity,
                                    unit_price,  # 使用最新的单价
                                    new_shipped_quantity,  # 更新dealer_quantity
                                    merged_remark,  # 合并后的备注
                                    current_time_str,
                                    existing_order['id']
                                ))
                                
                                # 添加累加历史记录
                                history_sql = """
                                    INSERT INTO order_reconciliation_history
                                    (order_id, user_id, user_role, action_type, new_quantity, old_quantity, remark)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                                """
                                cursor.execute(history_sql, (
                                    existing_order['id'],
                                    dealer_id,
                                    'dealer',
                                    'modify',
                                    new_shipped_quantity,
                                    existing_order['shipped_quantity'],
                                    f'订单数量累加'
                                ))
                                
                                # 如果原订单已经匹配了，需要重新评估匹配状态
                                if existing_order['matched_order_id']:
                                    # 将对账状态重置为预结算，需要重新确认
                                    reset_status_sql = """
                                        UPDATE order_items 
                                        SET reconciliation_status = 'pre_settlement', 
                                            publisher_confirm_status = 'unconfirmed'
                                        WHERE id = %s
                                    """
                                    cursor.execute(reset_status_sql, (existing_order['id'],))
                                    
                                    # 同时更新匹配的出版社订单状态
                                    update_publisher_sql = """
                                        UPDATE order_items 
                                        SET reconciliation_status = 'pre_settlement',
                                            dealer_quantity = %s
                                        WHERE id = %s
                                    """
                                    cursor.execute(update_publisher_sql, (new_shipped_quantity, existing_order['matched_order_id']))
                                    
                                    # 更新匹配记录状态
                                    update_match_sql = """
                                        UPDATE order_matches
                                        SET reconciliation_status = 'pre_settlement'
                                        WHERE dealer_order_id = %s
                                    """
                                    cursor.execute(update_match_sql, (existing_order['id'],))
                                
                                success_count += 1
                                continue
                        
                        # 查找是否有报备（如果提供了报备ID）
                        promotion_report_id = None
                        if report_id:
                            report_sql = "SELECT id FROM promotion_reports WHERE id = %s AND dealer_id = %s AND status = 'approved'"
                            cursor.execute(report_sql, (report_id, dealer_id))
                            report = cursor.fetchone()
                            if report:
                                promotion_report_id = report['id']
                        else:
                            # 如果没有提供报备ID，查找是否有匹配的有效报备
                            report_sql = """
                                SELECT id, status FROM promotion_reports 
                                WHERE dealer_id = %s AND sample_book_id = %s AND school_name = %s 
                                AND status = 'approved'
                                ORDER BY updated_at DESC LIMIT 1
                            """
                            cursor.execute(report_sql, (dealer_id, sample_id, school_name))
                            report = cursor.fetchone()
                            if report:
                                promotion_report_id = report['id']
                    
                        # 创建经销商订单项
                        sql = """
                            INSERT INTO order_items
                            (book_id, school_name, shipped_quantity, returned_quantity, unit_price, promotion_report_id,
                             from_dealer, effective, order_number, remark, created_at,
                             dealer_quantity, dealer_confirm_status, reconciliation_status, payment_status)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        
                        # 默认经销商订单确认自己的数量
                        dealer_quantity = shipped_quantity
                        dealer_confirm_status = 'confirmed'
                        
                        cursor.execute(sql, (
                            sample_id, 
                            school_name, 
                            shipped_quantity, 
                            returned_quantity,
                            unit_price,
                            promotion_report_id, 
                            1,  # from_dealer=1表示来自经销商的订单
                            1,  # effective=1表示有效状态
                            order_number, 
                            remark, 
                            current_time_str,
                            dealer_quantity,
                            dealer_confirm_status,
                            'pre_settlement',  # 默认为预结算状态
                            0  # 默认为未支付状态
                        ))
                        
                        # 获取刚插入的订单ID
                        dealer_order_id = cursor.lastrowid
                        
                        # 始终插入对账历史记录（无论是否匹配到出版社订单）
                        history_sql = """
                            INSERT INTO order_reconciliation_history
                            (order_id, user_id, user_role, action_type, new_quantity, remark)
                            VALUES (%s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(history_sql, (
                            dealer_order_id,
                            dealer_id,
                            'dealer',
                            'upload',
                            shipped_quantity,
                            '经销商上传订单'
                        ))
                        
                        # 只有当前订单有已通过报备时，才尝试查找匹配的出版社订单进行对账
                        if promotion_report_id:  # 只有有报备的订单才能进行自动对账
                            # 首先获取当前样书的出版社ID
                            get_publisher_sql = "SELECT publisher_id, name FROM sample_books WHERE id = %s"
                            cursor.execute(get_publisher_sql, (sample_id,))
                            sample_info = cursor.fetchone()

                            if sample_info:
                                sample_publisher_id = sample_info['publisher_id']
                                sample_name = sample_info['name']
                                
                                # 查找匹配的出版社订单（相同样书、相同学校、有已通过报备的）
                                match_sql = """
                                    SELECT oi.id, oi.shipped_quantity, oi.publisher_quantity, oi.publisher_confirm_status,
                                           oi.promotion_report_id, sb.publisher_id, sb.name as book_name,
                                           pr.status as report_status
                                    FROM order_items oi
                                    INNER JOIN sample_books sb ON oi.book_id = sb.id
                                        LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                                    WHERE oi.book_id = %s AND oi.school_name = %s AND oi.from_dealer = 0 
                                    AND oi.effective = 1 AND oi.matched_order_id IS NULL
                                    AND sb.publisher_id = %s
                                        AND (
                                            oi.promotion_report_id IS NULL OR 
                                            (pr.id IS NOT NULL AND pr.status = 'approved')
                                        )
                                    ORDER BY 
                                        CASE WHEN oi.promotion_report_id IS NOT NULL THEN 0 ELSE 1 END,
                                        oi.created_at DESC
                                    LIMIT 1
                                """
                                cursor.execute(match_sql, (sample_id, school_name, sample_publisher_id))
                                matching_order = cursor.fetchone()
                                
                                if matching_order:
                                    publisher_order_id = matching_order['id']
                                    publisher_quantity = matching_order['shipped_quantity']
                                    

                                    
                                    # 创建订单匹配记录
                                    match_insert_sql = """
                                        INSERT INTO order_matches 
                                        (publisher_order_id, dealer_order_id, reconciliation_status)
                                        VALUES (%s, %s, %s)
                                    """
                                    
                                    # 设置订单的匹配关系，但不自动设置为待支付状态
                                    reconciliation_status = 'pre_settlement'
                                    
                                    cursor.execute(match_insert_sql, (publisher_order_id, dealer_order_id, reconciliation_status))
                                    
                                    # 更新双方订单的匹配ID和对账状态
                                    update_dealer_sql = """
                                        UPDATE order_items SET 
                                        matched_order_id = %s, 
                                        publisher_quantity = %s,
                                        reconciliation_status = %s
                                        WHERE id = %s
                                    """
                                    cursor.execute(update_dealer_sql, (
                                        publisher_order_id, 
                                        publisher_quantity, 
                                        reconciliation_status, 
                                        dealer_order_id
                                    ))
                                    
                                    update_publisher_sql = """
                                        UPDATE order_items SET 
                                        matched_order_id = %s, 
                                        dealer_quantity = %s,
                                        dealer_confirm_status = %s,
                                        reconciliation_status = %s
                                        WHERE id = %s
                                    """
                                    cursor.execute(update_publisher_sql, (
                                        dealer_order_id, 
                                            shipped_quantity, 
                                        'confirmed', 
                                        reconciliation_status, 
                                        publisher_order_id
                                    ))
                                    
                                    # 更新订单匹配记录状态
                                    update_match_sql = """
                                        UPDATE order_matches
                                        SET reconciliation_status = %s
                                        WHERE publisher_order_id = %s AND dealer_order_id = %s
                                    """
                                    cursor.execute(update_match_sql, (reconciliation_status, publisher_order_id, dealer_order_id))
                                    
                                    # 添加自动匹配的对账历史记录
                                    match_history_sql = """
                                        INSERT INTO order_reconciliation_history
                                        (order_id, user_id, user_role, action_type, new_quantity, remark)
                                        VALUES (%s, %s, %s, %s, %s, %s)
                                    """
                                    cursor.execute(match_history_sql, (
                                        dealer_order_id,
                                        dealer_id,
                                        'dealer',
                                        'confirm',
                                            shipped_quantity,
                                            '自动匹配出版社订单（基于已通过报备）'
                                    ))
                                    
                                    # 添加到匹配订单列表
                                    matched_orders.append({
                                        'order_id': dealer_order_id,
                                        'sample_name': sample_name,
                                        'publisher_quantity': publisher_quantity,
                                            'dealer_quantity': shipped_quantity
                                    })

                        # 成功创建订单项目，递增计数器
                        success_count += 1


                    if success_count == 0:
                        return jsonify({"code": 1, "message": "没有有效的订单项目"})
                    
                    # 检测推荐成功
                    check_recommendation_success(connection, order_number)
                
                    # 完成事务提交
                    connection.commit()
                    
                    # 如果有匹配到的订单，返回匹配信息
                    if matched_orders:
                        return jsonify({
                            'success': True, 
                            'message': '订单提交成功，已匹配到出版社订单', 
                            'order_number': order_number,
                            'need_reconciliation': True,
                            'matched_orders': matched_orders
                        })
                    else:
                        return jsonify({'success': True, 'message': '订单提交成功', 'order_number': order_number})
                
            except Exception as e:
                connection.rollback()
                return jsonify({'success': False, 'message': '创建订单失败: ' + str(e)})
            finally:
                connection.close()
                
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 添加检测推荐成功的函数
def check_recommendation_success(connection, order_number):
    with connection.cursor(pymysql.cursors.DictCursor) as cursor:
        # 获取订单项
        sql = """
            SELECT oi.* 
            FROM order_items oi
            WHERE oi.order_number = %s AND oi.effective = 1
        """
        cursor.execute(sql, (order_number,))
        order_items = cursor.fetchall()
        
        if not order_items:
            return
        
        # 检查每个订单项是否匹配推荐
        for item in order_items:
            book_id = item['book_id']
            school_name = item['school_name']
            
            if not school_name:
                continue
            
            # 查找匹配的学校
            cursor.execute("SELECT id FROM schools WHERE name = %s", (school_name,))
            school = cursor.fetchone()
            
            if not school:
                continue
            
            school_id = school['id']
            
            # 查找进行中的推荐
            sql = """
                SELECT DISTINCT br.id 
                FROM book_recommendations br
                JOIN recommendation_results rr ON br.id = rr.recommendation_id
                WHERE br.school_id = %s AND br.status = 'in_progress' 
                AND rr.recommended_book_id = %s AND rr.status = 'pending'
            """
            cursor.execute(sql, (school_id, book_id))
            recommendations = cursor.fetchall()
            
            for rec in recommendations:
                # 更新推荐结果状态
                sql = """
                    UPDATE recommendation_results
                    SET status = 'approved', updated_at = NOW()
                    WHERE recommendation_id = %s AND recommended_book_id = %s
                """
                cursor.execute(sql, (rec['id'], book_id))

                # 检查是否所有推荐结果都已成功
                sql = """
                    SELECT COUNT(*) as total, SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as successful
                    FROM recommendation_results
                    WHERE recommendation_id = %s
                """
                cursor.execute(sql, (rec['id'],))
                stats = cursor.fetchone()

                # 如果至少有一个推荐成功，则标记推荐为结束
                if stats['successful'] > 0:
                    sql = "UPDATE book_recommendations SET status = 'ended', updated_at = NOW() WHERE id = %s"
                    cursor.execute(sql, (rec['id'],))

# 获取经销商订单列表
@dealer_bp.route('/get_orders', methods=['GET'])
def get_orders():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    if session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "只有经销商才能查看订单"})
    
    dealer_id = session.get('user_id')
    
    try:
        status = request.args.get('status', '')
        publisher_id = request.args.get('publisher_id', '')
        publisher_company_name = request.args.get('publisher_company_name', '')
        school_id = request.args.get('school_id', '')
        reconciliation_status = request.args.get('reconciliation_status', '')  # 新增对账状态过滤
        payment_status = request.args.get('payment_status', '')  # 新增支付状态过滤
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        
        offset = (page - 1) * page_size
        
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 构建查询条件 - 支持有报备和无报备的订单
                conditions = ["oi.from_dealer = 1"]  # 只查询来自经销商的订单
                params = []
                
                # 通过两种方式识别经销商订单：
                # 1. 有报备的订单：通过promotion_reports表关联
                # 2. 无报备的订单：通过order_reconciliation_history表关联
                conditions.append("""(
                    (oi.promotion_report_id IS NOT NULL AND pr.dealer_id = %s) OR
                    (oi.promotion_report_id IS NULL AND EXISTS (
                        SELECT 1 FROM order_reconciliation_history orh 
                        WHERE orh.order_id = oi.id AND orh.user_id = %s AND orh.user_role = 'dealer'
                    ))
                )""")
                params.extend([dealer_id, dealer_id])
                
                if status:
                    if status == "pending":
                        conditions.append("oi.effective = 0")
                    elif status == "approved":
                        conditions.append("oi.effective = 1")
                    elif status == "rejected":
                        conditions.append("oi.effective = 2")
                
                if publisher_id:
                    conditions.append("sb.publisher_id = %s")
                    params.append(publisher_id)
                
                if publisher_company_name:
                    conditions.append("""(SELECT name FROM publisher_companies WHERE id = 
                                        (SELECT publisher_company_id FROM users WHERE user_id = sb.publisher_id)) = %s""")
                    params.append(publisher_company_name)
                
                if school_id:
                    conditions.append("oi.school_name = (SELECT name FROM schools WHERE id = %s)")
                    params.append(school_id)
                    
                # 添加对账状态筛选
                if reconciliation_status:
                    conditions.append("oi.reconciliation_status = %s")
                    params.append(reconciliation_status)
                
                # 添加支付状态筛选
                if payment_status:
                    conditions.append("oi.payment_status = %s")
                    params.append(int(payment_status))
                
                where_clause = " AND ".join(conditions)
                
                # 查询订单总数
                count_sql = f"""
                    SELECT COUNT(*) as total
                    FROM order_items oi
                    LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                    LEFT JOIN sample_books sb ON oi.book_id = sb.id
                    WHERE {where_clause}
                """
                cursor.execute(count_sql, params)
                total = cursor.fetchone()['total']
                
                # 查询订单
                sql = f"""
                    SELECT 
                        oi.id, 
                        oi.book_id as sample_id,
                        sb.name as sample_name,
                        sb.publisher_id,
                        sb.publisher_name,
                        (SELECT name FROM publisher_companies WHERE id = (SELECT publisher_company_id FROM users WHERE user_id = sb.publisher_id)) as publisher_company_name,
                        oi.school_name,
                        oi.promotion_report_id,
                        oi.effective,
                        oi.reject_reason,
                        oi.remark,
                        oi.created_at,
                        oi.updated_at,
                        oi.order_number,
                        oi.shipped_quantity,
                        oi.returned_quantity,
                        oi.unit_price,
                        sb.isbn,
                        sb.author,
                        sb.shipping_discount,
                        sb.settlement_discount,
                        sb.promotion_rate,
                        (SELECT id FROM schools WHERE name = oi.school_name LIMIT 1) as school_id,
                        oi.reconciliation_status,
                        oi.payment_status,
                        oi.publisher_quantity,
                        oi.dealer_quantity,
                        oi.publisher_confirm_status,
                        oi.dealer_confirm_status,
                        oi.matched_order_id,
                        oi.last_modified_by
                    FROM order_items oi
                    LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                    LEFT JOIN sample_books sb ON oi.book_id = sb.id
                    WHERE {where_clause}
                    ORDER BY oi.created_at DESC
                    LIMIT %s, %s
                """
                
                params.extend([offset, page_size])
                cursor.execute(sql, params)
                orders = cursor.fetchall()
                
                # 获取经销商的加点值
                customer_level, point_value = get_dealer_level_and_point(cursor, dealer_id)
                
                result = []
                for order in orders:
                    # 计算实销数量 = 发货量 - 退货量
                    shipped_quantity = order.get('shipped_quantity', 0) or 0
                    returned_quantity = order.get('returned_quantity', 0) or 0
                    effective_quantity = shipped_quantity - returned_quantity
                    
                    # 计算推广费率（考虑加点值）
                    promotion_rate = 0
                    if order.get('promotion_rate') is not None:
                        promotion_rate = float(order['promotion_rate'])
                        if point_value is not None:
                            point_value_decimal = point_value / 100.0
                            promotion_rate = max(0, promotion_rate - point_value_decimal)
                    elif order.get('shipping_discount') is not None and order.get('settlement_discount') is not None:
                        # 如果没有设置推广费率，则默认为发货折扣-结算折扣
                        shipping_discount = float(order['shipping_discount'])
                        settlement_discount = float(order['settlement_discount'])
                        if point_value is not None:
                            point_value_decimal = point_value / 100.0
                            settlement_discount = settlement_discount + point_value_decimal
                        promotion_rate = max(0, shipping_discount - settlement_discount)
                    
                    # 计算实销码洋 = 实销数量 * 单价 * 推广费率
                    unit_price = float(order.get('unit_price', 0) or 0)
                    effective_sales_value = effective_quantity * unit_price * promotion_rate
                    
                    # 使用对账状态而不是effective状态
                    reconciliation_status = order.get('reconciliation_status', 'pre_settlement')
                    payment_status = order.get('payment_status', 0)
                    
                    # 添加状态映射，将effective转换为状态文本
                    if order.get('effective') == 0:
                        status = 'pending'
                    elif order.get('effective') == 1:
                        status = 'approved'
                    elif order.get('effective') == 2:
                        status = 'rejected'
                    else:
                        status = 'unknown'
                    
                    order_data = {
                        "id": order['id'],
                        "order_number": order.get('order_number'),
                        "sample_id": order.get('sample_id'),
                        "sample_name": order.get('sample_name'),
                        "isbn": order.get('isbn'),
                        "author": order.get('author'),
                        "publisher_id": order.get('publisher_id'),
                        "publisher_name": order.get('publisher_name'),
                        "publisher_company_name": order.get('publisher_company_name'),
                        "school_id": order.get('school_id'),
                        "school_name": order.get('school_name'),
                        "promotion_report_id": order.get('promotion_report_id'),
                        "status": status,
                        "reconciliation_status": reconciliation_status,
                        "payment_status": payment_status,
                        "shipped_quantity": shipped_quantity,
                        "returned_quantity": returned_quantity,
                        "effective_quantity": effective_quantity,
                        "unit_price": unit_price,
                        "promotion_rate": round(promotion_rate, 4),
                        "effective_sales_value": round(effective_sales_value, 2),
                        "publisher_quantity": order.get('publisher_quantity'),
                        "dealer_quantity": order.get('dealer_quantity'),
                        "publisher_confirm_status": order.get('publisher_confirm_status', 'unconfirmed'),
                        "dealer_confirm_status": order.get('dealer_confirm_status', 'unconfirmed'),
                        "matched_order_id": order.get('matched_order_id'),
                        "last_modified_by": order.get('last_modified_by'),
                        "remark": order.get('remark'),
                        "created_at": order['created_at'].strftime('%Y-%m-%d %H:%M:%S') if order['created_at'] else None,
                        "updated_at": order['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if order['updated_at'] else None
                    }
                    result.append(order_data)
                
                return jsonify({
                    "code": 0,
                    "data": {
                        "total": total,
                        "page": page,
                        "page_size": page_size,
                        "orders": result
                    }
                })
                
        except Exception as e:
            return jsonify({"code": 1, "message": f"获取订单列表失败: {str(e)}"})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取订单列表失败: {str(e)}"})

# 获取订单项目详情
def get_order_items(cursor, order_id):
    sql = """
        SELECT 
            oi.id, 
            oi.book_id,
            sb.name as sample_name,
            sb.isbn,
            sb.author,
            oi.shipped_quantity as quantity,
            oi.returned_quantity,
            oi.unit_price
        FROM order_items oi
        LEFT JOIN sample_books sb ON oi.book_id = sb.id
        WHERE oi.order_number = (SELECT order_number FROM order_items WHERE id = %s)
    """
    cursor.execute(sql, (order_id,))
    items = cursor.fetchall()
    return items

# 根据订单ID获取订单详情
@dealer_bp.route('/get_order_detail', methods=['GET'])
def get_order_detail():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    if session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "只有经销商才能查看订单详情"})
    
    dealer_id = session.get('user_id')
    order_id = request.args.get('order_id')
    
    if not order_id:
        return jsonify({"code": 1, "message": "订单ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询订单基本信息 - 支持有报备和无报备的订单
            sql = """
                SELECT 
                    oi.id, 
                    oi.book_id as sample_id,
                    sb.name as sample_name,
                    sb.publisher_id,
                    sb.publisher_name,
                    (SELECT name FROM publisher_companies WHERE id = (SELECT publisher_company_id FROM users WHERE user_id = sb.publisher_id)) as publisher_company_name,
                    oi.school_name,
                    oi.promotion_report_id,
                    oi.effective,
                    oi.reject_reason,
                    oi.remark,
                    oi.created_at,
                    oi.updated_at,
                    oi.order_number,
                    oi.shipped_quantity,
                    oi.returned_quantity,
                    oi.unit_price,
                    sb.isbn,
                    sb.author,
                    sb.shipping_discount,
                    sb.settlement_discount,
                    sb.promotion_rate,
                    (SELECT id FROM schools WHERE name = oi.school_name LIMIT 1) as school_id,
                    oi.reconciliation_status,
                    oi.payment_status,
                    oi.publisher_quantity,
                    oi.dealer_quantity,
                    oi.publisher_confirm_status,
                    oi.dealer_confirm_status,
                    oi.matched_order_id,
                    oi.last_modified_by
                FROM order_items oi
                LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                LEFT JOIN sample_books sb ON oi.book_id = sb.id
                WHERE oi.id = %s AND oi.from_dealer = 1 AND (
                    (oi.promotion_report_id IS NOT NULL AND pr.dealer_id = %s) OR
                    (oi.promotion_report_id IS NULL AND EXISTS (
                        SELECT 1 FROM order_reconciliation_history orh 
                        WHERE orh.order_id = oi.id AND orh.user_id = %s AND orh.user_role = 'dealer'
                    ))
                )
            """
            
            cursor.execute(sql, (order_id, dealer_id, dealer_id))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或无权访问"})
            
            # 获取经销商的加点值
            customer_level, point_value = get_dealer_level_and_point(cursor, dealer_id)
            
            # 计算实销数量 = 发货量 - 退货量
            shipped_quantity = order.get('shipped_quantity', 0) or 0
            returned_quantity = order.get('returned_quantity', 0) or 0
            effective_quantity = shipped_quantity - returned_quantity
            
            # 计算推广费率（考虑加点值）
            promotion_rate = 0
            if order.get('promotion_rate') is not None:
                promotion_rate = float(order['promotion_rate'])
                if point_value is not None:
                    point_value_decimal = point_value / 100.0
                    promotion_rate = max(0, promotion_rate - point_value_decimal)
            elif order.get('shipping_discount') is not None and order.get('settlement_discount') is not None:
                # 如果没有设置推广费率，则默认为发货折扣-结算折扣
                shipping_discount = float(order['shipping_discount'])
                settlement_discount = float(order['settlement_discount'])
                if point_value is not None:
                    point_value_decimal = point_value / 100.0
                    settlement_discount = settlement_discount + point_value_decimal
                promotion_rate = max(0, shipping_discount - settlement_discount)
            
            # 计算实销码洋 = 实销数量 * 单价 * 推广费率
            unit_price = float(order.get('unit_price', 0) or 0)
            effective_sales_value = effective_quantity * unit_price * promotion_rate
            
            # 转换订单状态
            status = "pending"
            if order.get('effective') == 1:
                status = "approved"
            elif order.get('effective') == 2:
                status = "rejected"
            
            # 构建订单详情
            order_detail = {
                "id": order['id'],
                "order_number": order.get('order_number'),
                "sample_id": order.get('sample_id'),
                "sample_name": order.get('sample_name'),
                "isbn": order.get('isbn'),
                "author": order.get('author'),
                "publisher_id": order.get('publisher_id'),
                "publisher_name": order.get('publisher_name'),
                "publisher_company_name": order.get('publisher_company_name'),
                "school_id": order.get('school_id'),
                "school_name": order.get('school_name'),
                "promotion_report_id": order.get('promotion_report_id'),
                "status": status,
                "reconciliation_status": order.get('reconciliation_status', 'pre_settlement'),
                "payment_status": order.get('payment_status', 0),
                "shipped_quantity": shipped_quantity,
                "returned_quantity": returned_quantity,
                "effective_quantity": effective_quantity,
                "unit_price": unit_price,
                "promotion_rate": round(promotion_rate, 4),
                "effective_sales_value": round(effective_sales_value, 2),
                "publisher_quantity": order.get('publisher_quantity'),
                "dealer_quantity": order.get('dealer_quantity'),
                "publisher_confirm_status": order.get('publisher_confirm_status', 'unconfirmed'),
                "dealer_confirm_status": order.get('dealer_confirm_status', 'unconfirmed'),
                "matched_order_id": order.get('matched_order_id'),
                "last_modified_by": order.get('last_modified_by'),
                "remark": order.get('remark'),
                "created_at": order['created_at'].strftime('%Y-%m-%d %H:%M:%S') if order['created_at'] else None,
                "updated_at": order['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if order['updated_at'] else None
            }
            
            # 构建订单项目详情（保持兼容性）
            order_detail['items'] = [{
                "sample_id": order.get('sample_id'),
                "sample_name": order.get('sample_name'),
                "isbn": order.get('isbn'),
                "author": order.get('author'),
                "shipped_quantity": shipped_quantity,
                "returned_quantity": returned_quantity,
                "effective_quantity": effective_quantity,
                "unit_price": unit_price
            }]
            
            return jsonify({"code": 0, "data": order_detail})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取订单详情失败: {str(e)}"})
    finally:
        connection.close()

# 取消订单
@dealer_bp.route('/cancel_order', methods=['POST'])
def cancel_order():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    if session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "只有经销商才能取消订单"})
    
    dealer_id = session.get('user_id')
    
    try:
        data = request.json
        order_id = data.get('order_id')
        
        if not order_id:
            return jsonify({"code": 1, "message": "订单ID不能为空"})
        
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 检查订单是否存在且属于该经销商
                sql = """
                    SELECT oi.id, oi.effective, oi.reject_reason
                    FROM order_items oi
                    LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                    WHERE oi.id = %s AND pr.dealer_id = %s
                """
                cursor.execute(sql, (order_id, dealer_id))
                order = cursor.fetchone()
                
                if not order:
                    return jsonify({"code": 1, "message": "未找到订单或该订单不属于您"})
                
                # 只有待处理的订单可以取消
                if order.get('effective') != 0:
                    return jsonify({"code": 1, "message": "只有待审核的订单可以取消"})
                
                # 删除订单
                delete_sql = """
                    DELETE FROM order_items 
                    WHERE id = %s
                """
                cursor.execute(delete_sql, (order_id,))
                connection.commit()
                
                return jsonify({"code": 0, "message": "订单已取消"})
                
        except Exception as e:
            connection.rollback()
            return jsonify({"code": 1, "message": f"取消订单失败: {str(e)}"})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"取消订单失败: {str(e)}"})

@dealer_bp.route('/get_book_recommendations', methods=['GET'])
def get_book_recommendations():
    """获取换版推荐列表"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取用户所属公司ID - 从users表获取dealer_company_id
                cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s AND role = 'dealer'", (user_id,))
                user_info = cursor.fetchone()
                if not user_info:
                    return jsonify({'success': False, 'message': '找不到经销商用户信息'})

                company_id = user_info['dealer_company_id']



                # 如果company_id为None，返回空列表
                if company_id is None:
                    return jsonify({'success': True, 'recommendations': []})


                sql = """
                    SELECT br.id, br.initiator_id, br.initiator_company_id, br.school_id,
                           br.original_book_id, br.original_book_supplier_id, br.replacement_reason,
                           br.replacement_reason_other, br.requirement_no_monopoly, br.requirement_recent_publish,
                           br.requirement_sufficient_stock, br.requirement_national_priority, br.requirement_other,
                           br.recommendation_type, br.status, br.referrer_id, br.created_at, br.updated_at,
                           COALESCE(NULLIF(br.school_level, ''), s.school_level) as school_level,
                           s.name as school_name, u.name as initiator_name,
                           sb.name as original_book_name, sb.isbn as original_book_isbn,
                           sb.publisher_name as original_book_publisher, sb.price as original_book_price,
                           sb.publication_date as original_book_publication_date,
                           pc.name as supplier_name,
                           (SELECT COUNT(*) FROM recommendation_results WHERE recommendation_id = br.id) as result_count,
                           (SELECT COUNT(*) FROM recommendation_results WHERE recommendation_id = br.id AND recommender_id = %s) as has_my_recommendation,
                           (SELECT COUNT(*) FROM recommendation_results WHERE recommendation_id = br.id AND is_monopoly_conflict = 1) as conflict_count,
                           (SELECT COUNT(*) FROM recommendation_results rr
                            JOIN sample_books rsb ON rr.recommended_book_id = rsb.id
                            WHERE rr.recommendation_id = br.id
                            AND br.requirement_recent_publish = 1
                            AND rsb.publication_date IS NOT NULL
                            AND rsb.publication_date < DATE_SUB(br.created_at, INTERVAL 3 YEAR)) as publication_conflict_count
                    FROM book_recommendations br
                    JOIN schools s ON br.school_id = s.id
                    JOIN users u ON br.initiator_id = u.user_id
                    JOIN sample_books sb ON br.original_book_id = sb.id
                    LEFT JOIN publisher_companies pc ON br.original_book_supplier_id = pc.id
                    WHERE (br.initiator_id = %s OR
                          (br.recommendation_type = 'internal' AND br.initiator_company_id = %s) OR
                          (br.recommendation_type = 'external' AND (br.referrer_id = %s OR br.initiator_id = %s)))
                    ORDER BY br.created_at DESC
                """
                cursor.execute(sql, (user_id, user_id, company_id, user_id, user_id))
                recommendations = cursor.fetchall()

                # 格式化日期字段
                for rec in recommendations:
                    if rec.get('original_book_publication_date'):
                        rec['original_book_publication_date'] = rec['original_book_publication_date'].strftime('%Y-%m-%d')
                    if rec.get('created_at'):
                        rec['created_at'] = rec['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    if rec.get('updated_at'):
                        rec['updated_at'] = rec['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                
                return jsonify({
                    'success': True,
                    'recommendations': recommendations,
                    'current_user_id': user_id,
                    'current_user_company_id': company_id
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/create_book_recommendation', methods=['POST'])
def create_book_recommendation():
    """创建换版推荐请求"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        data = request.json
        school_id = data.get('school_id')
        school_level = data.get('school_level')
        original_school_level = data.get('original_school_level')  # 学校原始层次
        original_book_id = data.get('original_book_id')
        supplier_id = data.get('supplier_id')
        replacement_reason = data.get('replacement_reason')
        replacement_reason_other = data.get('replacement_reason_other')
        no_monopoly = data.get('no_monopoly', False)
        recent_publish = data.get('recent_publish', False)
        sufficient_stock = data.get('sufficient_stock', False)
        national_priority = data.get('national_priority', False)
        other_requirements = data.get('other_requirements')
        recommendation_type = data.get('recommendation_type')

        # 验证必填字段
        if not all([school_id, school_level, original_book_id, supplier_id, recommendation_type]):
            return jsonify({'success': False, 'message': '请填写所有必填字段'})
        
        # 验证推荐类型
        if recommendation_type not in ['direct', 'internal', 'external']:
            return jsonify({'success': False, 'message': '无效的推荐类型'})
        
        # 如果选择"其他"原因，必须填写具体原因
        if replacement_reason == 'other' and not replacement_reason_other:
            return jsonify({'success': False, 'message': '请填写具体的换版原因'})
        
        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor() as cursor:
                # 获取用户所属公司ID - 从users表获取dealer_company_id
                cursor.execute("SELECT dealer_company_id, name FROM users WHERE user_id = %s AND role = 'dealer'", (user_id,))
                user_info = cursor.fetchone()
                if not user_info:
                    return jsonify({'success': False, 'message': '找不到经销商用户信息'})

                company_id = user_info['dealer_company_id']

                # 检查公司ID是否存在
                if not company_id:
                    return jsonify({'success': False, 'message': '用户未关联经销商公司，请联系管理员'})
                
                # 检查用户是否有发起推荐的权限
                cursor.execute("""
                    SELECT can_recommend_books FROM dealer_company_permissions
                    WHERE company_id = %s
                """, (company_id,))
                permission = cursor.fetchone()

                # 如果没有权限记录，自动创建一个并开启换版推荐权限
                if not permission:
                    cursor.execute("""
                        INSERT INTO dealer_company_permissions
                        (company_id, can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition)
                        VALUES (%s, 1, 0, 0, 0)
                    """, (company_id,))
                    connection.commit()

                elif not permission['can_recommend_books']:
                    return jsonify({'success': False, 'message': '您所在的单位没有发起换版推荐的权限，请联系管理员开启权限'})

                # 判断用户是否修改了学校层次
                # 如果用户修改了学校层次，则存储用户输入的值；否则存储空值
                stored_school_level = school_level if school_level != original_school_level else ''

                # 创建推荐记录
                sql = """
                    INSERT INTO book_recommendations
                    (initiator_id, initiator_company_id, school_id, school_level, original_book_id,
                     original_book_supplier_id, replacement_reason, replacement_reason_other,
                     requirement_no_monopoly, requirement_recent_publish, requirement_sufficient_stock,
                     requirement_national_priority, requirement_other, recommendation_type, status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                # 设置初始状态 - 所有推荐都是推荐中状态
                status = 'in_progress'



                cursor.execute(sql, (
                    user_id, company_id, school_id, stored_school_level, original_book_id,
                    supplier_id, replacement_reason, replacement_reason_other,
                    no_monopoly, recent_publish, sufficient_stock, national_priority,
                    other_requirements, recommendation_type, status
                ))
                recommendation_id = cursor.lastrowid



                connection.commit()
                return jsonify({'success': True, 'recommendation_id': recommendation_id, 'message': '换版推荐请求创建成功'})
                
        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/get_recommendation_detail', methods=['GET'])
def get_recommendation_detail():
    """获取换版推荐详情"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        recommendation_id = request.args.get('id')
        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取用户所属公司ID - 从users表获取dealer_company_id
                cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s AND role = 'dealer'", (user_id,))
                user_info = cursor.fetchone()
                if not user_info:
                    return jsonify({'success': False, 'message': '找不到经销商用户信息'})

                company_id = user_info['dealer_company_id']
                
                # 获取推荐详情，包含原用教材的费率信息
                sql = """
                    SELECT br.id, br.initiator_id, br.initiator_company_id, br.school_id,
                           br.original_book_id, br.original_book_supplier_id, br.replacement_reason,
                           br.replacement_reason_other, br.requirement_no_monopoly, br.requirement_recent_publish,
                           br.requirement_sufficient_stock, br.requirement_national_priority, br.requirement_other,
                           br.recommendation_type, br.status, br.referrer_id, br.created_at, br.updated_at,
                           COALESCE(NULLIF(br.school_level, ''), s.school_level) as school_level,
                           s.name as school_name, u.name as initiator_name,
                           sb.name as original_book_name, sb.isbn as original_book_isbn,
                           sb.author as original_book_author, sb.publisher_name as original_book_publisher,
                           sb.price as original_book_price, sb.publication_date as original_book_publication_date,
                           sb.shipping_discount as original_book_shipping_discount,
                           sb.settlement_discount as original_book_settlement_discount,
                           sb.promotion_rate as original_book_promotion_rate,
                           pc.name as supplier_name, dc.name as initiator_company_name
                    FROM book_recommendations br
                    JOIN schools s ON br.school_id = s.id
                    JOIN users u ON br.initiator_id = u.user_id
                    JOIN sample_books sb ON br.original_book_id = sb.id
                    LEFT JOIN publisher_companies pc ON br.original_book_supplier_id = pc.id
                    LEFT JOIN dealer_companies dc ON br.initiator_company_id = dc.id
                    WHERE br.id = %s
                """
                cursor.execute(sql, (recommendation_id,))
                recommendation = cursor.fetchone()
                
                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在'})
                
                # 检查用户是否有权限查看此推荐
                is_initiator = recommendation['initiator_id'] == user_id
                is_same_company = recommendation['initiator_company_id'] == company_id
                is_referrer = recommendation.get('referrer_id') == user_id

                # 权限检查：发起人、同公司内部推荐、外部推荐的转荐人或发起人
                has_permission = (
                    is_initiator or
                    (recommendation['recommendation_type'] == 'internal' and is_same_company) or
                    (recommendation['recommendation_type'] == 'external' and (is_referrer or is_initiator))
                )

                if not has_permission:
                    return jsonify({'success': False, 'message': '您无权查看此推荐'})

                # 获取经销商的加点值并调整原用教材费率（与报备管理页面保持一致）
                customer_level, point_value = get_dealer_level_and_point(cursor, user_id)

                # 调整原用教材的费率
                if point_value is not None:
                    # 调整结算折扣
                    if recommendation.get('original_book_settlement_discount') is not None:
                        settlement_discount = float(recommendation['original_book_settlement_discount'])
                        point_value_decimal = point_value / 100.0
                        recommendation['original_book_settlement_discount'] = round(settlement_discount + point_value_decimal, 4)

                    # 调整推广费率
                    if recommendation.get('original_book_promotion_rate') is not None:
                        promotion_rate = float(recommendation['original_book_promotion_rate'])
                        point_value_decimal = point_value / 100.0
                        recommendation['original_book_promotion_rate'] = round(max(0, promotion_rate - point_value_decimal), 4)
                    elif recommendation.get('original_book_shipping_discount') is not None and recommendation.get('original_book_settlement_discount') is not None:
                        # 如果没有设置推广费率，则默认为发货折扣-结算折扣
                        shipping_discount = float(recommendation['original_book_shipping_discount'])
                        settlement_discount = float(recommendation['original_book_settlement_discount'])
                        recommendation['original_book_promotion_rate'] = round(shipping_discount - settlement_discount, 4)

                # 获取推荐结果，并计算出版时间冲突
                sql = """
                    SELECT rr.*, sb.name as book_name, sb.author, sb.isbn, sb.publisher_name,
                           sb.price, sb.publication_date, u.name as recommender_name,
                           CASE
                               WHEN u.role = 'publisher' THEN pc.name
                               WHEN u.role = 'dealer' THEN dc.name
                               ELSE u.name
                           END as recommender_company,
                           CASE
                               WHEN br.requirement_recent_publish = 1
                                    AND sb.publication_date IS NOT NULL
                                    AND sb.publication_date < DATE_SUB(br.created_at, INTERVAL 3 YEAR)
                               THEN 1
                               ELSE 0
                           END as is_publication_conflict
                    FROM recommendation_results rr
                    JOIN sample_books sb ON rr.recommended_book_id = sb.id
                    JOIN users u ON rr.recommender_id = u.user_id
                    JOIN book_recommendations br ON rr.recommendation_id = br.id
                    LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                    LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                    WHERE rr.recommendation_id = %s
                    ORDER BY rr.created_at ASC
                """
                cursor.execute(sql, (recommendation_id,))
                results = cursor.fetchall()
                
                # 格式化日期字段
                if recommendation.get('original_book_publication_date'):
                    recommendation['original_book_publication_date'] = recommendation['original_book_publication_date'].strftime('%Y-%m-%d')
                if recommendation.get('created_at'):
                    recommendation['created_at'] = recommendation['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                if recommendation.get('updated_at'):
                    recommendation['updated_at'] = recommendation['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                
                for result in results:
                    if result.get('publication_date'):
                        result['publication_date'] = result['publication_date'].strftime('%Y-%m-%d')
                    if result.get('created_at'):
                        result['created_at'] = result['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    if result.get('updated_at'):
                        result['updated_at'] = result['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                
                # 检查用户是否已经推荐过
                cursor.execute("""
                    SELECT COUNT(*) as count FROM recommendation_results
                    WHERE recommendation_id = %s AND recommender_id = %s
                """, (recommendation_id, user_id))
                my_recommendation_count = cursor.fetchone()['count']

                # 添加操作权限
                recommendation['is_initiator'] = is_initiator
                recommendation['can_recommend'] = (
                    recommendation['status'] == 'in_progress' and
                    my_recommendation_count == 0 and
                    ((recommendation['recommendation_type'] == 'internal' and is_same_company and not is_initiator) or
                     (recommendation['recommendation_type'] == 'direct' and is_initiator))
                )
                recommendation['can_modify_recommendation'] = (
                    recommendation['status'] == 'in_progress' and
                    my_recommendation_count > 0 and
                    ((recommendation['recommendation_type'] == 'internal' and is_same_company and not is_initiator) or
                     (recommendation['recommendation_type'] == 'direct' and is_initiator))
                )
                
                return jsonify({
                    'success': True, 
                    'recommendation': recommendation, 
                    'results': results
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 提交推荐结果（经销商和出版社推荐样书）
@dealer_bp.route('/submit_batch_recommendation_results', methods=['POST'])
def submit_batch_recommendation_results():
    """批量提交推荐结果"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.json
        recommendations = data.get('recommendations', [])

        if not recommendations:
            return jsonify({'success': False, 'message': '推荐数据不能为空'})

        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                success_count = 0
                failed_count = 0
                duplicate_books = []

                for rec_data in recommendations:
                    recommendation_id = rec_data.get('recommendation_id')
                    recommended_book_id = rec_data.get('recommended_book_id')
                    stock_quantity = rec_data.get('stock_quantity')
                    notes = rec_data.get('notes')

                    # 获取推荐详情并验证
                    cursor.execute("""
                        SELECT br.* FROM book_recommendations br
                        WHERE br.id = %s AND br.recommendation_type = 'internal'
                        AND br.status = 'in_progress'
                    """, (recommendation_id,))
                    recommendation = cursor.fetchone()

                    if not recommendation:
                        failed_count += 1
                        continue

                    # 检查是否已经推荐过这本书
                    cursor.execute("""
                        SELECT id FROM recommendation_results
                        WHERE recommendation_id = %s AND recommender_id = %s AND recommended_book_id = %s
                    """, (recommendation_id, user_id, recommended_book_id))
                    existing = cursor.fetchone()

                    if existing:
                        # 获取样书信息用于提示
                        cursor.execute("SELECT name FROM sample_books WHERE id = %s", (recommended_book_id,))
                        book = cursor.fetchone()
                        duplicate_books.append(book['name'] if book else f'ID:{recommended_book_id}')
                        failed_count += 1
                        continue

                    # 检查包销冲突
                    is_monopoly_conflict = False
                    if recommendation['requirement_no_monopoly']:
                        cursor.execute("""
                            SELECT pc.is_publisher FROM sample_books sb
                            LEFT JOIN users u ON sb.publisher_id = u.user_id
                            LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                            WHERE sb.id = %s
                        """, (recommended_book_id,))
                        book_publisher_info = cursor.fetchone()

                        if book_publisher_info and not book_publisher_info.get('is_publisher'):
                            is_monopoly_conflict = True

                    # 出版时间冲突检查已移到查询时实时计算，不需要在插入时存储

                    # 插入推荐结果（出版时间冲突不需要存储，在查询时实时计算）
                    cursor.execute("""
                        INSERT INTO recommendation_results
                        (recommendation_id, recommender_id, recommended_book_id, stock_quantity,
                         notes, is_monopoly_conflict, status)
                        VALUES (%s, %s, %s, %s, %s, %s, 'pending')
                    """, (recommendation_id, user_id, recommended_book_id, stock_quantity,
                          notes, is_monopoly_conflict))
                    success_count += 1

                connection.commit()

                if duplicate_books:
                    message = f'成功推荐 {success_count} 本样书'
                    if duplicate_books:
                        message += f'，{len(duplicate_books)} 本样书已推荐过：{", ".join(duplicate_books)}'
                    return jsonify({'success': True, 'message': message, 'partial': True})
                else:
                    return jsonify({'success': True, 'message': f'成功推荐 {success_count} 本样书'})

        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/submit_recommendation_result', methods=['POST'])
def submit_recommendation_result():
    """提交推荐结果"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        data = request.json
        recommendation_id = data.get('recommendation_id')
        recommended_book_id = data.get('recommended_book_id')
        stock_quantity = data.get('stock_quantity')
        notes = data.get('notes')
        
        if not all([recommendation_id, recommended_book_id]):
            return jsonify({'success': False, 'message': '请填写必要的推荐信息'})
        
        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取推荐详情
                cursor.execute("""
                    SELECT br.*, dc.name as company_name
                    FROM book_recommendations br
                    LEFT JOIN dealer_companies dc ON br.initiator_company_id = dc.id
                    WHERE br.id = %s
                """, (recommendation_id,))
                recommendation = cursor.fetchone()
                
                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在'})
                
                # 检查推荐状态
                if recommendation['status'] == 'ended':
                    return jsonify({'success': False, 'message': '该推荐已结束，无法继续推荐'})
                
                # 检查是否需要库存数量
                if recommendation['requirement_sufficient_stock'] and not stock_quantity:
                    return jsonify({'success': False, 'message': '该推荐要求填写库存数量'})
                
                # 检查包销冲突
                is_monopoly_conflict = False
                if recommendation['requirement_no_monopoly']:
                    # 检查被推荐样书的出版社类型
                    cursor.execute("""
                        SELECT pc.is_publisher FROM sample_books sb
                        LEFT JOIN users u ON sb.publisher_id = u.user_id
                        LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                        WHERE sb.id = %s
                    """, (recommended_book_id,))
                    book_publisher_info = cursor.fetchone()

                    # 如果样书的出版社不是"仅为出版社"，则为包销书，存在冲突
                    if book_publisher_info and not book_publisher_info.get('is_publisher'):
                        is_monopoly_conflict = True

                # 出版时间冲突检查已移到查询时实时计算，不需要在插入时存储

                # 插入推荐结果
                cursor.execute("""
                    INSERT INTO recommendation_results
                    (recommendation_id, recommender_id, recommended_book_id, stock_quantity,
                     notes, is_monopoly_conflict, status)
                    VALUES (%s, %s, %s, %s, %s, %s, 'pending')
                """, (recommendation_id, user_id, recommended_book_id, stock_quantity,
                      notes, is_monopoly_conflict))
                
                # 推荐状态保持为 in_progress（推荐中）
                cursor.execute("""
                    UPDATE book_recommendations
                    SET updated_at = NOW()
                    WHERE id = %s
                """, (recommendation_id,))
                
                connection.commit()
                return jsonify({'success': True, 'message': '推荐提交成功'})
                
        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 获取出版社单位列表（供应商选择）  
@dealer_bp.route('/get_supplier_companies', methods=['GET'])
def get_supplier_companies():
    """获取出版社单位列表"""
    try:
        search = request.args.get('search', '')
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = "SELECT id, name FROM publisher_companies WHERE 1=1"
                params = []
                
                if search:
                    sql += " AND name LIKE %s"
                    params.append(f'%{search}%')
                
                sql += " ORDER BY name"
                cursor.execute(sql, params)
                companies = cursor.fetchall()
                
                return jsonify({'success': True, 'companies': companies})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/get_publisher_companies', methods=['GET'])
def get_publisher_companies():
    """获取出版社公司列表（换版推荐用）"""
    try:
        search = request.args.get('search', '')
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = "SELECT id, name FROM publisher_companies WHERE 1=1"
                params = []
                
                if search:
                    sql += " AND name LIKE %s"
                    params.append(f'%{search}%')
                
                sql += " ORDER BY name LIMIT 50"
                cursor.execute(sql, params)
                companies = cursor.fetchall()
                
                return jsonify({'success': True, 'companies': companies})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/get_school_info', methods=['GET'])
def get_school_info():
    """获取学校信息（包括默认学校层次）"""
    try:
        school_id = request.args.get('school_id')
        if not school_id:
            return jsonify({'success': False, 'message': '缺少学校ID'})

        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = """
                    SELECT id, name, school_level, city
                    FROM schools
                    WHERE id = %s
                """
                cursor.execute(sql, (school_id,))
                school = cursor.fetchone()

                if not school:
                    return jsonify({'success': False, 'message': '学校不存在'})

                return jsonify({'success': True, 'school': school})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 样书选择器接口
@dealer_bp.route('/search_sample_books', methods=['GET'])
def search_sample_books():
    """搜索样书用于选择"""
    try:
        search = request.args.get('search', '')
        publisher_id = request.args.get('publisher_id')
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = """
                    SELECT sb.id, sb.name, sb.author, sb.isbn, sb.publisher_name, 
                           sb.price, sb.publication_date, u.name as publisher_user_name
                    FROM sample_books sb
                    LEFT JOIN users u ON sb.publisher_id = u.user_id
                    WHERE 1=1
                """
                params = []
                
                if search:
                    sql += """ AND (sb.name LIKE %s OR sb.author LIKE %s 
                              OR sb.isbn LIKE %s OR sb.publisher_name LIKE %s)"""
                    params.extend([f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'])
                
                if publisher_id:
                    sql += " AND sb.publisher_id = %s"
                    params.append(publisher_id)
                
                sql += " ORDER BY sb.name LIMIT 50"
                cursor.execute(sql, params)
                books = cursor.fetchall()
                
                # 格式化日期
                for book in books:
                    if book.get('publication_date'):
                        book['publication_date'] = book['publication_date'].strftime('%Y-%m-%d')
                
                return jsonify({'success': True, 'books': books})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 检查用户推荐权限
@dealer_bp.route('/check_recommendation_permission', methods=['GET'])
def check_recommendation_permission():
    """检查用户是否有换版推荐权限"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 首先获取用户基本信息 - 从users表获取dealer_company_id
                cursor.execute("SELECT dealer_company_id, name FROM users WHERE user_id = %s AND role = 'dealer'", (user_id,))
                user_info = cursor.fetchone()

                if not user_info:
                    return jsonify({'success': True, 'has_permission': False, 'reason': '未找到经销商用户信息，请联系管理员为您分配经销商公司'})

                company_id = user_info['dealer_company_id']

                # 检查公司ID是否存在
                if not company_id:
                    return jsonify({'success': True, 'has_permission': False, 'reason': '用户未关联经销商公司，请联系管理员'})

                # 获取公司名称
                cursor.execute("SELECT name FROM dealer_companies WHERE id = %s", (company_id,))
                company_info = cursor.fetchone()
                company_name = company_info['name'] if company_info else '未知公司'

                # 获取公司权限
                cursor.execute("""
                    SELECT can_recommend_books FROM dealer_company_permissions
                    WHERE company_id = %s
                """, (company_id,))
                permission = cursor.fetchone()

                # 如果没有权限记录，自动创建一个并开启换版推荐权限
                if not permission:
                    cursor.execute("""
                        INSERT INTO dealer_company_permissions
                        (company_id, can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition)
                        VALUES (%s, 1, 0, 0, 0)
                    """, (company_id,))
                    connection.commit()
                    has_permission = True

                else:
                    has_permission = bool(permission['can_recommend_books'])

                return jsonify({
                    'success': True,
                    'has_permission': has_permission,
                    'company_name': company_name,
                    'reason': '您所在的单位没有换版推荐权限，请联系管理员开启权限' if not has_permission else ''
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/get_my_recommendations', methods=['GET'])
def get_my_recommendations():
    """获取我的推荐结果"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        recommendation_id = request.args.get('recommendation_id')
        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})

        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取我的推荐结果
                cursor.execute("""
                    SELECT rr.id, rr.recommended_book_id, rr.stock_quantity, rr.notes,
                           rr.is_monopoly_conflict, rr.status,
                           sb.name as book_name, sb.author, sb.isbn, sb.price
                    FROM recommendation_results rr
                    JOIN sample_books sb ON rr.recommended_book_id = sb.id
                    WHERE rr.recommendation_id = %s AND rr.recommender_id = %s
                    ORDER BY rr.created_at DESC
                """, (recommendation_id, user_id))
                recommendations = cursor.fetchall()

                return jsonify({'success': True, 'recommendations': recommendations})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/remove_recommendation_result', methods=['POST'])
def remove_recommendation_result():
    """删除推荐结果"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.json
        result_id = data.get('result_id')

        if not result_id:
            return jsonify({'success': False, 'message': '缺少推荐结果ID'})

        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查推荐结果是否存在且属于当前用户
                cursor.execute("""
                    SELECT id FROM recommendation_results
                    WHERE id = %s AND recommender_id = %s
                """, (result_id, user_id))
                result = cursor.fetchone()

                if not result:
                    return jsonify({'success': False, 'message': '推荐结果不存在或无权删除'})

                # 删除推荐结果
                cursor.execute("""
                    DELETE FROM recommendation_results
                    WHERE id = %s AND recommender_id = %s
                """, (result_id, user_id))

                connection.commit()
                return jsonify({'success': True, 'message': '推荐删除成功'})

        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/refer_recommendation', methods=['POST'])
def refer_recommendation():
    """转荐功能：将内部推荐转为外部推荐"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.json
        recommendation_id = data.get('recommendation_id')

        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})

        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取用户公司信息
                cursor.execute("""
                    SELECT dealer_company_id FROM users WHERE user_id = %s
                """, (user_id,))
                user_info = cursor.fetchone()
                if not user_info:
                    return jsonify({'success': False, 'message': '用户信息不存在'})

                user_company_id = user_info['dealer_company_id']

                # 检查推荐是否存在且用户是否有权限操作（发起人或同单位内部推荐权限）
                cursor.execute("""
                    SELECT * FROM book_recommendations
                    WHERE id = %s AND (
                        initiator_id = %s OR
                        (recommendation_type = 'internal' AND initiator_company_id = %s)
                    )
                """, (recommendation_id, user_id, user_company_id))
                recommendation = cursor.fetchone()

                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在或您无权操作'})

                # 检查推荐类型是否为内部推荐
                if recommendation['recommendation_type'] != 'internal':
                    return jsonify({'success': False, 'message': '只能转荐内部推荐'})

                # 检查推荐状态是否为推荐中
                if recommendation['status'] != 'in_progress':
                    return jsonify({'success': False, 'message': '只能转荐进行中的推荐'})

                # 更新推荐类型为外部推荐，并设置转荐人
                cursor.execute("""
                    UPDATE book_recommendations
                    SET recommendation_type = 'external', referrer_id = %s
                    WHERE id = %s
                """, (user_id, recommendation_id))

                connection.commit()
                return jsonify({'success': True, 'message': '转荐成功'})

        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@dealer_bp.route('/end_recommendation', methods=['POST'])
def end_recommendation():
    """结束推荐（只有发起人可以操作）"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.json
        recommendation_id = data.get('recommendation_id')

        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})

        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查推荐是否存在且是否为发起人
                cursor.execute("""
                    SELECT initiator_id, status FROM book_recommendations
                    WHERE id = %s
                """, (recommendation_id,))
                recommendation = cursor.fetchone()

                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在'})

                if recommendation['initiator_id'] != user_id:
                    return jsonify({'success': False, 'message': '只有发起人可以结束推荐'})

                if recommendation['status'] == 'ended':
                    return jsonify({'success': False, 'message': '推荐已经结束'})

                # 更新推荐状态为已结束
                cursor.execute("""
                    UPDATE book_recommendations
                    SET status = 'ended', updated_at = NOW()
                    WHERE id = %s
                """, (recommendation_id,))

                connection.commit()
                return jsonify({'success': True, 'message': '推荐已结束'})

        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})



@dealer_bp.route('/cancel_recommendation_result', methods=['POST'])
def cancel_recommendation_result():
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        data = request.json
        recommendation_id = data.get('recommendation_id')
        
        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})
        
        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取推荐详情
                sql = """
                    SELECT * FROM book_recommendations WHERE id = %s
                """
                cursor.execute(sql, (recommendation_id,))
                recommendation = cursor.fetchone()
                
                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在'})
                
                # 检查是否有该推荐的结果记录
                sql = """
                    SELECT * FROM recommendation_results 
                    WHERE recommendation_id = %s AND recommender_id = %s
                """
                cursor.execute(sql, (recommendation_id, user_id))
                results = cursor.fetchall()
                
                if not results:
                    return jsonify({'success': False, 'message': '您没有此推荐的结果记录'})
                
                # 删除推荐结果
                sql = """
                    DELETE FROM recommendation_results 
                    WHERE recommendation_id = %s AND recommender_id = %s
                """
                cursor.execute(sql, (recommendation_id, user_id))
                
                # 检查是否还有其他人的推荐结果
                sql = """
                    SELECT COUNT(*) as count FROM recommendation_results 
                    WHERE recommendation_id = %s
                """
                cursor.execute(sql, (recommendation_id,))
                count = cursor.fetchone()['count']
                
                # 推荐状态保持为 in_progress（推荐中），不需要改回待处理状态
                
                connection.commit()
                return jsonify({'success': True, 'message': '成功取消推荐结果'})
        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 辅助函数，获取经销商级别和加点值
def get_dealer_level_and_point(cursor, user_id):
    """
    获取经销商的客户级别和加点值
    返回:
        (customer_level, point_value) 元组
    """
    query = """
    SELECT customer_level, point_value
    FROM dealers
    WHERE user_id = %s
    """
    cursor.execute(query, (user_id,))
    dealer_info = cursor.fetchone()
    
    if not dealer_info:
        return None, None
    
    customer_level = dealer_info.get('customer_level', 3)
    point_value = dealer_info.get('point_value')
    
    # 如果加点值为空，则按规则计算
    if point_value is None:
        if customer_level <= 3:
            point_value = 3
        else:
            point_value = customer_level
    
    return customer_level, point_value

@dealer_bp.route('/get_report_counts', methods=['GET'])
def get_report_counts():
    """
    获取各状态报备的数量
    返回:
        各状态的报备数量
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    user_id = session.get('user_id')
    
    connection = get_db_connection()
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询各状态报备数量
            sql = """
            SELECT 
                COUNT(*) as all_count,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN status = 'approved' OR status LIKE 'approved_%' THEN 1 ELSE 0 END) as approved_count,
                SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count
            FROM promotion_reports
            WHERE dealer_id = %s
            """
            cursor.execute(sql, (user_id,))
            result = cursor.fetchone()
            
            if result:
                counts = {
                    'all': result['all_count'] or 0,
                    'pending': result['pending_count'] or 0,
                    'approved': result['approved_count'] or 0,
                    'rejected': result['rejected_count'] or 0
                }
            else:
                counts = {
                    'all': 0,
                    'pending': 0,
                    'approved': 0,
                    'rejected': 0
                }
                
            return jsonify({"code": 0, "message": "获取成功", "data": counts})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报备数量失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/confirm_publisher_quantity', methods=['POST'])
def confirm_publisher_quantity():
    """
    经销商确认或否定出版社的数量
    请求数据:
        order_id: 订单ID
        action: 操作类型('confirm' 或 'reject')
        confirm_quantity: 确认的数量 (如果是确认操作)
    返回:
        处理结果
    """
    if 'user_id' not in session or session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    dealer_id = session['user_id']
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({"code": 1, "message": "请提供请求数据"})
    
    order_id = data.get('order_id')
    action = data.get('action')  # 'confirm' 或 'reject'
    confirm_quantity = data.get('confirm_quantity')  # 只有当action为confirm时使用
    
    if not order_id or not action:
        return jsonify({"code": 1, "message": "参数不完整"})
    
    if action not in ['confirm', 'reject']:
        return jsonify({"code": 1, "message": "无效的操作类型"})
    
    if action == 'confirm' and confirm_quantity is None:
        return jsonify({"code": 1, "message": "确认操作需要提供数量"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取订单信息
            sql = """
                SELECT oi.*, matched.id as matched_order_id, matched.shipped_quantity as publisher_shipped_quantity
                FROM order_items oi
                JOIN order_items matched ON oi.matched_order_id = matched.id
                WHERE oi.id = %s AND oi.from_dealer = 1
            """
            cursor.execute(sql, (order_id,))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或不是经销商订单"})
            
            publisher_order_id = order['matched_order_id']
            
            if action == 'confirm':
                # 确认出版社数量
                try:
                    confirm_quantity = int(confirm_quantity)
                    if confirm_quantity < 0:
                        return jsonify({"code": 1, "message": "确认数量不能为负数"})
                except:
                    return jsonify({"code": 1, "message": "确认数量必须是整数"})
                
                # 更新经销商订单
                update_dealer_sql = """
                    UPDATE order_items
                    SET shipped_quantity = %s, dealer_quantity = %s, publisher_quantity = %s, 
                        dealer_confirm_status = 'confirmed',
                        reconciliation_status = 'pending_payment',
                        last_modified_by = 'dealer',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_dealer_sql, (confirm_quantity, confirm_quantity, confirm_quantity, order_id))
                
                # 更新出版社订单
                update_publisher_sql = """
                    UPDATE order_items
                    SET shipped_quantity = %s, dealer_quantity = %s,
                        reconciliation_status = 'pending_payment',
                        last_modified_by = 'dealer',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_publisher_sql, (confirm_quantity, confirm_quantity, publisher_order_id))
                
                # 更新订单匹配记录
                update_match_sql = """
                    UPDATE order_matches
                    SET reconciliation_status = 'pending_payment'
                    WHERE publisher_order_id = %s AND dealer_order_id = %s
                """
                cursor.execute(update_match_sql, (publisher_order_id, order_id))
                
                # 添加对账历史记录
                history_sql = """
                    INSERT INTO order_reconciliation_history
                    (order_id, user_id, user_role, action_type, old_quantity, new_quantity, remark)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(history_sql, (
                    order_id,
                    dealer_id,
                    'dealer',
                    'confirm',
                    order['shipped_quantity'],
                    confirm_quantity,
                    '经销商确认接受出版社数量'
                ))
                
                message = "已确认出版社数量并更新为待支付状态"
            else:
                # 否定出版社数量
                update_dealer_sql = """
                    UPDATE order_items
                    SET dealer_confirm_status = 'rejected',
                        last_modified_by = 'dealer',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_dealer_sql, (order_id,))
                
                # 更新出版社订单
                update_publisher_sql = """
                    UPDATE order_items
                    SET dealer_confirm_status = 'rejected',
                        last_modified_by = 'dealer',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_publisher_sql, (publisher_order_id,))
                
                # 添加对账历史记录
                history_sql = """
                    INSERT INTO order_reconciliation_history
                    (order_id, user_id, user_role, action_type, remark)
                    VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(history_sql, (
                    order_id,
                    dealer_id,
                    'dealer',
                    'reject',
                    '经销商否定出版社数量'
                ))
                
                message = "已否定出版社数量，等待协商"
            
            connection.commit()
            return jsonify({"code": 0, "message": message})
            
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"处理失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/mark_order_paid', methods=['POST'])
def mark_order_paid():
    """
    经销商标记订单为已支付
    请求数据:
        order_id: 订单ID
    返回:
        处理结果
    """
    if 'user_id' not in session or session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    dealer_id = session['user_id']
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({"code": 1, "message": "请提供请求数据"})
    
    order_id = data.get('order_id')
    
    if not order_id:
        return jsonify({"code": 1, "message": "订单ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取订单信息
            sql = """
                SELECT oi.*, matched.id as matched_order_id
                FROM order_items oi
                JOIN order_items matched ON oi.matched_order_id = matched.id
                WHERE oi.id = %s AND oi.from_dealer = 1
            """
            cursor.execute(sql, (order_id,))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或不是经销商订单"})
            
            # 检查订单是否为待支付状态
            if order['reconciliation_status'] != 'pending_payment':
                return jsonify({"code": 1, "message": "只有待支付状态的订单可以标记为已支付"})
            
            publisher_order_id = order['matched_order_id']
            
            # 更新经销商订单
            update_dealer_sql = """
                UPDATE order_items
                SET payment_status = 1,
                    reconciliation_status = 'settled',
                    updated_at = NOW()
                WHERE id = %s
            """
            cursor.execute(update_dealer_sql, (order_id,))
            
            # 更新出版社订单
            update_publisher_sql = """
                UPDATE order_items
                SET payment_status = 1,
                    reconciliation_status = 'settled',
                    updated_at = NOW()
                WHERE id = %s
            """
            cursor.execute(update_publisher_sql, (publisher_order_id,))
            
            # 更新订单匹配记录
            update_match_sql = """
                UPDATE order_matches
                SET reconciliation_status = 'settled'
                WHERE publisher_order_id = %s AND dealer_order_id = %s
            """
            cursor.execute(update_match_sql, (publisher_order_id, order_id))
            
            # 添加对账历史记录
            history_sql = """
                INSERT INTO order_reconciliation_history
                (order_id, user_id, user_role, action_type, remark)
                VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(history_sql, (
                order_id,
                dealer_id,
                'dealer',
                'payment',
                '经销商标记订单已支付'
            ))
            
            connection.commit()
            return jsonify({"code": 0, "message": "订单已标记为已支付"})
            
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"处理失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/modify_order_quantity', methods=['POST'])
def modify_order_quantity():
    """
    经销商修改订单数量
    请求数据:
        order_id: 订单ID
        quantity: 新数量
    返回:
        处理结果
    """
    if 'user_id' not in session or session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    dealer_id = session['user_id']
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({"code": 1, "message": "请提供请求数据"})
    
    order_id = data.get('order_id')
    quantity = data.get('quantity')
    
    if not order_id or quantity is None:
        return jsonify({"code": 1, "message": "参数不完整"})
    
    try:
        quantity = int(quantity)
        if quantity < 0:
            return jsonify({"code": 1, "message": "数量不能为负数"})
    except:
        return jsonify({"code": 1, "message": "数量必须是整数"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取订单信息
            sql = """
                SELECT oi.*, matched.id as matched_order_id, matched.shipped_quantity as publisher_shipped_quantity
                FROM order_items oi
                LEFT JOIN order_items matched ON oi.matched_order_id = matched.id
                WHERE oi.id = %s AND oi.from_dealer = 1
            """
            cursor.execute(sql, (order_id,))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或不是经销商订单"})
            
            old_quantity = order['shipped_quantity']
            publisher_quantity = order.get('publisher_shipped_quantity')
            
            # 判断是否与出版社数量一致
            is_quantity_matched = (publisher_quantity is not None and quantity == publisher_quantity)
            
            # 设置新的状态
            if is_quantity_matched:
                # 数量一致，自动进入待支付状态
                new_reconciliation_status = 'pending_payment'
                dealer_confirm_status = 'confirmed'
                publisher_confirm_status = 'confirmed'
            else:
                # 数量不一致，回到预结算状态
                new_reconciliation_status = 'pre_settlement'
                dealer_confirm_status = 'confirmed'
                publisher_confirm_status = 'unconfirmed'
            
            # 更新经销商订单数量
            update_sql = """
                UPDATE order_items
                SET shipped_quantity = %s,
                    dealer_quantity = %s,
                    dealer_confirm_status = %s,
                    publisher_confirm_status = %s, 
                    reconciliation_status = %s,
                    last_modified_by = 'dealer',
                    updated_at = NOW()
                WHERE id = %s
            """
            cursor.execute(update_sql, (quantity, quantity, dealer_confirm_status, 
                                      publisher_confirm_status, new_reconciliation_status, order_id))
            
            # 如果订单已匹配，更新出版社订单的状态
            if order['matched_order_id']:
                publisher_order_id = order['matched_order_id']
                
                update_publisher_sql = """
                    UPDATE order_items
                    SET dealer_quantity = %s,
                        dealer_confirm_status = %s,
                        publisher_confirm_status = %s, 
                        reconciliation_status = %s,
                        last_modified_by = 'dealer',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_publisher_sql, (quantity, dealer_confirm_status, 
                                                    publisher_confirm_status, new_reconciliation_status, publisher_order_id))
                
                # 更新订单匹配记录
                update_match_sql = """
                    UPDATE order_matches
                    SET reconciliation_status = %s
                    WHERE publisher_order_id = %s AND dealer_order_id = %s
                """
                cursor.execute(update_match_sql, (new_reconciliation_status, publisher_order_id, order_id))
                
                # 添加对账历史记录
                history_sql = """
                    INSERT INTO order_reconciliation_history
                    (order_id, user_id, user_role, action_type, old_quantity, new_quantity, remark)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
            
                if is_quantity_matched:
                    remark = f'经销商修改订单数量至{quantity}，与出版社数量一致，自动进入待支付状态'
                else:
                    remark = f'经销商修改订单数量至{quantity}'
                
                cursor.execute(history_sql, (
                    order_id,
                    dealer_id,
                    'dealer',
                    'modify',
                    old_quantity,
                    quantity,
                    remark
                ))
            
            connection.commit()
            
            if is_quantity_matched:
                return jsonify({"code": 0, "message": "订单数量已修改，与出版社数量一致，已自动进入待支付状态"})
            else:
                return jsonify({"code": 0, "message": "订单数量已修改"})
            
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"修改失败: {str(e)}"})
    finally:
        connection.close()

@dealer_bp.route('/get_order_reconciliation_history', methods=['GET'])
def get_order_reconciliation_history():
    """
    获取订单对账历史
    请求参数:
        order_id: 订单ID
    返回:
        对账历史记录
    """
    if 'user_id' not in session or session.get('role') != 'dealer':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    dealer_id = session['user_id']
    order_id = request.args.get('order_id')
    
    if not order_id:
        return jsonify({"code": 1, "message": "订单ID不能为空"})
    
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 验证订单是否属于当前经销商 - 支持有报备和无报备的订单
            sql = """
                SELECT oi.id, pr.dealer_id
                FROM order_items oi
                LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                WHERE oi.id = %s AND oi.from_dealer = 1 AND (
                    (oi.promotion_report_id IS NOT NULL AND pr.dealer_id = %s) OR
                    (oi.promotion_report_id IS NULL AND EXISTS (
                        SELECT 1 FROM order_reconciliation_history orh 
                        WHERE orh.order_id = oi.id AND orh.user_id = %s AND orh.user_role = 'dealer'
                    ))
                )
            """
            cursor.execute(sql, (order_id, dealer_id, dealer_id))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或无权访问"})
            
            # 查询订单本身和匹配订单的对账历史
            sql = """
                SELECT orh.id, orh.order_id, orh.user_id, orh.user_role, orh.action_type,
                       orh.old_quantity, orh.new_quantity, orh.remark, orh.created_at,
                       u.name as user_name, 
                       CASE 
                           WHEN orh.user_role = 'publisher' THEN p.name 
                           WHEN orh.user_role = 'dealer' THEN d.name 
                           ELSE u.name 
                       END as company_name
                FROM order_reconciliation_history orh
                JOIN users u ON orh.user_id = u.user_id
                LEFT JOIN publisher_companies p ON u.publisher_company_id = p.id
                LEFT JOIN dealer_companies d ON u.dealer_company_id = d.id
                WHERE orh.order_id = %s OR orh.order_id IN (
                    SELECT matched_order_id FROM order_items WHERE id = %s
                )
                ORDER BY orh.created_at DESC
            """
            cursor.execute(sql, (order_id, order_id))
            history = cursor.fetchall()
            
            # 格式化时间
            for record in history:
                if record['created_at']:
                    record['created_at'] = record['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            
            return jsonify({"code": 0, "data": history})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取对账历史失败: {str(e)}"})
    finally:
        connection.close()

# 获取样书列表（用于换版推荐功能）
@dealer_bp.route('/get_sample_books', methods=['GET'])
def get_sample_books():
    """获取样书列表用于换版推荐功能"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        search = request.args.get('search', '')
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = """
                    SELECT sb.id, sb.name, sb.author, sb.isbn, sb.publisher_name, 
                           sb.price, sb.publication_date, sb.level, sb.book_type,
                           sb.material_type, sb.national_regulation, sb.provincial_regulation,
                           nrl.name as national_regulation_level_name,
                           prl.name as provincial_regulation_level_name,
                           (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ') 
                            FROM sample_book_features sbf 
                            JOIN book_features bf ON sbf.feature_id = bf.id 
                            WHERE sbf.sample_id = sb.id) as feature_name
                    FROM sample_books sb
                    LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                    LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                    WHERE 1=1
                """
                params = []
                
                if search:
                    sql += """ AND (sb.name LIKE %s OR sb.author LIKE %s 
                              OR sb.isbn LIKE %s OR sb.publisher_name LIKE %s)"""
                    params.extend([f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'])
                
                sql += " ORDER BY sb.name LIMIT 500"
                cursor.execute(sql, params)
                books = cursor.fetchall()
                
                # 格式化日期
                for book in books:
                    if book.get('publication_date'):
                        book['publication_date'] = book['publication_date'].strftime('%Y-%m-%d')
                
                return jsonify({'success': True, 'books': books})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})