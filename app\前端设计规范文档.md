# 前端设计规范文档

## 概述

本文档旨在为团队提供统一的前端设计规范，确保产品界面的一致性和用户体验的连贯性。我们采用现代化简约设计理念，参考 Penguin UI 组件风格，构建美观、实用的用户界面。

## 技术栈

### 核心框架
- **CSS 框架**: Tailwind CSS v3+ `<link rel="stylesheet" href="/`static/css/tailwind.css">` 
- **JavaScript 框架**: Alpine.js v3+ `<script defer src="/static/js/alpine.min.js"></script>`
- **图标库**: Font Awesome 6+ `<link rel="stylesheet" href="/static/css/font-awesome-all.min.css">`
- **jQuery**: `<script src="/static/jquery.js"></script>`
- **字体**: 系统默认字体栈

### 设计系统
- **设计风格**: 现代化简约设计
- **参考组件库**: [Penguin UI](https://www.penguinui.com/components)
- **色彩系统**: Tailwind CSS 默认调色板
- **圆角系统**: 统一使用 `rounded-xl` (12px)
- **阴影系统**: 分层阴影设计

## 设计原则

### 1. 简约至上
- 页面顶部均不需要页面标题
- 减少不必要的视觉元素
- 保持界面整洁清晰
- 突出重要信息

### 2. 一致性
- 统一的组件样式
- 一致的交互模式
- 规范的间距系统

### 3. 响应式
- 移动端优先设计
- 弹性布局系统
- 适配各种屏幕尺寸

### 4. 用户体验
- 流畅的动画过渡
- 明确的状态反馈
- 直观的操作反馈

## 设计规范

### 色彩系统

#### 主色调
```css
/* 蓝色系 - 主要操作 */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* 绿色系 - 成功/确认 */
.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* 紫色系 - 特殊功能 */
.btn-purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

/* 红色系 - 警告/删除 */
.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}
```

#### 中性色调
```css
/* 背景色 */
.bg-primary: bg-gradient-to-br from-slate-50 to-blue-50
.bg-card: bg-white/80 backdrop-blur-sm
.bg-overlay: rgba(0, 0, 0, 0.5) + backdrop-filter: blur(8px)

/* 文本色 */
.text-primary: text-slate-800
.text-secondary: text-slate-600
.text-muted: text-slate-500
.text-light: text-slate-400
```

### 间距系统

```css
/* 组件间距 */
.spacing-xs: 0.5rem (8px)
.spacing-sm: 0.75rem (12px)
.spacing-md: 1rem (16px)
.spacing-lg: 1.5rem (24px)
.spacing-xl: 2rem (32px)
.spacing-2xl: 3rem (48px)

/* 内边距 */
.padding-card: p-6 (24px)
.padding-button: px-6 py-3 (24px 12px)
.padding-input: px-4 py-3 (16px 12px)
```

### 圆角系统

```css
.radius-sm: rounded-lg (8px)
.radius-md: rounded-xl (12px)
.radius-lg: rounded-2xl (16px)
.radius-full: rounded-full (50%)
```

### Z-index 层级系统

```css
/* Z-index 层级规范 */
.z-base: 1          /* 基础层级 */
.z-dropdown: 10     /* 普通下拉菜单 */
.z-sticky: 20       /* 粘性定位元素 */
.z-fixed: 30        /* 固定定位元素 */
.z-modal-backdrop: 50    /* 模态框背景 */
.z-modal: 60        /* 模态框内容 */
.z-popover: 70      /* 弹出层 */
.z-tooltip: 80      /* 提示框 */
.z-notification: 90 /* 通知消息 */
.z-max: 9999        /* 最高层级（如搜索下拉框） */
```

#### 层级使用指南
- **基础层级 (1-9)**: 普通内容、卡片悬停效果
- **功能层级 (10-49)**: 下拉菜单、粘性导航、固定元素
- **模态层级 (50-89)**: 模态框、弹出层、提示框
- **系统层级 (90-9999)**: 通知、加载遮罩、搜索下拉框等关键UI组件

## 通用组件

### 1. 按钮组件

#### 主要按钮
```html
<button class="btn-primary h-12 px-6 text-white rounded-xl flex items-center space-x-2 shadow-lg">
    <i class="fas fa-plus"></i>
    <span>添加内容</span>
</button>
```

#### 样式定义
```css
.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    transition: all 0.3s ease;
}
.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
}
```

#### 按钮变体
- **主要操作**: `btn-primary` (蓝色渐变)
- **成功操作**: `btn-success` (绿色渐变)
- **特殊操作**: `btn-purple` (紫色渐变)
- **危险操作**: `btn-danger` (红色渐变)
- **次要操作**: `bg-slate-100 text-slate-700`

### 2. 模态框组件

#### 基础模态框结构
```html
<div id="modalContainer" class="fixed inset-0 z-50 hidden">
    <div class="modal-overlay flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
            <div class="flex items-center justify-between p-6 border-b border-slate-200">
                <h3 id="modalTitle" class="text-xl font-semibold text-slate-800"></h3>
                <button onclick="closeModal()"
                        class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
            <div id="modalBody" class="p-6 overflow-y-auto max-h-[70vh] custom-scrollbar"></div>
        </div>
    </div>
</div>
```

#### 固定高度模态框（推荐用于内容较多的场景）
```html
<div id="fixedHeightModal" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4">
        <!-- 固定高度的模态框容器 -->
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-5xl overflow-hidden flex flex-col" style="height: 600px;">
            <!-- 模态框头部 - 固定高度 -->
            <div class="flex items-center justify-between px-6 py-4 border-b border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0" style="height: 80px;">
                <div class="flex items-center space-x-3">
                    <button onclick="closeModal()"
                            class="w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                        <i class="fas fa-arrow-left text-sm"></i>
                    </button>
                    <div>
                        <h3 class="text-lg font-bold text-slate-800">模态框标题</h3>
                        <p class="text-xs text-slate-500">副标题信息</p>
                    </div>
                </div>
                <button onclick="closeModal()"
                        class="w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>

            <!-- 模态框内容 - 剩余高度，可滚动 -->
            <div class="flex-1 overflow-hidden" style="height: 520px;">
                <div class="flex h-full">
                    <!-- 左侧固定区域（可选） -->
                    <div class="w-80 flex-shrink-0 modal-left-panel border-r border-slate-200" style="height: 520px;">
                        <div class="flex flex-col h-full justify-center p-6">
                            <!-- 左侧内容 -->
                        </div>
                    </div>

                    <!-- 右侧滚动区域 -->
                    <div class="flex-1 modal-content-area scroll-indicator" style="height: 520px;" x-data="{ scrollHintVisible: true }">
                        <!-- 滚动提示 -->
                        <div class="scroll-hint" x-show="scrollHintVisible" x-transition>
                            <i class="fas fa-chevron-down text-xs"></i>
                            <span>向下滚动查看更多</span>
                        </div>
                        <div class="h-full overflow-y-auto custom-scrollbar p-6 modal-scroll-content"
                             @scroll="handleScroll($event)"
                             x-ref="scrollContainer">
                            <!-- 滚动内容 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
```

#### 模态框样式定义
```css
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 50;
}

/* 模态框左侧面板样式 */
.modal-left-panel {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modal-content-area {
    background: #ffffff;
}

/* 确保滚动区域有足够的内边距 */
.modal-scroll-content {
    padding-bottom: 2rem;
}

/* 滚动提示样式 */
.scroll-indicator {
    position: relative;
}

.scroll-indicator::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.9));
    pointer-events: none;
    opacity: 1;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.scroll-indicator.scrolled-to-bottom::after {
    opacity: 0;
}

.scroll-hint {
    position: absolute;
    bottom: 10px;
    right: 20px;
    background: rgba(59, 130, 246, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    animation: bounce 2s infinite;
    z-index: 20;
    transition: opacity 0.3s ease;
}

.scroll-hint.hidden {
    opacity: 0;
    pointer-events: none;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}
```

#### JavaScript 控制
```javascript
function openModal(title, content) {
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalBody').innerHTML = content;
    document.getElementById('modalContainer').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('modalContainer').classList.add('hidden');
}

// 处理滚动事件（用于滚动提示功能）
function handleScroll(event) {
    const container = event.target;
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;

    // 检查是否有可滚动内容
    const hasScrollableContent = scrollHeight > clientHeight;

    // 计算滚动进度
    const scrollProgress = hasScrollableContent ? scrollTop / (scrollHeight - clientHeight) : 1;

    // 只有在有可滚动内容且滚动进度小于20%时才显示提示
    const shouldShowScrollHint = hasScrollableContent && scrollProgress < 0.2;

    // 更新滚动提示显示状态
    const scrollIndicator = container.closest('.scroll-indicator');
    const scrollHint = scrollIndicator.querySelector('.scroll-hint');

    // 使用Alpine.js的数据绑定更新状态
    const alpineComponent = Alpine.$data(scrollIndicator);
    if (alpineComponent) {
        alpineComponent.scrollHintVisible = shouldShowScrollHint;
    }

    // 同时更新CSS类，确保状态一致
    if (shouldShowScrollHint) {
        scrollHint.classList.remove('hidden');
    } else {
        scrollHint.classList.add('hidden');
    }

    // 更新底部渐变遮罩 - 只有在有可滚动内容时才显示
    if (hasScrollableContent && scrollProgress >= 0.95) {
        scrollIndicator.classList.add('scrolled-to-bottom');
    } else if (!hasScrollableContent) {
        // 没有可滚动内容时，直接隐藏底部遮罩
        scrollIndicator.classList.add('scrolled-to-bottom');
    } else {
        scrollIndicator.classList.remove('scrolled-to-bottom');
    }
}

// 检查内容是否需要滚动（在内容加载完成后调用）
function checkScrollableContent() {
    const container = document.querySelector('.modal-scroll-content');
    if (container) {
        // 先确保提示是隐藏的
        const scrollIndicator = container.closest('.scroll-indicator');
        const scrollHint = scrollIndicator.querySelector('.scroll-hint');
        const alpineComponent = Alpine.$data(scrollIndicator);

        if (alpineComponent) {
            alpineComponent.scrollHintVisible = false;
        }
        scrollHint.classList.add('hidden');

        // 延迟检查，确保DOM完全渲染
        setTimeout(() => {
            handleScroll({ target: container });
        }, 50);
    }
}
```

#### 模态框滚动提示功能

##### 功能说明
滚动提示功能用于告知用户模态框内容区域还有更多内容可以滚动查看，特别适用于内容较多的详情页面。

##### 核心特性
1. **默认隐藏**：滚动提示默认隐藏状态，避免误导用户
2. **智能显示检测**：只有当内容高度超过容器高度时才显示滚动提示
3. **动态滚动提示**：右下角显示"向下滚动查看更多"的提示
4. **底部渐变遮罩**：底部有渐变遮罩提示还有内容
5. **智能隐藏**：滚动超过20%时自动隐藏提示
6. **到底检测**：滚动到底部时隐藏底部遮罩
7. **内容完整显示**：当内容已完全显示时，不显示任何滚动提示

##### 使用场景
- 样书详情模态框
- 用户信息详情页面
- 长表单或长列表展示
- 任何需要滚动查看完整内容的模态框

##### 实现步骤

###### 1. HTML 结构要求
```html
<!-- 滚动容器必须包含以下结构 -->
<div class="modal-content-area scroll-indicator" x-data="{ scrollHintVisible: false }">
    <!-- 滚动提示元素 - 默认隐藏 -->
    <div class="scroll-hint hidden" x-show="scrollHintVisible" x-transition>
        <i class="fas fa-chevron-down text-xs"></i>
        <span>向下滚动查看更多</span>
    </div>

    <!-- 实际滚动区域 -->
    <div class="h-full overflow-y-auto custom-scrollbar p-6 modal-scroll-content"
         @scroll="handleScroll($event)"
         x-ref="scrollContainer">
        <!-- 你的内容 -->
    </div>
</div>
```

###### 2. CSS 样式（已包含在模态框样式中）
- `.scroll-indicator`：滚动指示器容器
- `.scroll-hint`：滚动提示样式，带弹跳动画
- `::after` 伪元素：底部渐变遮罩

###### 3. JavaScript 事件处理
在 Alpine.js 组件中添加 `handleScroll` 方法，或在全局作用域中定义该函数。

**重要**：在内容加载完成后，必须调用 `checkScrollableContent()` 函数来检查初始状态：
```javascript
// 在内容加载完成后调用
setTimeout(() => {
    this.checkScrollableContent();
}, 100);
```

##### 自定义配置

###### 修改提示文案
```html
<div class="scroll-hint" x-show="scrollHintVisible" x-transition>
    <i class="fas fa-chevron-down text-xs"></i>
    <span>还有更多内容</span> <!-- 自定义文案 -->
</div>
```

###### 调整触发阈值
```javascript
// 在 handleScroll 函数中修改阈值
const scrollHintVisible = scrollProgress < 0.3; // 改为30%时隐藏
```

###### 修改提示位置
```css
.scroll-hint {
    /* 默认右下角 */
    bottom: 10px;
    right: 20px;

    /* 改为左下角 */
    /* bottom: 10px; */
    /* left: 20px; */

    /* 改为居中底部 */
    /* bottom: 10px; */
    /* left: 50%; */
    /* transform: translateX(-50%); */
}
```

##### 最佳实践

1. **内容长度检测**：只在内容确实需要滚动时显示提示
2. **提示时机**：在模态框打开后适当延迟显示提示
3. **动画效果**：使用 Alpine.js 的 `x-transition` 实现平滑过渡
4. **响应式适配**：在移动端可考虑调整提示样式和位置

##### 注意事项

1. **容器高度**：确保滚动容器有明确的高度限制
2. **内容检测**：避免在内容不足一屏时显示滚动提示
3. **性能考虑**：滚动事件处理要避免过度频繁的DOM操作
4. **可访问性**：考虑为滚动提示添加适当的 aria 属性

### 3. 消息通知组件

#### 使用方法
```javascript
showMessage('操作成功', 'success');
showMessage('操作失败', 'error');
showMessage('请注意', 'warning');
showMessage('提示信息', 'info');
```

#### 实现代码
```javascript
function showMessage(text, type = 'info') {
    const id = ++messageId;
    const container = document.getElementById('messageContainer');
    
    const messageEl = document.createElement('div');
    messageEl.id = `message-${id}`;
    messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
        type === 'success' ? 'border-green-500' : 
        type === 'error' ? 'border-red-500' : 
        type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
    }`;
    
    messageEl.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                type === 'success' ? 'text-green-500' : 
                type === 'error' ? 'text-red-500' : 
                type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
            }">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' : 
                    type === 'error' ? 'fa-exclamation-circle' : 
                    type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                }"></i>
            </div>
            <div class="flex-1">
                <p class="text-sm font-medium text-slate-800">${text}</p>
            </div>
            <button onclick="removeMessage(${id})" 
                    class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    `;
    
    container.appendChild(messageEl);
    
    // 动画显示
    setTimeout(() => {
        messageEl.classList.remove('translate-x-full', 'opacity-0');
    }, 10);
    
    // 5秒后自动移除
    setTimeout(() => {
        removeMessage(id);
    }, 5000);
}
```

### 4. 卡片组件

#### 基础卡片结构
```html
<article class="sample-card bg-white rounded-2xl shadow-sm border border-slate-100 overflow-hidden hover:border-slate-200">
    <div class="sample-card-content">
        <!-- 卡片头部 -->
        <div class="p-6 pb-4">
            <h3 class="text-lg font-semibold text-slate-800 line-clamp-2 leading-tight mb-2">标题</h3>
            <p class="text-sm text-slate-500">副标题</p>
        </div>
        
        <!-- 卡片主体 -->
        <div class="sample-card-body px-6 pb-4">
            <!-- 内容区域 -->
        </div>
    </div>
</article>
```

#### 卡片样式
```css
.sample-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    display: flex;
    flex-direction: column;
}
.sample-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.sample-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.sample-card-body {
    flex: 1;
}
```

### 5. 表单组件

#### 输入框
```html
<input type="text" 
       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
       placeholder="请输入内容...">
```

#### 选择框
```html
<select class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
    <option value="">请选择</option>
</select>
```

#### 自定义搜索下拉框组件

##### HTML 结构
```html
<div class="custom-select" id="selectContainer">
    <div class="custom-select-trigger" id="selectTrigger">
        <span class="custom-select-text">请选择选项</span>
        <i class="fas fa-chevron-down custom-select-arrow"></i>
    </div>
    <div class="custom-select-dropdown">
        <div class="custom-select-search">
            <input type="text" placeholder="搜索选项..." id="selectSearch">
        </div>
        <div class="custom-select-options" id="selectOptions">
            <!-- 选项将动态生成 -->
        </div>
    </div>
</div>
```

##### CSS 样式
```css
/* 自定义搜索下拉框样式 */
.custom-select {
    position: relative;
}

.custom-select-trigger {
    width: 100%;
    height: 40px;
    padding: 8px 40px 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    background-color: white;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.custom-select-trigger:hover {
    border-color: #9ca3af;
}

.custom-select-trigger:focus,
.custom-select.active .custom-select-trigger {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.custom-select-trigger.disabled {
    background-color: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed;
    border-color: #e5e7eb;
}

.custom-select-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.2s ease;
    color: #6b7280;
    font-size: 12px;
}

.custom-select.active .custom-select-arrow {
    transform: translateY(-50%) rotate(180deg);
}

.custom-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index:1000;
    max-height: 240px;
    overflow: hidden;
    margin-top: 4px;
    opacity: 0;
    transform: translateY(-10px);
    visibility: hidden;
    transition: all 0.2s ease;
}

.custom-select.active .custom-select-dropdown {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
}

.custom-select-search {
    padding: 8px 12px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.custom-select-search input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 13px;
    outline: none;
    transition: border-color 0.2s ease;
}

.custom-select-search input:focus {
    border-color: #3b82f6;
}

.custom-select-options {
    max-height: 180px;
    overflow-y: auto;
}

.custom-select-option {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    transition: background-color 0.15s ease;
}

.custom-select-option:hover {
    background-color: #f3f4f6;
}

.custom-select-option.selected {
    background-color: #3b82f6;
    color: white;
}

.custom-select-option.no-results {
    color: #9ca3af;
    cursor: default;
    text-align: center;
    padding: 16px 12px;
}

/* 滚动条样式 */
.custom-select-options::-webkit-scrollbar {
    width: 4px;
}

.custom-select-options::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
}

.custom-select-options::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}
```

##### JavaScript 类
```javascript
class CustomSelect {
    constructor(containerId, options = {}) {
        this.container = $('#' + containerId);
        this.trigger = this.container.find('.custom-select-trigger');
        this.dropdown = this.container.find('.custom-select-dropdown');
        this.searchInput = this.container.find('.custom-select-search input');
        this.optionsContainer = this.container.find('.custom-select-options');
        this.textSpan = this.trigger.find('.custom-select-text');
        
        this.options = [];
        this.selectedValue = '';
        this.selectedText = '';
        this.placeholder = options.placeholder || '请选择';
        this.disabled = options.disabled || false;
        this.onSelect = options.onSelect || null;
        
        this.init();
    }
    
    init() {
        // 绑定事件
        this.trigger.on('click', (e) => {
            if (!this.disabled) {
                this.toggle();
            }
        });
        
        // 搜索功能
        this.searchInput.on('input', (e) => {
            this.filterOptions(e.target.value);
        });
        
        // 点击选项
        this.optionsContainer.on('click', '.custom-select-option:not(.no-results)', (e) => {
            const option = $(e.target);
            const value = option.data('value');
            const text = option.text();
            this.selectOption(value, text);
        });
        
        // 点击外部关闭
        $(document).on('click', (e) => {
            if (!this.container.is(e.target) && this.container.has(e.target).length === 0) {
                this.close();
            }
        });
    }
    
    setOptions(options) {
        this.options = options;
        this.renderOptions();
    }
    
    setValue(value) {
        const option = this.options.find(opt => opt.value === value);
        if (option) {
            this.selectOption(value, option.text);
        }
    }
    
    getValue() {
        return this.selectedValue;
    }
    
    getText() {
        return this.selectedText;
    }
    
    reset() {
        this.selectedValue = '';
        this.selectedText = '';
        this.textSpan.text(this.placeholder);
        this.searchInput.val('');
        this.renderOptions();
        this.close();
    }
    
    updatePlaceholder(newPlaceholder) {
        this.placeholder = newPlaceholder;
        if (!this.selectedValue) {
            this.textSpan.text(this.placeholder);
        }
    }
    
    setDisabled(disabled) {
        this.disabled = disabled;
        if (disabled) {
            this.trigger.addClass('disabled');
            this.close();
        } else {
            this.trigger.removeClass('disabled');
        }
    }
    
    // 其他方法...
}
```

##### 使用示例

###### 单个搜索下拉框（推荐用法）
```javascript
// 基础单个选择器
const statusSelect = new CustomSelect('statusContainer', {
    placeholder: '请选择状态',
    onSelect: function(value, text) {
        console.log('选择了状态:', value, text);
        // 处理选择逻辑
    }
});

// 设置选项
statusSelect.setOptions([
    {value: 'pending', text: '待审核'},
    {value: 'approved', text: '已通过'},
    {value: 'rejected', text: '已拒绝'}
]);

// 动态更新选项
function updateOptions() {
    statusSelect.setOptions([
        {value: 'draft', text: '草稿'},
        {value: 'published', text: '已发布'}
    ]);
}

// 设置默认值
statusSelect.setValue('pending');

// 获取当前选择
const currentValue = statusSelect.getValue();
const currentText = statusSelect.getText();

// 重置选择
statusSelect.reset();

// 更新占位符文本（动态改变提示文字）
statusSelect.updatePlaceholder('请选择新状态');

// 禁用/启用
statusSelect.setDisabled(true);
statusSelect.setDisabled(false);
```

###### 三级联动示例（复杂用法）
```javascript
// 三级联动省市区选择器
const provinceSelect = new CustomSelect('provinceContainer', {
    placeholder: '请选择省份',
    onSelect: function(provinceId) {
        loadCities(provinceId);
    }
});

const citySelect = new CustomSelect('cityContainer', {
    placeholder: '请选择城市',
    disabled: true,
    onSelect: function(cityId) {
        loadDistricts(cityId);
    }
});

const districtSelect = new CustomSelect('districtContainer', {
    placeholder: '请选择区县',
    disabled: true,
    onSelect: function(districtId) {
        console.log('最终选择:', districtId);
    }
});

function loadCities(provinceId) {
    // 重置下级选择
    citySelect.reset();
    citySelect.setDisabled(true);
    districtSelect.reset();
    districtSelect.setDisabled(true);
    
    if (provinceId) {
        // 加载城市数据
        const cityOptions = getCitiesByProvince(provinceId);
        citySelect.setOptions(cityOptions);
        citySelect.setDisabled(false);
        // 更新占位符文本
        citySelect.updatePlaceholder('请选择城市');
    }
}

function loadDistricts(cityId) {
    districtSelect.reset();
    districtSelect.setDisabled(true);
    
    if (cityId) {
        // 加载区县数据
        const districtOptions = getDistrictsByCity(cityId);
        districtSelect.setOptions(districtOptions);
        districtSelect.setDisabled(false);
        // 更新占位符文本
        districtSelect.updatePlaceholder('请选择区县');
    }
}
```

##### 组件特性
- **现代化设计**: 12px圆角、渐变阴影、平滑动画
- **搜索功能**: 实时搜索过滤选项
- **键盘支持**: ESC键关闭、自动聚焦
- **响应式**: 适配各种屏幕尺寸
- **状态管理**: 禁用/启用状态切换
- **三级联动**: 支持级联选择场景
- **模态框兼容**: 在模态框内正常显示和使用

##### 设计规范
- **高度**: 40px 统一高度
- **圆角**: 12px 圆角保持一致性
- **边框**: 1px #d1d5db 默认边框
- **聚焦状态**: 蓝色边框 + 浅蓝色外发光
- **禁用状态**: 灰色背景 + 禁用光标
- **动画**: 200ms 平滑过渡动画
- **阴影**: 分层阴影增强立体感

#### 搜索框
```html
<div class="search-container">
    <input type="text" 
           placeholder="搜索..." 
           class="w-80 h-12 pl-4 pr-12 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
</div>
```

#### 文件上传组件

##### 基础文件上传（参考 Penguin UI）
```html
<div class="border-2 border-dashed border-slate-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors bg-slate-50">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true" fill="currentColor" class="w-12 h-12 mx-auto mb-4 text-slate-400">
        <path fill-rule="evenodd" d="M10.5 3.75a6 6 0 0 0-5.98 6.496A5.25 5.25 0 0 0 6.75 20.25H18a4.5 4.5 0 0 0 2.206-8.423 3.75 3.75 0 0 0-4.133-4.303A6.001 6.001 0 0 0 10.5 3.75Zm2.03 5.47a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 1 0 1.06 1.06l1.72-1.72v4.94a.75.75 0 0 0 1.5 0v-4.94l1.72 1.72a.75.75 0 1 0 1.06-1.06l-3-3Z" clip-rule="evenodd"/>
    </svg>
    <div>
        <label for="fileInput" class="cursor-pointer inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
            <input id="fileInput" type="file" accept=".xlsx,.xls" class="sr-only" />
            <i class="fas fa-upload mr-2"></i>选择文件
        </label>
        <span class="ml-2 text-slate-600">或拖拽文件到此处</span>
    </div>
    <small class="block mt-2 text-slate-500">支持 .xlsx, .xls 格式 - 最大 10MB</small>
</div>
```

##### 图片上传组件
```html
<div class="border-2 border-dashed border-slate-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors bg-slate-50">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true" fill="currentColor" class="w-8 h-8 mx-auto mb-3 text-slate-400">
        <path fill-rule="evenodd" d="M1.5 6a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 19.5 6v12a2.25 2.25 0 0 1-2.25 2.25H3.75A2.25 2.25 0 0 1 1.5 18V6ZM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0 0 21 18v-1.94l-2.69-2.689a1.5 1.5 0 0 0-2.12 0l-.88.879.97.97a.75.75 0 1 1-1.06 1.06l-5.16-5.159a1.5 1.5 0 0 0-2.12 0L3 16.061Zm10.125-7.81a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z" clip-rule="evenodd"/>
    </svg>
    <div>
        <label for="imageInput" class="cursor-pointer inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
            <input id="imageInput" type="file" accept=".jpg,.jpeg,.png,.webp" class="sr-only" />
            <i class="fas fa-upload mr-2"></i>选择图片
        </label>
        <span class="ml-2 text-slate-600 text-sm">或拖拽图片到此处</span>
    </div>
    <small class="block mt-2 text-slate-500">支持 JPG, PNG, WebP 格式 - 最大 5MB</small>
</div>
```

##### 文件上传样式规范
- **容器**: 虚线边框 `border-2 border-dashed border-slate-300`
- **圆角**: 统一使用 `rounded-xl` (12px)
- **背景**: 浅灰背景 `bg-slate-50`
- **悬停效果**: 边框变蓝 `hover:border-blue-400`
- **图标**: 使用 SVG 图标，大小 `w-12 h-12` (文件) 或 `w-8 h-8` (图片)
- **按钮**: 蓝色背景，白色文字，悬停变深
- **输入框**: 隐藏原生输入框 `sr-only`
- **提示文案**: 小号字体 `text-sm`，灰色 `text-slate-600`
- **格式说明**: 更小字体，浅灰色 `text-slate-500`

##### 使用场景
- **Excel 文件上传**: 批量导入功能
- **图片上传**: 封面、头像等
- **文档上传**: PDF、Word 等文档类型

##### 参考来源
文件上传组件设计参考 [Penguin UI File Input](https://www.penguinui.com/components/file-input)，保持与现代化 UI 设计的一致性。

### 6. 标签组件

#### 状态标签
```html
<span class="tag tag-primary">主要</span>
<span class="tag tag-success">成功</span>
<span class="tag tag-warning">警告</span>
<span class="tag tag-purple">特殊</span>
<span class="tag tag-gray">默认</span>
```

#### 标签样式
```css
.tag {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}
.tag-primary { background: #dbeafe; color: #1d4ed8; }
.tag-success { background: #d1fae5; color: #065f46; }
.tag-warning { background: #fef3c7; color: #92400e; }
.tag-purple { background: #ede9fe; color: #6d28d9; }
.tag-gray { background: #f1f5f9; color: #475569; }
```

### 7. 模式切换组件

#### 标签页式切换
```html
<div class="flex bg-slate-100 rounded-lg p-1">
    <button @click="switchMode('directory')"
            :class="['flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all', 
                     currentMode === 'directory' ? 'bg-white text-blue-600 shadow-sm' : 'text-slate-600 hover:text-slate-800']">
        <i class="fas fa-folder mr-2"></i>目录模式
    </button>
    <button @click="switchMode('filter')"
            :class="['flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all', 
                     currentMode === 'filter' ? 'bg-white text-blue-600 shadow-sm' : 'text-slate-600 hover:text-slate-800']">
        <i class="fas fa-search mr-2"></i>高级筛选
    </button>
</div>
```

#### 样式特点
- 背景使用 `bg-slate-100` 圆角容器
- 激活状态：白色背景 + 蓝色文字 + 阴影
- 非激活状态：透明背景 + 灰色文字 + 悬停效果
- 图标与文字组合提升可读性

### 8. 筛选组件

#### 可折叠筛选项
```html
<div class="border border-slate-200 rounded-lg overflow-hidden">
    <button type="button"
            @click="collapsed = !collapsed"
            class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
        <span class="text-sm font-medium text-slate-700">筛选类别</span>
        <i class="fas fa-chevron-down text-sm transition-transform duration-200"
           :class="{'rotate-180': !collapsed}"></i>
    </button>
    <div x-show="!collapsed" x-transition class="p-3 border-t border-slate-200">
        <div class="space-y-2">
            <!-- 筛选选项 -->
        </div>
    </div>
</div>
```

#### 复选框筛选项
```html
<label class="flex items-center">
    <input type="checkbox" name="category" value="option" @change="applyFilters"
           class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
    <span class="ml-2 text-sm text-slate-700">选项名称</span>
</label>
```

#### 搜索型筛选
```html
<div class="mb-2">
    <input type="text" 
           x-model="searchKeyword" 
           placeholder="搜索选项..."
           class="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
</div>
<div class="space-y-2 max-h-40 overflow-y-auto">
    <!-- 过滤后的选项列表 -->
</div>
```

### 9. 侧边栏导航组件

#### 结构设计
```html
<div class="w-80 bg-white/80 backdrop-blur-sm border-r border-slate-200/60 flex flex-col">
    <!-- 标题栏 -->
    <div class="p-6 border-b border-slate-200/60">
        <!-- 标题和模式切换 -->
    </div>
    
    <!-- 动态内容区 -->
    <div class="flex-1 overflow-hidden">
        <!-- 根据模式显示不同内容 -->
    </div>
</div>
```

#### 设计特点
- 固定宽度 `w-80` (320px)
- 毛玻璃效果背景 `bg-white/80 backdrop-blur-sm`
- 顶部标题区固定，内容区可滚动
- 边框使用半透明设计增强层次感

### 富文本编辑器组件

#### 概述
富文本编辑器基于Quill.js 2.0+实现，支持文本格式化、图片插入、全屏编辑等功能。适用于需要富文本内容编辑的场景，如文章编辑、描述输入等。

#### 资源引用
```html
<!-- CSS文件 -->
<link href="/static/css/quill2.snow.css" rel="stylesheet">

<!-- JavaScript文件 -->
<script src="/static/js/quill2.js"></script>
```

#### 基本HTML结构
```html
<div>
    <label class="block text-sm font-medium text-slate-700 mb-2">内容标题</label>
    <div class="quill-wrapper">
        <!-- 全屏编辑按钮 -->
        <button type="button" class="expand-button" @click="openFullscreenEditor()" title="全屏编辑">
            <i class="fas fa-expand text-slate-600"></i>
        </button>
        <!-- 编辑器容器 -->
        <div id="editor-container" class="bg-white"></div>
    </div>
    <p class="text-xs text-slate-500 mt-1">支持富文本格式，可插入图片，点击右上角按钮可全屏编辑</p>
</div>
```

#### CSS样式规范

##### 基础编辑器样式
```css
/* Quill编辑器基础样式 */
.ql-editor {
    min-height: 120px;
    font-size: 14px;
    line-height: 1.6;
}

.ql-editor.ql-blank::before {
    opacity: var(--placeholder-opacity, 1);
    transition: opacity 0.3s ease;
}

.ql-toolbar {
    border-top: 1px solid #d1d5db;
    border-left: 1px solid #d1d5db;
    border-right: 1px solid #d1d5db;
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
}

.ql-container {
    border-bottom: 1px solid #d1d5db;
    border-left: 1px solid #d1d5db;
    border-right: 1px solid #d1d5db;
    border-bottom-left-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
}

.ql-editor:focus {
    outline: none;
}

.quill-wrapper:focus-within .ql-toolbar,
.quill-wrapper:focus-within .ql-container {
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
}

/* 图片默认样式 */
.ql-editor img {
    max-width: 22%;
    height: auto;
    width: 22%;
    display: block;
    margin: 0.5em auto;
}
```

##### 全屏按钮样式
```css
/* 全屏按钮 - 模仿Quill工具栏按钮样式 */
.expand-button {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 28px;
    height: 28px;
    background: transparent;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: background-color 0.15s ease;
}

.expand-button:hover {
    background-color: #e6e6e6;
}

.expand-button:active {
    background-color: #ccc;
}

.expand-button i {
    color: #444;
    font-size: 11px;
    line-height: 1;
}

.quill-wrapper {
    position: relative;
}
```

##### 全屏编辑器样式
```css
/* 全屏编辑器覆盖层 */
.fullscreen-editor-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.fullscreen-editor-container {
    background: white;
    border-radius: 1rem;
    width: 100%;
    max-width: 1200px;
    height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.fullscreen-editor-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top-left-radius: 1rem;
    border-top-right-radius: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.fullscreen-editor-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.fullscreen-editor .ql-toolbar {
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-top: none;
}

.fullscreen-editor .ql-container {
    flex: 1;
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-bottom: none;
}

.fullscreen-editor .ql-editor {
    min-height: auto;
    height: 100%;
    font-size: 16px;
    line-height: 1.6;
    padding: 1.5rem;
}
```

#### JavaScript实现规范

##### 基础配置
```javascript
// Alpine.js组件数据
{
    // 编辑器实例
    descriptionEditor: null,
    fullscreenEditor: null,
    isFullscreenMode: false,

    // 初始化方法
    async initialize() {
        // 确保DOM完全渲染后再初始化
        await this.$nextTick();
        setTimeout(() => {
            this.initializeQuillEditor();
        }, 200);
    }
}
```

##### 编辑器初始化
```javascript
initializeQuillEditor() {
    // 检查容器元素是否存在
    const editorContainer = document.getElementById('editor-container');
    if (!editorContainer) {
        console.warn('Quill编辑器容器未找到，延迟重试...');
        setTimeout(() => {
            this.initializeQuillEditor();
        }, 200);
        return;
    }

    const toolbarOptions = [
        [{ 'header': [1, 2, 3, false] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'align': [] }],
        ['link', 'image'],
        ['clean']
    ];

    try {
        this.descriptionEditor = new Quill('#editor-container', {
            theme: 'snow',
            placeholder: '请输入内容...',
            modules: {
                toolbar: {
                    container: toolbarOptions,
                    handlers: {
                        image: this.handleImageUpload.bind(this)
                    }
                }
            }
        });

        // 监听内容变化
        this.descriptionEditor.on('text-change', () => {
            this.form.description = this.descriptionEditor.root.innerHTML;
        });

        // 优化placeholder显示
        this.descriptionEditor.on('selection-change', (range) => {
            if (range) {
                const editor = this.descriptionEditor.root;
                if (editor.classList.contains('ql-blank')) {
                    editor.style.setProperty('--placeholder-opacity', '0');
                }
            }
        });

        console.log('Quill编辑器初始化成功');
    } catch (error) {
        console.error('Quill编辑器初始化失败:', error);
        setTimeout(() => {
            this.initializeQuillEditor();
        }, 500);
    }
}
```

##### 图片上传处理
```javascript
async handleImageUpload() {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.click();

    input.onchange = async () => {
        const file = input.files[0];
        if (file) {
            try {
                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch('/api/common/upload/image', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 获取当前编辑器实例（普通或全屏）
                    const currentEditor = this.isFullscreenMode ? this.fullscreenEditor : this.descriptionEditor;

                    if (currentEditor) {
                        try {
                            // 确保编辑器有焦点
                            currentEditor.focus();

                            // 使用多重策略插入图片
                            const length = currentEditor.getLength();
                            currentEditor.insertEmbed(length - 1, 'image', result.data.url);
                            currentEditor.insertText(length, '\n');

                            showMessage('图片上传成功', 'success');
                        } catch (error) {
                            console.error('插入图片时发生错误:', error);
                            showMessage('图片插入失败，但文件已上传成功', 'warning');
                        }
                    }
                } else {
                    showMessage(result.message || '图片上传失败', 'error');
                }
            } catch (error) {
                console.error('图片上传失败:', error);
                showMessage('图片上传失败，请稍后重试', 'error');
            }
        }
    };
}
```

##### 全屏编辑功能
```javascript
// 打开全屏编辑器
openFullscreenEditor() {
    const overlay = document.createElement('div');
    overlay.className = 'fullscreen-editor-overlay';
    overlay.id = 'fullscreen-overlay';

    overlay.innerHTML = `
        <div class="fullscreen-editor-container">
            <div class="fullscreen-editor-header">
                <h3 class="text-lg font-semibold text-slate-800">全屏编辑</h3>
                <div class="flex items-center space-x-2">
                    <button type="button" id="fullscreen-save-btn"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-check mr-2"></i>保存
                    </button>
                    <button type="button" id="fullscreen-close-btn"
                            class="px-4 py-2 bg-slate-500 text-white rounded-lg hover:bg-slate-600 transition-colors">
                        <i class="fas fa-times mr-2"></i>关闭
                    </button>
                </div>
            </div>
            <div class="fullscreen-editor-content">
                <div id="fullscreen-editor-container" class="fullscreen-editor"></div>
            </div>
        </div>
    `;

    document.body.appendChild(overlay);
    this.initializeFullscreenEditor();
    this.isFullscreenMode = true;

    // 绑定按钮事件
    document.getElementById('fullscreen-save-btn').addEventListener('click', () => {
        this.saveFullscreenContent();
    });

    document.getElementById('fullscreen-close-btn').addEventListener('click', () => {
        this.closeFullscreenEditor();
    });

    // 阻止背景滚动
    document.body.style.overflow = 'hidden';
},

// 保存全屏编辑器内容
saveFullscreenContent() {
    if (this.fullscreenEditor && this.descriptionEditor) {
        this.descriptionEditor.root.innerHTML = this.fullscreenEditor.root.innerHTML;
        this.form.description = this.fullscreenEditor.root.innerHTML;
        showMessage('内容已保存', 'success');
    }
    this.closeFullscreenEditor();
},

// 关闭全屏编辑器
closeFullscreenEditor() {
    const overlay = document.getElementById('fullscreen-overlay');
    if (overlay) {
        overlay.remove();
    }

    this.fullscreenEditor = null;
    this.isFullscreenMode = false;

    // 恢复背景滚动
    document.body.style.overflow = '';
}
```

#### 使用示例

##### 完整的Alpine.js组件示例
```javascript
function richTextManager() {
    return {
        // 表单数据
        form: {
            title: '',
            description: ''
        },

        // 编辑器实例
        descriptionEditor: null,
        fullscreenEditor: null,
        isFullscreenMode: false,

        // 初始化
        async initialize() {
            await this.$nextTick();
            setTimeout(() => {
                this.initializeQuillEditor();
            }, 200);
        },

        // 提交表单
        async submitForm() {
            const data = {
                title: this.form.title.trim(),
                description: this.descriptionEditor ? this.descriptionEditor.root.innerHTML : ''
            };

            // 提交逻辑
            console.log('提交数据:', data);
        }

        // ... 其他方法（参考上面的实现）
    }
}
```

#### 最佳实践

##### 1. 初始化时序
- 使用`$nextTick()`确保DOM渲染完成
- 添加延迟确保所有依赖资源加载完成
- 检查容器元素存在性，失败时自动重试

##### 2. 图片处理
- 支持多种图片格式：PNG、JPG、JPEG、GIF、WebP
- 文件大小限制：5MB
- 图片默认显示为22%宽度，保持比例
- 使用多重策略确保图片插入成功

##### 3. 全屏编辑
- 提供更大的编辑空间
- 保持所有编辑功能
- 内容自动同步
- 用户友好的保存和关闭操作

##### 4. 错误处理
- 完善的异常捕获和处理
- 用户友好的错误提示
- 自动重试机制
- 详细的调试日志

#### 注意事项

1. **资源依赖**：确保Quill.js相关文件正确加载
2. **容器ID**：每个页面的编辑器容器ID必须唯一
3. **图片上传API**：需要后端提供`/api/common/upload/image`接口
4. **样式冲突**：注意与其他CSS框架的样式冲突
5. **浏览器兼容性**：Quill.js 2.0+支持现代浏览器

## 布局规范

### 1. 网格系统

#### 响应式网格
```css
.samples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    align-items: start;
}

@media (min-width: 1280px) {
    .samples-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
}

@media (min-width: 1536px) {
    .samples-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
}
```

### 2. 侧边栏导航

#### 固定宽度侧边栏
```html
<div class="w-80 bg-white/80 backdrop-blur-sm border-r border-slate-200/60 flex flex-col">
    <!-- 侧边栏内容 -->
</div>
```

### 3. 主内容区

#### 弹性布局
```html
<div class="flex-1 flex flex-col overflow-hidden">
    <!-- 顶部操作栏 -->
    <div class="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 px-6 py-4">
        <!-- 操作内容 -->
    </div>
    
    <!-- 滚动内容区 -->
    <div class="flex-1 overflow-y-auto custom-scrollbar p-6">
        <!-- 主要内容 -->
    </div>
</div>
```

## 交互效果

### 1. 悬停效果

```css
/* 卡片悬停 */
.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* 按钮悬停 */
.hover-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
}
```

### 2. 过渡动画

```css
/* 标准过渡 */
.transition-standard {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 快速过渡 */
.transition-fast {
    transition: all 0.2s ease;
}
```

### 3. 加载动画

```css
.loading-skeleton {
    background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}
```

## Alpine.js 使用规范

### 1. 组件结构

```javascript
function componentName() {
    return {
        // 状态数据
        loading: false,
        data: [],
        
        // 生命周期
        initialize() {
            this.loadData();
        },
        
        // 方法
        async loadData() {
            this.loading = true;
            try {
                // 数据加载逻辑
            } catch (error) {
                showMessage('加载失败', 'error');
            } finally {
                this.loading = false;
            }
        }
    }
}
```

### 2. 状态管理

```html
<div x-data="componentName()" x-init="initialize()">
    <!-- 模板内容 -->
</div>
```

### 3. 条件渲染

```html
<!-- 加载状态 -->
<template x-if="loading">
    <div class="loading-skeleton"></div>
</template>

<!-- 数据展示 -->
<template x-if="!loading && data.length > 0">
    <div class="data-list">
        <template x-for="item in data" :key="item.id">
            <div x-text="item.name"></div>
        </template>
    </div>
</template>

<!-- 空状态 -->
<template x-if="!loading && data.length === 0">
    <div class="empty-state">暂无数据</div>
</template>
```

## 最佳实践

### 1. 性能优化
- 使用 CSS 变换而非直接修改布局属性
- 合理使用防抖和节流
- 优化图片和资源加载

### 2. 可访问性
- 为交互元素提供适当的 ARIA 标签
- 确保键盘导航可用
- 保持足够的色彩对比度

### 3. 响应式设计
- 移动端优先设计
- 使用弹性单位
- 测试各种屏幕尺寸

### 4. 代码组织
- 保持组件的单一职责
- 使用语义化的 CSS 类名
- 注释复杂的样式和逻辑

## 分页组件

### 设计理念
分页组件采用现代化设计，提供完整的导航功能，包括首页、末页、页码按钮和省略号，确保用户能够高效地浏览大量数据。

### 组件结构

#### HTML 结构
```html
<div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
    <!-- 信息显示区域 -->
    <div class="flex items-center">
        <p class="text-sm text-gray-700 mr-4">
            第 <span id="currentPage" class="font-medium">1</span> 页，
            共 <span id="totalPages" class="font-medium">1</span> 页，
            共 <span id="totalCount" class="font-medium">0</span> 条
        </p>
    </div>
    
    <!-- 分页按钮区域 -->
    <div class="flex gap-1">
        <!-- 首页按钮 -->
        <button id="firstBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            <span class="sr-only">首页</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
        </button>
        
        <!-- 上一页按钮 -->
        <button id="prevBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            上一页
        </button>
        
        <!-- 页码按钮容器 -->
        <div id="pageNumbers" class="flex gap-1">
            <!-- 页码将通过JavaScript动态生成 -->
        </div>
        
        <!-- 下一页按钮 -->
        <button id="nextBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            下一页
        </button>
        
        <!-- 末页按钮 -->
        <button id="lastBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            <span class="sr-only">末页</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
        </button>
    </div>
</div>
```

### JavaScript 实现

#### 页码生成算法
```javascript
// 页码生成函数
function getPageNumbers(currentPage, totalPages) {
    const pageNumbers = [];
    
    if (totalPages <= 7) {
        // 总页数不超过7页，显示所有页码
        for (let i = 1; i <= totalPages; i++) {
            pageNumbers.push(i);
        }
    } else {
        // 总页数超过7页，使用省略号
        pageNumbers.push(1);
        
        if (currentPage <= 4) {
            // 当前页在前部
            pageNumbers.push(2, 3, 4, 5);
            pageNumbers.push('...');
            pageNumbers.push(totalPages);
        } else if (currentPage >= totalPages - 3) {
            // 当前页在后部
            pageNumbers.push('...');
            pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
            pageNumbers.push(totalPages);
        } else {
            // 当前页在中部
            pageNumbers.push('...');
            pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
            pageNumbers.push('...');
            pageNumbers.push(totalPages);
        }
    }
    
    return pageNumbers;
}
```

#### 页码渲染函数
```javascript
// 渲染页码按钮
function renderPageNumbers(containerSelector, currentPage, totalPages, clickHandler) {
    const container = $(containerSelector);
    container.empty();
    
    const pageNumbers = getPageNumbers(currentPage, totalPages);
    
    pageNumbers.forEach(pageNumber => {
        if (pageNumber === '...') {
            // 省略号
            container.append(`
                <span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700">
                    ...
                </span>
            `);
        } else {
            // 页码按钮
            const isActive = pageNumber === currentPage;
            const activeClass = isActive ? 'bg-blue-50 text-blue-600 border-blue-500' : 'bg-white text-gray-700';
            
            container.append(`
                <button data-page="${pageNumber}" 
                        class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md hover:bg-gray-50 ${activeClass}">
                    ${pageNumber}
                </button>
            `);
        }
    });
    
    // 绑定页码点击事件
    container.off('click', 'button[data-page]').on('click', 'button[data-page]', function() {
        const page = parseInt($(this).data('page'));
        if (page && page !== currentPage) {
            clickHandler(page);
        }
    });
}
```

#### 分页更新函数
```javascript
// 更新分页状态
function updatePagination(totalCount, pageSize) {
    const totalPages = Math.ceil(totalCount / pageSize);
    
    // 更新显示信息
    $('#currentPage').text(currentPage);
    $('#totalPages').text(totalPages);
    $('#totalCount').text(totalCount);
    
    // 更新按钮状态
    $('#firstBtn').prop('disabled', currentPage <= 1);
    $('#prevBtn').prop('disabled', currentPage <= 1);
    $('#nextBtn').prop('disabled', currentPage >= totalPages);
    $('#lastBtn').prop('disabled', currentPage >= totalPages);
    
    // 渲染页码
    renderPageNumbers('#pageNumbers', currentPage, totalPages, function(page) {
        currentPage = page;
        loadData(); // 重新加载数据
    });
}

// 调用示例
// 在AJAX成功回调中调用：
// updatePagination(response.total, pageSize);
```

#### 事件绑定
```javascript
// 绑定分页按钮事件
$('#firstBtn').click(function() {
    if (currentPage !== 1) {
        currentPage = 1;
        loadData();
    }
});

$('#prevBtn').click(function() {
    if (currentPage > 1) {
        currentPage--;
        loadData();
    }
});

$('#nextBtn').click(function() {
    if (currentPage < totalPages) {
        currentPage++;
        loadData();
    }
});

$('#lastBtn').click(function() {
    if (currentPage !== totalPages) {
        currentPage = totalPages;
        loadData();
    }
});
```

### 设计特点

#### 1. 完整的导航功能
- **首页按钮**: 快速跳转到第一页
- **末页按钮**: 快速跳转到最后一页
- **上一页/下一页**: 顺序导航
- **页码按钮**: 直接跳转到指定页面

#### 2. 智能省略号
- 总页数 ≤ 7页：显示所有页码
- 总页数 > 7页：使用省略号优化显示
- 当前页在前部：`1 2 3 4 5 ... 20`
- 当前页在中部：`1 ... 8 9 10 ... 20`
- 当前页在后部：`1 ... 16 17 18 19 20`

#### 3. 状态反馈
- **当前页高亮**: 蓝色背景和边框
- **禁用状态**: 首页/末页按钮在边界时禁用
- **悬停效果**: 按钮悬停时背景变化

#### 4. 信息显示
- 显示当前页码、总页数、总条数
- 提供完整的数据概览信息

### 使用示例

#### 在出版社管理页面中的应用
```javascript
// 待处理申请分页
function updatePendingPagination(total = 0) {
    $('#pendingCurrentPage').text(pendingCurrentPage);
    $('#pendingTotalPages').text(pendingTotalPages);
    $('#pendingTotalCount').text(total);
    
    $('#pendingFirstBtn').prop('disabled', pendingCurrentPage <= 1);
    $('#pendingPrevBtn').prop('disabled', pendingCurrentPage <= 1);
    $('#pendingNextBtn').prop('disabled', pendingCurrentPage >= pendingTotalPages);
    $('#pendingLastBtn').prop('disabled', pendingCurrentPage >= pendingTotalPages);
    
    renderPageNumbers('#pendingPageNumbers', pendingCurrentPage, pendingTotalPages, function(page) {
        pendingCurrentPage = page;
        loadPendingRequests();
    });
}
```

### 样式规范

#### 按钮样式
```css
/* 普通状态 */
.pagination-btn {
    @apply relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed;
}

/* 当前页状态 */
.pagination-btn-active {
    @apply bg-blue-50 text-blue-600 border-blue-500;
}

/* 省略号样式 */
.pagination-ellipsis {
    @apply relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700;
}
```

### 最佳实践

1. **一致性**: 在所有需要分页的页面使用相同的组件结构
2. **响应式**: 在小屏幕设备上适当调整按钮大小和间距
3. **性能**: 避免在页码变化时重新渲染整个列表
4. **用户体验**: 提供清晰的状态反馈和加载提示
5. **可访问性**: 为按钮提供适当的 aria-label 和 sr-only 文本

### 注意事项

1. **边界处理**: 确保在第一页和最后一页时正确禁用相应按钮
2. **数据同步**: 页码变化时及时更新显示信息
3. **错误处理**: 处理总页数为0或负数的情况
4. **事件解绑**: 避免重复绑定事件导致的问题

### 常见问题

#### 问题1: 总条数显示为0
**症状**: 分页组件显示"共 0 条"，但实际有数据

**原因**: 调用updatePagination函数时没有传递total参数

**解决方案**:
```javascript
// ❌ 错误的调用方式
updatePagination();

// ✅ 正确的调用方式
updatePagination(response.total, pageSize);
```

#### 问题2: 页码按钮重复绑定事件
**症状**: 点击页码按钮触发多次请求

**原因**: 多次调用renderPageNumbers没有先解绑事件

**解决方案**:
```javascript
// 在renderPageNumbers函数中使用off().on()
container.off('click', 'button[data-page]').on('click', 'button[data-page]', function() {
    // 处理点击事件
});
```

#### 问题3: 按钮禁用状态不正确
**症状**: 在第一页时"首页"按钮仍可点击

**原因**: 没有正确更新按钮的disabled属性

**解决方案**:
```javascript
// 正确更新按钮状态
$('#firstBtn').prop('disabled', currentPage <= 1);
$('#prevBtn').prop('disabled', currentPage <= 1);
$('#nextBtn').prop('disabled', currentPage >= totalPages);
$('#lastBtn').prop('disabled', currentPage >= totalPages);
```

## 7. 标签组件系统

### 概述

标签组件系统用于分类显示样书的各种属性信息，通过不同的颜色、图标和样式来区分不同类型的标签，提升信息的可读性和视觉层次。

### 设计原则

1. **语义化**：每种标签类型都有明确的语义和用途
2. **可识别性**：通过颜色和图标快速识别标签类型
3. **一致性**：同类型标签在所有页面保持一致的样式
4. **简洁性**：避免过度装饰，保持简洁美观

### 基础标签样式

#### 通用标签基类
```css
.tag {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid;
    line-height: 1;
    gap: 0.25rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
```

### 标签类型规范

#### 1. 学校层次标签 (.tag-level)
**用途**：显示教材适用的学校层次（如：中职、专科、本科、技校）

**视觉规范**：
- **颜色**：蓝色主题渐变 `#e0f2fe` → `#b3e5fc`
- **文字颜色**：`#0277bd`
- **边框颜色**：`#81d4fa`
- **图标**：`fas fa-graduation-cap` 🎓

```css
.tag-level {
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
    color: #0277bd;
    border-color: #81d4fa;
}
```

**使用示例**：
```html
<span class="tag tag-level">
    <i class="fas fa-graduation-cap"></i>
    <span>高职</span>
</span>
```

#### 2. 图书类型标签 (.tag-book-type)
**用途**：显示图书的类型分类（如：教材、参考书、教学参考书）

**视觉规范**：
- **颜色**：绿色主题渐变 `#e8f5e8` → `#c8e6c9`
- **文字颜色**：`#2e7d32`
- **边框颜色**：`#a5d6a7`
- **图标**：`fas fa-book` 📚

```css
.tag-book-type {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #2e7d32;
    border-color: #a5d6a7;
}
```

**使用示例**：
```html
<span class="tag tag-book-type">
    <i class="fas fa-book"></i>
    <span>教材</span>
</span>
```

#### 3. 国家规划标签 (.tag-national)
**用途**：标识国家级规划教材

**视觉规范**：
- **颜色**：橙色主题渐变 `#fff3e0` → `#ffe0b2`
- **文字颜色**：`#e65100`
- **边框颜色**：`#ffcc02`
- **图标**：`fas fa-star` ⭐

```css
.tag-national {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    color: #e65100;
    border-color: #ffcc02;
}
```

**使用示例**：
```html
<span class="tag tag-national">
    <i class="fas fa-star"></i>
    <span>国家规划</span>
    <!-- 如果有具体级别名称 -->
    <span>(十四五)</span>
</span>
```

#### 4. 省级规划标签 (.tag-provincial)
**用途**：标识省级规划教材

**视觉规范**：
- **颜色**：紫色主题渐变 `#f3e5f5` → `#e1bee7`
- **文字颜色**：`#7b1fa2`
- **边框颜色**：`#ce93d8`
- **图标**：`fas fa-medal` 🏅

```css
.tag-provincial {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    color: #7b1fa2;
    border-color: #ce93d8;
}
```

**使用示例**：
```html
<span class="tag tag-provincial">
    <i class="fas fa-medal"></i>
    <span>省级规划</span>
    <!-- 如果有具体级别名称 -->
    <span>(重点建设)</span>
</span>
```

#### 5. 特色标签 (.tag-feature)
**用途**：显示教材的特色标签（如：实践性、创新性、综合性等）

**视觉规范**：
- **颜色**：浅绿色主题渐变 `#f1f8e9` → `#dcedc8`
- **文字颜色**：`#33691e`
- **边框颜色**：`#aed581`
- **图标**：`fas fa-tags` 🏷️

```css
.tag-feature {
    background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
    color: #33691e;
    border-color: #aed581;
}
```

**使用示例**：
```html
<span class="tag tag-feature">
    <i class="fas fa-tags"></i>
    <span>实践性</span>
</span>
```

#### 6. 材质标签 (.tag-material)
**用途**：显示教材的材质类型（如：纸质教材、数字教材）

**视觉规范**：
- **颜色**：粉色主题渐变 `#fce4ec` → `#f8bbd9`
- **文字颜色**：`#ad1457`
- **边框颜色**：`#f48fb1`
- **图标**：`fas fa-file-alt` 📄

```css
.tag-material {
    background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
    color: #ad1457;
    border-color: #f48fb1;
}
```

**使用示例**：
```html
<span class="tag tag-material">
    <i class="fas fa-file-alt"></i>
    <span>纸质教材</span>
</span>
```

#### 7. 色系标签 (.tag-color-*)
**用途**：显示教材的色系类型（如：彩色、双色、四色等）

**视觉规范**：

##### 彩色标签 (.tag-color-colorful)
- **颜色**：彩虹渐变主题 `#fef3c7` → `#fde68a`
- **文字颜色**：`#92400e`
- **边框颜色**：`#fbbf24`
- **图标**：`fas fa-palette` 🎨

```css
.tag-color-colorful {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border-color: #fbbf24;
}
```

##### 双色标签 (.tag-color-dual)
- **颜色**：青色主题渐变 `#e0f7fa` → `#b2ebf2`
- **文字颜色**：`#00695c`
- **边框颜色**：`#4dd0e1`
- **图标**：`fas fa-palette` 🎨

```css
.tag-color-dual {
    background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
    color: #00695c;
    border-color: #4dd0e1;
}
```

##### 四色标签 (.tag-color-four)
- **颜色**：红色主题渐变 `#ffebee` → `#ffcdd2`
- **文字颜色**：`#c62828`
- **边框颜色**：`#ef5350`
- **图标**：`fas fa-palette` 🎨

```css
.tag-color-four {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    color: #c62828;
    border-color: #ef5350;
}
```

**使用示例**：
```html
<span class="tag tag-color-colorful">
    <i class="fas fa-palette"></i>
    <span>彩色</span>
</span>

<span class="tag tag-color-dual">
    <i class="fas fa-palette"></i>
    <span>双色</span>
</span>

<span class="tag tag-color-four">
    <i class="fas fa-palette"></i>
    <span>四色</span>
</span>
```

#### 8. 默认标签 (.tag-default)
**用途**：备用样式，用于其他类型的标签

**视觉规范**：
- **颜色**：灰色主题渐变 `#f5f5f5` → `#eeeeee`
- **文字颜色**：`#616161`
- **边框颜色**：`#e0e0e0`

```css
.tag-default {
    background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
    color: #616161;
    border-color: #e0e0e0;
}
```

### 使用指南

#### Alpine.js 模板使用
在Alpine.js模板中，推荐使用条件渲染确保只有在有值时才显示标签：

```html
<!-- 基础信息标签 -->
<div class="flex flex-wrap gap-2">
    <!-- 学校层次 -->
    <template x-if="sample.level">
        <span class="tag tag-level">
            <i class="fas fa-graduation-cap"></i>
            <span x-text="sample.level"></span>
        </span>
    </template>
    
    <!-- 图书类型 -->
    <template x-if="sample.book_type">
        <span class="tag tag-book-type">
            <i class="fas fa-book"></i>
            <span x-text="sample.book_type"></span>
        </span>
    </template>
    
    <!-- 材质类型 -->
    <template x-if="sample.material_type">
        <span class="tag tag-material">
            <i class="fas fa-file-alt"></i>
            <span x-text="sample.material_type"></span>
        </span>
    </template>
</div>

<!-- 规划认证标签 -->
<div class="flex flex-wrap gap-2">
    <!-- 国家规划 -->
    <template x-if="sample.national_regulation == 1">
        <span class="tag tag-national">
            <i class="fas fa-star"></i>
            <span>国家规划</span>
            <template x-if="sample.national_regulation_level_name">
                <span x-text="'(' + sample.national_regulation_level_name + ')'"></span>
            </template>
        </span>
    </template>
    
    <!-- 省级规划 -->
    <template x-if="sample.provincial_regulation == 1">
        <span class="tag tag-provincial">
            <i class="fas fa-medal"></i>
            <span>省级规划</span>
            <template x-if="sample.provincial_regulation_level_name">
                <span x-text="'(' + sample.provincial_regulation_level_name + ')'"></span>
            </template>
        </span>
    </template>
    
    <!-- 特色标签 -->
    <template x-if="sample.feature_name">
        <span class="tag tag-feature">
            <i class="fas fa-tags"></i>
            <span x-text="sample.feature_name"></span>
        </span>
    </template>
</div>
```

### 布局规范

#### 标签分组
建议将标签按照语义进行分组显示：

1. **基础信息组**：学校层次、图书类型、材质类型
2. **认证信息组**：国家规划、省级规划、特色标签

#### 间距规范
- **标签间距**：使用 `gap-2` (8px)
- **标签组间距**：使用 `space-y-3` (12px)
- **标签与其他元素间距**：根据上下文适当调整

### 响应式设计

标签组件在不同屏幕尺寸下的表现：

- **桌面端**：正常显示所有标签
- **平板端**：可能需要换行显示
- **移动端**：优先显示重要标签，次要标签可折叠

### 可访问性

1. **颜色对比度**：所有标签文字与背景的对比度符合WCAG 2.1 AA标准
2. **语义化图标**：使用具有明确语义的Font Awesome图标
3. **屏幕阅读器**：可考虑添加 `aria-label` 属性提供更好的描述

### 最佳实践

1. **一致性**：在所有页面使用相同的标签样式和布局规则
2. **简洁性**：避免在单个项目上显示过多标签
3. **优先级**：重要标签优先显示，次要标签可折叠或隐藏
4. **性能**：避免过度使用动画效果影响性能

### 注意事项

1. **数据验证**：确保在显示标签前验证数据的有效性
2. **空值处理**：使用条件渲染避免显示空值标签
3. **长文本处理**：对于过长的标签文本考虑使用省略号或换行
4. **国际化**：考虑不同语言版本下标签的显示效果

## 时间筛选组件

### 概述

时间筛选组件是一个通用的时间范围选择器，提供预设的时间选项和自定义日期范围功能。该组件遵循前端设计规范，具有统一的视觉风格和交互体验。

### 设计原则

1. **易用性**：提供常用的时间选项，减少用户操作步骤
2. **灵活性**：支持自定义日期范围，满足特殊需求
3. **一致性**：与其他筛选组件保持统一的样式和交互
4. **直观性**：清晰的标签和反馈，用户能快速理解当前选择

### 组件结构

#### HTML 结构
```html
<div>
    <label class="block text-sm font-medium text-slate-700 mb-2">时间</label>
    <div class="custom-select" id="timeFilterContainer">
        <div class="custom-select-trigger" id="timeFilterTrigger" style="user-select: none;">
            <span class="custom-select-text">全部时间</span>
            <i class="fas fa-chevron-down custom-select-arrow" style="pointer-events: none;"></i>
        </div>
        <div class="custom-select-dropdown">
            <!-- 注意：时间筛选组件不包含搜索框，因为选项固定且数量较少 -->
            <!-- 如需搜索功能，可添加：<div class="custom-select-search"><input type="text" placeholder="搜索时间..." /></div> -->
            <div class="custom-select-options" id="timeFilterOptions">
                <div class="custom-select-option" data-value="">全部时间</div>
                <div class="custom-select-option" data-value="custom">自定义</div>
                <div class="custom-select-option" data-value="today">今天</div>
                <div class="custom-select-option" data-value="yesterday">昨天</div>
                <div class="custom-select-option" data-value="this_month">本月</div>
                <div class="custom-select-option" data-value="last_month">上月</div>
                <div class="custom-select-option" data-value="this_year">本年</div>
                <div class="custom-select-option" data-value="last_year">上年</div>
            </div>
        </div>
    </div>
</div>

<!-- 自定义日期选择器模态框 -->
<div id="datePickerModal" class="fixed inset-0 z-50 hidden">
    <div class="modal-overlay flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between p-6 border-b border-slate-200">
                <h3 class="text-lg font-semibold text-slate-800">选择时间范围</h3>
                <button onclick="closeDatePicker()"
                        class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
            <!-- 模态框内容 -->
            <div class="p-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">开始日期</label>
                        <input type="date" id="startDate"
                               class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">结束日期</label>
                        <input type="date" id="endDate"
                               class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
            <!-- 模态框按钮 -->
            <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                <button onclick="closeDatePicker()"
                        class="btn-secondary btn-sm">
                    取消
                </button>
                <button onclick="confirmDateRange()"
                        class="btn-primary btn-sm">
                    确定
                </button>
            </div>
        </div>
    </div>
</div>
```

### JavaScript 实现

#### 初始化组件
```javascript
// 时间筛选相关变量
let timeFilterSelect = null;
let currentTimeFilter = '';
let customStartDate = '';
let customEndDate = '';

// 初始化时间筛选器
timeFilterSelect = new CustomSelect('timeFilterContainer', {
    placeholder: '全部时间',
    onSelect: function(value, text) {
        handleTimeFilterSelect(value, text);
    }
});

// 设置时间筛选选项
timeFilterSelect.setOptions([
    {value: '', text: '全部时间'},
    {value: 'custom', text: '自定义'},
    {value: 'today', text: '今天'},
    {value: 'yesterday', text: '昨天'},
    {value: 'this_month', text: '本月'},
    {value: 'last_month', text: '上月'},
    {value: 'this_year', text: '本年'},
    {value: 'last_year', text: '上年'}
]);
```

#### 核心处理函数
```javascript
// 处理时间筛选选择
function handleTimeFilterSelect(value, text) {
    currentTimeFilter = value;

    if (value === 'custom') {
        // 显示自定义日期选择器
        showDatePicker();
    } else {
        // 清空自定义日期
        customStartDate = '';
        customEndDate = '';
        // 应用筛选
        applyFilters();
    }
}

// 显示日期选择器
function showDatePicker() {
    // 设置默认日期为今天
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('startDate').value = customStartDate || today;
    document.getElementById('endDate').value = customEndDate || today;

    // 显示模态框
    document.getElementById('datePickerModal').classList.remove('hidden');
}

// 关闭日期选择器
function closeDatePicker() {
    document.getElementById('datePickerModal').classList.add('hidden');

    // 如果没有设置自定义日期，重置时间筛选器
    if (!customStartDate || !customEndDate) {
        timeFilterSelect.reset();
        currentTimeFilter = '';
    }
}

// 确认日期范围
function confirmDateRange() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    if (!startDate || !endDate) {
        showMessage('请选择开始日期和结束日期', 'warning');
        return;
    }

    if (startDate > endDate) {
        showMessage('开始日期不能晚于结束日期', 'warning');
        return;
    }

    // 保存自定义日期
    customStartDate = startDate;
    customEndDate = endDate;

    // 更新时间筛选器显示文本
    const startDateStr = formatDate(startDate);
    const endDateStr = formatDate(endDate);
    timeFilterSelect.textSpan.textContent = `${startDateStr} 至 ${endDateStr}`;

    // 关闭模态框
    closeDatePicker();

    // 应用筛选
    applyFilters();
}

// 获取时间筛选的日期范围
function getTimeFilterRange() {
    if (!currentTimeFilter) {
        return null;
    }

    const now = new Date();
    let startDate, endDate;

    switch (currentTimeFilter) {
        case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
            break;
        case 'yesterday':
            const yesterday = new Date(now);
            yesterday.setDate(yesterday.getDate() - 1);
            startDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
            endDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);
            break;
        case 'this_month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
            break;
        case 'last_month':
            const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            startDate = lastMonth;
            endDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59);
            break;
        case 'this_year':
            startDate = new Date(now.getFullYear(), 0, 1);
            endDate = new Date(now.getFullYear(), 11, 31, 23, 59, 59);
            break;
        case 'last_year':
            startDate = new Date(now.getFullYear() - 1, 0, 1);
            endDate = new Date(now.getFullYear() - 1, 11, 31, 23, 59, 59);
            break;
        case 'custom':
            if (customStartDate && customEndDate) {
                startDate = new Date(customStartDate);
                endDate = new Date(customEndDate + ' 23:59:59');
            } else {
                return null;
            }
            break;
        default:
            return null;
    }

    return {
        start: startDate.toISOString().split('T')[0],
        end: endDate.toISOString().split('T')[0]
    };
}
```

#### 筛选集成
```javascript
// 在筛选函数中集成时间筛选
function applyFilters() {
    // ... 其他筛选逻辑

    // 按时间筛选
    const timeRange = getTimeFilterRange();
    if (timeRange) {
        filtered = filtered.filter(r => {
            if (!r.created_at) return false;

            // 将创建时间转换为日期字符串进行比较
            const createdDate = new Date(r.created_at).toISOString().split('T')[0];
            return createdDate >= timeRange.start && createdDate <= timeRange.end;
        });
    }

    // ... 其他筛选逻辑
}
```

### 预设时间选项

| 选项值 | 显示文本 | 时间范围说明 |
|--------|----------|-------------|
| `""` | 全部时间 | 不进行时间筛选 |
| `custom` | 自定义 | 用户选择的日期范围 |
| `today` | 今天 | 当天 00:00:00 - 23:59:59 |
| `yesterday` | 昨天 | 昨天 00:00:00 - 23:59:59 |
| `this_month` | 本月 | 本月第一天 - 本月最后一天 |
| `last_month` | 上月 | 上月第一天 - 上月最后一天 |
| `this_year` | 本年 | 本年1月1日 - 本年12月31日 |
| `last_year` | 上年 | 上年1月1日 - 上年12月31日 |

### 使用场景

1. **数据列表筛选**：按创建时间、更新时间等筛选记录
2. **报表查询**：选择统计时间范围
3. **日志查看**：按时间范围查看操作日志
4. **订单管理**：按下单时间筛选订单
5. **换版推荐管理**：在经销商和出版社页面按推荐时间筛选

### 设计规范

#### 视觉规范
- **触发器高度**：40px，与其他筛选组件保持一致
- **圆角**：12px，符合整体设计风格
- **边框**：1px #d1d5db，聚焦时变为蓝色
- **字体大小**：14px，保持可读性
- **间距**：内边距 8px 12px

#### 交互规范
- **下拉动画**：200ms 平滑过渡
- **模态框动画**：淡入淡出效果
- **日期验证**：实时验证日期有效性
- **错误提示**：使用统一的消息通知组件

#### 自定义日期选择器
- **模态框宽度**：最大 400px，响应式适配
- **日期输入框**：HTML5 date 类型，统一样式
- **按钮样式**：遵循按钮组件规范
- **验证逻辑**：开始日期不能晚于结束日期

### 最佳实践

1. **默认选项**：通常设置"全部时间"为默认选项
2. **选项顺序**：将"自定义"选项放在"全部时间"后面，确保用户无需滚动即可看到
3. **常用优先**：将最常用的时间选项放在显眼位置
4. **自定义提示**：为自定义选项提供清晰的操作指引
5. **数据格式**：统一使用 ISO 8601 日期格式进行比较
6. **性能优化**：避免频繁的日期计算，可考虑缓存结果

### 响应式设计

- **桌面端**：正常显示所有功能
- **平板端**：模态框适当缩小，保持可用性
- **移动端**：优化触摸操作，增大点击区域

### 可访问性

1. **键盘导航**：支持 Tab 键和方向键导航
2. **屏幕阅读器**：提供适当的 aria-label
3. **焦点管理**：模态框打开时正确设置焦点
4. **颜色对比**：确保文字与背景有足够对比度

### 筛选布局优化

#### 多行布局设计
当页面包含多个筛选条件时，建议采用分层布局来优化空间利用率：

```html
<!-- 筛选选项 -->
<div class="space-y-4 mb-4">
    <!-- 第一行：时间筛选、搜索、重置按钮 -->
    <div class="flex flex-col md:flex-row gap-4 md:items-end">
        <div class="md:w-80">
            <label class="block text-sm font-medium text-slate-700 mb-2">时间</label>
            <!-- 时间筛选组件 -->
        </div>
        <div class="flex-1">
            <label class="block text-sm font-medium text-slate-700 mb-2">搜索</label>
            <input type="text" placeholder="搜索..." class="w-full px-3 py-2 h-10 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div class="flex items-end">
            <button type="button" class="px-4 py-2 h-10 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors flex items-center justify-center gap-2">
                <i class="fas fa-undo"></i>
                <span class="hidden sm:inline">重置</span>
            </button>
        </div>
    </div>

    <!-- 第二行：主要筛选条件 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">学校筛选</label>
            <!-- 筛选组件 -->
        </div>
        <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">推荐类型</label>
            <!-- 筛选组件 -->
        </div>
        <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">推荐状态</label>
            <!-- 筛选组件 -->
        </div>
    </div>
</div>
```

#### 布局原则
1. **功能优先**：将时间筛选、搜索等常用功能放在第一行，便于快速访问
2. **空间优化**：合理利用水平空间，避免垂直空间浪费
3. **宽度适配**：时间筛选组件使用足够宽度，确保自定义日期范围能完整显示
4. **对齐一致**：所有组件在同一行内保持底部对齐，视觉效果统一
5. **响应式适配**：在移动端自动调整为垂直布局

#### 组件宽度规范
- **时间筛选组件**：使用较宽的固定宽度（如 `md:w-80`），确保自定义日期范围显示完整
- **搜索框组件**：使用弹性宽度（`flex-1`），充分利用剩余空间
- **操作按钮**：使用 `flex items-end` 确保与其他组件底部对齐

#### 单行筛选布局（适用于筛选项较少的页面）
```html
<!-- 出版社页面：学校-时间-搜索单行布局 -->
<div class="flex flex-col md:flex-row gap-4 md:items-end">
    <!-- 学校筛选 -->
    <div class="md:w-64">
        <label class="block text-sm font-medium text-slate-700 mb-2">学校筛选</label>
        <!-- 学校筛选组件 -->
    </div>

    <!-- 时间筛选 -->
    <div class="md:w-64">
        <label class="block text-sm font-medium text-slate-700 mb-2">时间</label>
        <!-- 时间筛选组件 -->
    </div>

    <!-- 搜索和操作按钮 -->
    <div class="flex-1">
        <label class="block text-sm font-medium text-slate-700 mb-2">搜索</label>
        <div class="flex gap-2">
            <div class="flex-1 relative">
                <input type="text" placeholder="搜索..." class="w-full px-4 py-3 pl-10 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
            </div>
            <button class="px-4 py-3 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors">
                <i class="fas fa-sync-alt"></i>
            </button>
        </div>
    </div>
</div>
```

**单行布局特点**：
- 适用于筛选项较少（3-4个）的页面
- 所有筛选项在同一行，节省垂直空间
- 搜索框和操作按钮组合在一起，操作更紧凑

### 注意事项

1. **时区处理**：根据业务需求决定是否需要时区转换
2. **边界情况**：处理无效日期、空值等情况
3. **性能考虑**：大数据量时考虑服务端筛选
4. **浏览器兼容**：确保 date 输入框在各浏览器正常工作
5. **搜索框可选**：CustomSelect组件的搜索框是可选的，时间筛选等固定选项的组件可以省略搜索框
6. **布局适配**：在不同屏幕尺寸下保持良好的布局效果



## 更新日志

### v1.3.6 (2024-12-19)
- 优化出版社页面筛选布局，将所有筛选项合并为单行显示
- 调整筛选顺序为：学校-时间-搜索，提升操作流程的逻辑性
- 新增单行筛选布局设计规范和示例代码
- 完善筛选布局的适用场景说明

### v1.3.5 (2024-12-19)
- 在出版社换版推荐页面同步添加时间筛选功能
- 适配出版社页面的布局特点，优化筛选组件排列
- 完善CustomSelect组件，支持可选搜索框的时间筛选器
- 扩展时间筛选组件的使用场景说明

### v1.3.4 (2024-12-19)
- 修正筛选布局设计，将时间筛选、搜索和重置按钮调整到第一行
- 增加时间筛选组件宽度（md:w-80），确保自定义日期范围完整显示
- 优化重置按钮对齐方式，使用flex items-end确保底部对齐
- 更新布局原则，强调功能优先和对齐一致性

### v1.3.3 (2024-12-19)
- 新增筛选布局优化设计规范，支持多行分层布局
- 优化空间利用率，将时间筛选、搜索和重置按钮合并为一行
- 定义组件宽度规范和响应式布局原则
- 提供完整的多行筛选布局示例代码

### v1.3.2 (2024-12-19)
- 优化时间筛选组件选项顺序，将"自定义"选项移至"全部时间"后面
- 提升用户体验，确保重要选项在可视区域内
- 更新最佳实践指南，强调选项顺序的重要性

### v1.3.1 (2024-12-19)
- 修复CustomSelect组件搜索框依赖问题，使搜索框变为可选组件
- 优化时间筛选组件HTML结构，移除不必要的搜索框
- 完善组件使用说明，添加搜索框可选性的注意事项

### v1.3.0 (2024-12-19)
- 新增时间筛选组件设计规范
- 定义预设时间选项和自定义日期范围功能
- 完善组件的视觉规范、交互规范和使用指南
- 提供完整的JavaScript实现和集成方案

### v1.2.1 (2024-12-19)
- 新增色系标签组件规范（彩色、双色、四色）
- 定义色系标签的视觉规范和使用指南
- 完善标签组件系统，支持更多样化的标签类型

### v1.2.0 (2024-12-19)
- 新增标签组件系统设计规范
- 定义6种标签类型的视觉规范和使用指南
- 完善标签组件的Alpine.js模板使用方法
- 建立标签布局和响应式设计规范

### v1.1.1 (2024-12-19)
- 修复分页组件总条数显示为0的问题
- 更新分页函数调用示例，明确参数传递要求
- 新增常见问题和解决方案章节
- 完善出版社和教师管理页面分页组件

### v1.1.0 (2024-12-19)
- 新增分页组件设计规范
- 实现首页、末页、页码按钮和智能省略号功能
- 完善出版社订单管理页面分页样式
- 优化用户体验和交互反馈

### v1.0.0 (2024-01-15)
- 初始版本发布
- 建立基础设计规范
- 完成核心组件库

---

本文档将随着项目发展持续更新，请团队成员及时关注最新版本。如有疑问或建议，请联系前端团队负责人。 