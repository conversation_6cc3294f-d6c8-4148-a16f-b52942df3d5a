<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>系统登录</title>
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-container {
            width: 400px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            padding: 40px;
            box-sizing: border-box;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .login-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
        }

        .system-title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        .system-title h1 {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .system-title p {
            font-size: 14px;
            color: #666;
            margin-top: 0;
        }

        .login-form {
            width: 100%;
        }

        .form-item {
            /* margin-bottom: 25px; */
            width: 100%;
        }

        .input-container {
            position: relative;
            height: 50px;
            width: 100%;
            display: flex;
            align-items: center;
        }

        .form-input {
            width: 100%;
            height: 100%;
            box-sizing: border-box;
            padding: 12px 15px 12px 55px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 15px;
            transition: all 0.3s;
            background-color: #f8f9fa;
            color: #333;
        }

        .form-input:focus {
            border-color: #2575fc;
            box-shadow: 0 0 0 2px rgba(37, 117, 252, 0.2);
            background-color: #fff;
            outline: none;
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #888;
            font-size: 20px;
            z-index: 2;
            transition: all 0.2s ease;
        }

        .hint-container {
            height: 20px;
            margin-top: 5px;
            display: flex;
            align-items: flex-start;
        }

        .login-hint {
            font-size: 12px;
            color: #666;
            line-height: 1.2;
            transition: all 0.2s ease;
            min-height: 15px;
        }

        .login-hint.error {
            color: #F44336;
        }

        .login-hint.success {
            color: #2575fc;
        }

        .login-hint.empty {
            opacity: 0;
            visibility: hidden;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            background: linear-gradient(to right, #6a11cb, #2575fc);
            color: white;
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 10px;
        }

        .login-btn:hover {
            background: linear-gradient(to right, #5800c7, #1e68e6);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 117, 252, 0.4);
        }

        .register-btn {
            width: 100%;
            padding: 12px;
            border-radius: 6px;
            background: transparent;
            color: #6a11cb;
            font-size: 16px;
            font-weight: 600;
            border: 1px solid #6a11cb;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 10px;
        }

        .register-btn:hover {
            background: rgba(106, 17, 203, 0.1);
        }

        .footer-text {
            text-align: center;
            margin-top: 20px;
            font-size: 13px;
            color: #666;
        }

        @media (max-width: 500px) {
            .login-container {
                width: 90%;
                padding: 30px 20px;
            }
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="system-title">
            <!-- 动态Logo -->
            <div id="siteLogo" class="site-logo" style="display: none; text-align: center; margin-bottom: 15px;">
                <img id="siteLogoImg" src="" alt="网站Logo" style="max-width: 120px; max-height: 80px;">
            </div>
            <h1 id="siteTitle"></h1>
        </div>

        <form id="loginForm" class="login-form" action="/api/common/login" method="post">
            <div class="form-item">
                <div class="input-container">
                    <i class="fas fa-user input-icon" id="loginIcon"></i>
                    <input type="text" name="username" required id="usernameInput" placeholder="请输入用户名、手机号或邮箱"
                        autocomplete="off" class="form-input">
                </div>
                <div class="hint-container">
                    <div id="loginTypeHint" class="login-hint"></div>
                </div>
            </div>

            <div class="form-item">
                <div class="input-container">
                    <i class="fas fa-lock input-icon"></i>
                    <input type="password" name="password" required placeholder="请输入密码" autocomplete="off"
                        class="form-input">
                </div>
            </div>

            <button type="submit" class="login-btn">
                登录系统
            </button>

            <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                <a id="registerLink" href="/register" style="text-decoration: none; flex: 1; margin-right: 5px;">
                    <button type="button" class="register-btn">注册账号</button>
                </a>
                <a id="forgotPasswordLink" href="/forgot_password"
                    style="text-decoration: none; flex: 1; margin-left: 5px;">
                    <button type="button" class="register-btn"
                        style="border-color: #2575fc; color: #2575fc;">忘记密码</button>
                </a>
            </div>
        </form>

        <div class="footer-text" id="siteTitleFotter"></div>
    </div>

    <script src="{{ url_for('static', filename='jquery.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 获取网站信息
            loadSiteInfo();

            // 设置忘记密码链接和注册链接
            setForgotPasswordLink();
            setRegisterLink();

            // 初始化登录类型检测
            initLoginTypeDetection();

            const loginForm = document.getElementById('loginForm');

            loginForm.addEventListener('submit', function (event) {
                event.preventDefault();

                const formData = new FormData(loginForm);
                const data = {};
                formData.forEach((value, key) => {
                    data[key] = value;
                });

                fetch('/api/common/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(data)
                })
                    .then(response => response.json())
                    .then(response => {
                        if (response.status === 'success') {
                            showMessage('登录成功', true);
                            setTimeout(() => {
                                // 检查是否有返回地址参数
                                const urlParams = new URLSearchParams(window.location.search);
                                const returnTo = urlParams.get('return_to');

                                if (returnTo) {
                                    // 如果有返回地址，跳转到返回地址
                                    window.location.href = decodeURIComponent(returnTo);
                                } else {
                                    // 否则跳转到默认的dashboard
                                    window.location.href = '/dashboard';
                                }
                            }, 1500);
                        } else {
                            showMessage('登录失败: ' + response.message, false);
                        }
                    })
                    .catch(error => {
                        showMessage('登录请求失败，请重试', false);
                    });
            });

            function showMessage(message, isSuccess) {
                const messageDiv = document.createElement('div');
                messageDiv.textContent = message;
                messageDiv.style.position = 'fixed';
                messageDiv.style.top = '20px';
                messageDiv.style.left = '50%';
                messageDiv.style.transform = 'translateX(-50%)';
                messageDiv.style.padding = '10px 20px';
                messageDiv.style.borderRadius = '4px';
                messageDiv.style.backgroundColor = isSuccess ? '#4CAF50' : '#F44336';
                messageDiv.style.color = 'white';
                messageDiv.style.zIndex = '1000';
                messageDiv.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';

                document.body.appendChild(messageDiv);

                setTimeout(() => {
                    messageDiv.style.opacity = '0';
                    messageDiv.style.transition = 'opacity 0.5s';
                    setTimeout(() => {
                        document.body.removeChild(messageDiv);
                    }, 500);
                }, 3000);
            }

            function loadSiteInfo() {

                fetch(`/api/common/get_site_info?role=teacher`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 0 && data.data) {
                            // 更新网站名称
                            if (data.data.site_name) {
                                document.getElementById('siteTitle').textContent = data.data.site_name;
                                document.getElementById('siteTitleFotter').textContent = data.data.site_name;
                            }

                            // 更新Logo
                            if (data.data.logo_url) {
                                document.getElementById('siteLogoImg').src = data.data.logo_url;
                                document.getElementById('siteLogo').style.display = 'block';
                            }
                        }
                    })
                    .catch(error => {
                        console.log('获取网站信息失败:', error);
                        // 失败时使用默认值，不显示错误
                    });
            }

            function setForgotPasswordLink() {
                // 获取当前页面的URL参数
                const urlParams = new URLSearchParams(window.location.search);
                const urlParam = urlParams.get('url');
                const returnTo = urlParams.get('return_to');

                // 构建忘记密码链接，携带url和return_to参数
                const forgotPasswordLink = document.getElementById('forgotPasswordLink');
                let forgotUrl = '/forgot_password';
                const params = new URLSearchParams();

                if (urlParam) {
                    params.append('url', urlParam);
                }
                if (returnTo) {
                    params.append('return_to', returnTo);
                }

                if (params.toString()) {
                    forgotUrl += '?' + params.toString();
                }

                forgotPasswordLink.href = forgotUrl;
            }

            function setRegisterLink() {
                // 获取当前页面的URL参数
                const urlParams = new URLSearchParams(window.location.search);
                const urlParam = urlParams.get('url');
                const returnTo = urlParams.get('return_to');

                // 构建注册链接，携带url和return_to参数
                const registerLink = document.getElementById('registerLink');
                let registerUrl = '/register';
                const params = new URLSearchParams();

                if (urlParam) {
                    params.append('url', urlParam);
                }
                if (returnTo) {
                    params.append('return_to', returnTo);
                }

                if (params.toString()) {
                    registerUrl += '?' + params.toString();
                }

                registerLink.href = registerUrl;
            }

            function initLoginTypeDetection() {
                const usernameInput = document.getElementById('usernameInput');
                const loginIcon = document.getElementById('loginIcon');
                const loginTypeHint = document.getElementById('loginTypeHint');
                let debounceTimer = null;

                // 检测登录类型的函数
                function detectLoginType(input) {
                    if (!input || !input.trim()) {
                        return 'username';
                    }

                    // 邮箱格式检测
                    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (emailPattern.test(input)) {
                        return 'email';
                    }

                    // 手机号格式检测（中国大陆手机号）
                    const phonePattern = /^1[3-9]\d{9}$/;
                    if (phonePattern.test(input)) {
                        return 'phone';
                    }

                    // 默认为用户名
                    return 'username';
                }

                // 更新图标和提示的函数
                function updateLoginTypeUI(loginType, input) {
                    // 移除所有状态类
                    loginTypeHint.classList.remove('error', 'success', 'empty');

                    switch (loginType) {
                        case 'email':
                            loginIcon.className = 'fas fa-envelope input-icon';
                            loginTypeHint.textContent = '检测到邮箱格式';
                            loginTypeHint.classList.add('success');
                            break;
                        case 'phone':
                            loginIcon.className = 'fas fa-mobile-alt input-icon';
                            loginTypeHint.textContent = '检测到手机号格式';
                            loginTypeHint.classList.add('success');
                            break;
                        case 'username':
                            loginIcon.className = 'fas fa-user input-icon';
                            if (input && input.trim()) {
                                loginTypeHint.textContent = '用户名格式';
                                loginTypeHint.classList.add('success');
                            } else {
                                loginTypeHint.textContent = '';
                                loginTypeHint.classList.add('empty');
                            }
                            break;
                    }
                }

                // 验证登录凭据的函数
                function validateLoginInput(input, loginType) {
                    if (!input || !input.trim()) {
                        return { valid: false, message: '登录凭据不能为空' };
                    }

                    switch (loginType) {
                        case 'email':
                            const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                            if (!emailPattern.test(input)) {
                                return { valid: false, message: '邮箱格式不正确' };
                            }
                            break;
                        case 'phone':
                            const phonePattern = /^1[3-9]\d{9}$/;
                            if (!phonePattern.test(input)) {
                                return { valid: false, message: '手机号格式不正确' };
                            }
                            break;
                        case 'username':
                            const usernamePattern = /^[a-zA-Z0-9_]{3,20}$/;
                            if (!usernamePattern.test(input)) {
                                return { valid: false, message: '用户名格式不正确（3-20位字母、数字、下划线）' };
                            }
                            break;
                    }

                    return { valid: true, message: '' };
                }

                // 输入事件监听
                usernameInput.addEventListener('input', function (e) {
                    const input = e.target.value;
                    const loginType = detectLoginType(input);

                    // 清除之前的定时器
                    if (debounceTimer) {
                        clearTimeout(debounceTimer);
                    }

                    // 立即更新UI
                    updateLoginTypeUI(loginType, input);

                    // 防抖验证
                    debounceTimer = setTimeout(() => {
                        if (input && input.trim()) {
                            const validation = validateLoginInput(input, loginType);
                            if (!validation.valid) {
                                loginTypeHint.classList.remove('success', 'empty');
                                loginTypeHint.classList.add('error');
                                loginTypeHint.textContent = validation.message;
                                usernameInput.style.borderColor = '#F44336';
                            } else {
                                usernameInput.style.borderColor = '#e0e0e0';
                            }
                        }
                    }, 500);
                });

                // 焦点事件
                usernameInput.addEventListener('focus', function () {
                    const input = this.value;
                    const loginType = detectLoginType(input);
                    updateLoginTypeUI(loginType, input);
                });

                usernameInput.addEventListener('blur', function () {
                    if (!this.value.trim()) {
                        loginTypeHint.style.display = 'none';
                        loginIcon.className = 'fas fa-user input-icon';
                        this.style.borderColor = '#e0e0e0';
                    }
                });
            }
        });
    </script>
</body>

</html>