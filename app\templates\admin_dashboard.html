<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>管理员控制台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #3b82f6;
            --primary-hover: #2563eb;
            --primary-light: #93c5fd;
            --secondary: #14b8a6;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --card-bg: #ffffff;
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --card-radius: 0.5rem;
        }
        
        body {
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #f1f5f9;
        }
        
        .stat-card {
            background-color: var(--card-bg);
            border-radius: var(--card-radius);
            box-shadow: var(--card-shadow);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .chart-container {
            height: 250px;
            position: relative;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.3s ease forwards;
        }
        
        @keyframes slideUp {
            from { transform: translateY(10px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .animate-slide-up {
            animation: slideUp 0.3s ease forwards;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .animate-spin {
            animation: spin 1s linear;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .grid-cols-4 {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
            
            .md\:grid-cols-3 {
                grid-template-columns: repeat(1, minmax(0, 1fr));
            }
            
            .px-6 {
                padding-left: 1rem;
                padding-right: 1rem;
            }
            
            .chart-container {
                height: 200px;
            }
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">
    <div class="container mx-auto px-4 md:px-6 py-6 md:py-8">
        <!-- 页面标题和刷新按钮 -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 md:mb-8">
            <h1 class="text-2xl md:text-3xl font-bold text-blue-600 mb-4 md:mb-0">管理员控制台</h1>
            <button id="refreshStats" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg shadow flex items-center transition duration-300">
                <i class="fas fa-sync-alt mr-2"></i>刷新数据
            </button>
        </div>
        
        <!-- 统计卡片 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mb-6 md:mb-8">
            <div class="stat-card p-4 md:p-6">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-2 md:p-3 rounded-full mr-3 md:mr-4">
                        <i class="fas fa-users text-blue-500 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-gray-500 text-xs md:text-sm">用户总数</div>
                        <div id="totalUsers" class="text-xl md:text-3xl font-bold">-</div>
                    </div>
                </div>
            </div>
            
            <div class="stat-card p-4 md:p-6">
                <div class="flex items-center">
                    <div class="bg-green-100 p-2 md:p-3 rounded-full mr-3 md:mr-4">
                        <i class="fas fa-book text-green-500 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-gray-500 text-xs md:text-sm">样书总数</div>
                        <div id="totalSamples" class="text-xl md:text-3xl font-bold">-</div>
                    </div>
                </div>
            </div>
            
            <div class="stat-card p-4 md:p-6">
                <div class="flex items-center">
                    <div class="bg-yellow-100 p-2 md:p-3 rounded-full mr-3 md:mr-4">
                        <i class="fas fa-file-alt text-yellow-500 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-gray-500 text-xs md:text-sm">样书申请</div>
                        <div id="totalSampleRequests" class="text-xl md:text-3xl font-bold">-</div>
                    </div>
                </div>
            </div>
            
            <div class="stat-card p-4 md:p-6">
                <div class="flex items-center">
                    <div class="bg-purple-100 p-2 md:p-3 rounded-full mr-3 md:mr-4">
                        <i class="fas fa-store text-purple-500 text-xl"></i>
                    </div>
                    <div>
                        <div class="text-gray-500 text-xs md:text-sm">书展活动</div>
                        <div id="totalExhibitions" class="text-xl md:text-3xl font-bold">-</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 用户统计 -->
            <div class="stat-card overflow-hidden">
                <div class="p-4 bg-blue-50 border-b border-blue-100">
                    <h2 class="text-lg font-semibold text-blue-700">用户统计</h2>
                </div>
                <div class="p-4 md:p-6">
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">教师用户</span>
                            <span id="teacherCount" class="font-medium text-base md:text-lg">-</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">出版社用户</span>
                            <span id="publisherCount" class="font-medium text-base md:text-lg">-</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">经销商用户</span>
                            <span id="dealerCount" class="font-medium text-base md:text-lg">-</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">管理员用户</span>
                            <span id="adminCount" class="font-medium text-base md:text-lg">-</span>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <canvas id="userChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 样书申请统计 -->
            <div class="stat-card overflow-hidden">
                <div class="p-4 bg-green-50 border-b border-green-100">
                    <h2 class="text-lg font-semibold text-green-700">样书申请统计</h2>
                </div>
                <div class="p-4 md:p-6">
                    <div class="grid grid-cols-3 gap-4 mb-6">
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">待处理</span>
                            <span id="pendingSampleRequests" class="font-medium text-base md:text-lg">-</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">已通过</span>
                            <span id="approvedSampleRequests" class="font-medium text-base md:text-lg">-</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">已拒绝</span>
                            <span id="rejectedSampleRequests" class="font-medium text-base md:text-lg">-</span>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <canvas id="sampleRequestChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 书展活动统计 -->
            <div class="stat-card overflow-hidden">
                <div class="p-4 bg-purple-50 border-b border-purple-100">
                    <h2 class="text-lg font-semibold text-purple-700">书展活动统计</h2>
                </div>
                <div class="p-4 md:p-6">
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">草稿</span>
                            <span id="draftExhibitions" class="font-medium text-base md:text-lg">-</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">已发布</span>
                            <span id="publishedExhibitions" class="font-medium text-base md:text-lg">-</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">已取消</span>
                            <span id="cancelledExhibitions" class="font-medium text-base md:text-lg">-</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">已结束</span>
                            <span id="endedExhibitions" class="font-medium text-base md:text-lg">-</span>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <canvas id="exhibitionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 第二行内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <!-- 样书分类统计 -->
            <div class="stat-card overflow-hidden">
                <div class="p-4 bg-green-50 border-b border-green-100">
                    <h2 class="text-lg font-semibold text-green-700">样书分类统计</h2>
                </div>
                <div class="p-4 md:p-6">
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">数字教材</span>
                            <span id="digitalBooks" class="font-medium text-base md:text-lg">-</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">纸质教材</span>
                            <span id="physicalBooks" class="font-medium text-base md:text-lg">-</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">国家规划</span>
                            <span id="nationalBooks" class="font-medium text-base md:text-lg">-</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs md:text-sm text-gray-500">省级规划</span>
                            <span id="provincialBooks" class="font-medium text-base md:text-lg">-</span>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <canvas id="bookCategoryChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 最近活动 -->
            <div class="stat-card overflow-hidden">
                <div class="p-4 bg-indigo-50 border-b border-indigo-100">
                    <h2 class="text-lg font-semibold text-indigo-700">最近活动</h2>
                </div>
                <div class="p-4">
                    <ul id="recentActivities" class="space-y-3 max-h-72 overflow-y-auto">
                        <li class="flex items-start p-3 border-b border-gray-100">
                            <div class="bg-gray-200 rounded-full p-2 mr-3">
                                <i class="fas fa-spinner fa-spin text-gray-500"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-gray-500">加载中...</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 快捷操作区域 -->
            <div class="stat-card overflow-hidden">
                <div class="p-4 bg-gray-50 border-b border-gray-100">
                    <h2 class="text-lg font-semibold text-gray-700">快捷操作</h2>
                </div>
                <div class="p-4 md:p-6">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <a href="/manage_users" class="flex flex-col items-center p-3 md:p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition duration-300">
                            <i class="fas fa-users text-blue-500 text-xl md:text-2xl mb-2"></i>
                            <span class="text-blue-700 text-sm text-center">用户管理</span>
                        </a>
                        <a href="/admin_manage_samples" class="flex flex-col items-center p-3 md:p-4 bg-green-50 rounded-lg hover:bg-green-100 transition duration-300">
                            <i class="fas fa-book text-green-500 text-xl md:text-2xl mb-2"></i>
                            <span class="text-green-700 text-sm text-center">样书管理</span>
                        </a>
                        <a href="/admin_manage_sample_requests" class="flex flex-col items-center p-3 md:p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition duration-300">
                            <i class="fas fa-file-alt text-yellow-500 text-xl md:text-2xl mb-2"></i>
                            <span class="text-yellow-700 text-sm text-center">样书申请</span>
                        </a>
                        <a href="/admin_manage_exhibitions" class="flex flex-col items-center p-3 md:p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition duration-300">
                            <i class="fas fa-store text-purple-500 text-xl md:text-2xl mb-2"></i>
                            <span class="text-purple-700 text-sm text-center">书展管理</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let userChart = null;
            let sampleRequestChart = null;
            let exhibitionChart = null;
            let bookCategoryChart = null;
            
            // 加载统计数据
            function loadStatistics() {
                fetch('/api/admin/get_statistics')
                    .then(response => response.json())
                    .then(response => {
                        if(response.code === 0) {
                            updateStatistics(response.data);
                            updateCharts(response.data);
                            updateRecentActivities(response.data.recent_activities);
                        } else {
                            showMessage('获取统计数据失败: ' + response.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showMessage('网络错误，请稍后重试');
                    });
            }
            
            // 更新统计数据
            function updateStatistics(data) {
                // 更新用户统计
                document.getElementById('totalUsers').textContent = data.user_stats.total;
                document.getElementById('teacherCount').textContent = data.user_stats.teacher;
                document.getElementById('publisherCount').textContent = data.user_stats.publisher;
                document.getElementById('dealerCount').textContent = data.user_stats.dealer;
                document.getElementById('adminCount').textContent = data.user_stats.admin;
                
                // 更新样书统计
                document.getElementById('totalSamples').textContent = data.sample_stats.total;
                
                // 更新样书分类统计
                document.getElementById('digitalBooks').textContent = data.sample_stats.digital;
                document.getElementById('physicalBooks').textContent = data.sample_stats.physical;
                document.getElementById('nationalBooks').textContent = data.sample_stats.national;
                document.getElementById('provincialBooks').textContent = data.sample_stats.provincial;
                
                // 更新样书申请统计
                document.getElementById('totalSampleRequests').textContent = data.sample_request_stats.total;
                document.getElementById('pendingSampleRequests').textContent = data.sample_request_stats.pending;
                document.getElementById('approvedSampleRequests').textContent = data.sample_request_stats.approved;
                document.getElementById('rejectedSampleRequests').textContent = data.sample_request_stats.rejected;
                
                // 更新书展活动统计
                document.getElementById('totalExhibitions').textContent = data.exhibition_stats.total;
                document.getElementById('draftExhibitions').textContent = data.exhibition_stats.draft;
                document.getElementById('publishedExhibitions').textContent = data.exhibition_stats.published;
                document.getElementById('cancelledExhibitions').textContent = data.exhibition_stats.cancelled;
                document.getElementById('endedExhibitions').textContent = data.exhibition_stats.ended;
            }
            
            // 更新最近活动
            function updateRecentActivities(activities) {
                const activitiesList = document.getElementById('recentActivities');
                activitiesList.innerHTML = '';
                
                if(activities.length === 0) {
                    activitiesList.innerHTML = '<li class="p-3 text-center text-gray-500">暂无活动</li>';
                    return;
                }
                
                activities.forEach(activity => {
                    const li = document.createElement('li');
                    li.className = 'flex items-start p-3 border-b border-gray-100 hover:bg-gray-50';
                    
                    const icon = getActivityIcon(activity.type);
                    const date = new Date(activity.timestamp);
                    
                    li.innerHTML = `
                        <div class="bg-${icon.color}-100 rounded-full p-2 mr-3">
                            <i class="fas ${icon.icon} text-${icon.color}-500"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-gray-800 text-sm">${activity.message}</p>
                            <p class="text-xs text-gray-500 mt-1">${formatTimeAgo(date)}</p>
                        </div>
                    `;
                    
                    activitiesList.appendChild(li);
                });
            }
            
            // 获取活动图标
            function getActivityIcon(type) {
                const icons = {
                    'user': { icon: 'fa-user-plus', color: 'blue' },
                    'sample_request': { icon: 'fa-file-alt', color: 'yellow' },
                    'exhibition': { icon: 'fa-store', color: 'purple' },
                    'book': { icon: 'fa-book', color: 'green' },
                    'login': { icon: 'fa-sign-in-alt', color: 'blue' },
                    'default': { icon: 'fa-bell', color: 'gray' }
                };
                
                return icons[type] || icons.default;
            }
            
            // 格式化时间
            function formatTimeAgo(date) {
                const now = new Date();
                const diffMs = now - date;
                const diffSec = Math.floor(diffMs / 1000);
                const diffMin = Math.floor(diffSec / 60);
                const diffHour = Math.floor(diffMin / 60);
                const diffDay = Math.floor(diffHour / 24);
                
                if(diffDay > 0) {
                    return diffDay + '天前';
                } else if(diffHour > 0) {
                    return diffHour + '小时前';
                } else if(diffMin > 0) {
                    return diffMin + '分钟前';
                } else {
                    return '刚刚';
                }
            }
            
            // 显示消息
            function showMessage(message) {
                // 简单的消息提示
                alert(message);
            }
            
            // 更新图表
            function updateCharts(data) {
                // 用户分布图表
                const userCtx = document.getElementById('userChart').getContext('2d');
                if(userChart) {
                    userChart.destroy();
                }
                
                userChart = new Chart(userCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['教师', '出版社', '经销商', '管理员'],
                        datasets: [{
                            data: [
                                data.user_stats.teacher,
                                data.user_stats.publisher,
                                data.user_stats.dealer,
                                data.user_stats.admin
                            ],
                            backgroundColor: [
                                '#60A5FA', // 蓝色
                                '#34D399', // 绿色
                                '#FBBF24', // 黄色
                                '#A78BFA'  // 紫色
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    boxWidth: 12,
                                    padding: 10
                                }
                            }
                        }
                    }
                });
                
                // 样书申请状态图表
                const sampleRequestCtx = document.getElementById('sampleRequestChart').getContext('2d');
                if(sampleRequestChart) {
                    sampleRequestChart.destroy();
                }
                
                sampleRequestChart = new Chart(sampleRequestCtx, {
                    type: 'bar',
                    data: {
                        labels: ['待处理', '已通过', '已拒绝'],
                        datasets: [{
                            label: '样书申请',
                            data: [
                                data.sample_request_stats.pending,
                                data.sample_request_stats.approved,
                                data.sample_request_stats.rejected
                            ],
                            backgroundColor: [
                                '#FBBF24', // 黄色
                                '#34D399', // 绿色
                                '#F87171'  // 红色
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
                
                // 书展活动状态图表
                const exhibitionCtx = document.getElementById('exhibitionChart').getContext('2d');
                if(exhibitionChart) {
                    exhibitionChart.destroy();
                }
                
                exhibitionChart = new Chart(exhibitionCtx, {
                    type: 'pie',
                    data: {
                        labels: ['草稿', '已发布', '已取消', '已结束'],
                        datasets: [{
                            data: [
                                data.exhibition_stats.draft,
                                data.exhibition_stats.published,
                                data.exhibition_stats.cancelled,
                                data.exhibition_stats.ended
                            ],
                            backgroundColor: [
                                '#93C5FD', // 浅蓝
                                '#34D399', // 绿色
                                '#F87171', // 红色
                                '#A78BFA'  // 紫色
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    boxWidth: 12,
                                    padding: 10
                                }
                            }
                        }
                    }
                });
                
                // 样书分类统计图表
                const bookCategoryCtx = document.getElementById('bookCategoryChart').getContext('2d');
                if(bookCategoryChart) {
                    bookCategoryChart.destroy();
                }
                
                bookCategoryChart = new Chart(bookCategoryCtx, {
                    type: 'bar',
                    data: {
                        labels: ['数字教材', '纸质教材', '国家规划', '省级规划'],
                        datasets: [{
                            label: '样书分类',
                            data: [
                                data.sample_stats.digital,
                                data.sample_stats.physical,
                                data.sample_stats.national,
                                data.sample_stats.provincial
                            ],
                            backgroundColor: [
                                '#3B82F6', // 蓝色
                                '#10B981', // 绿色
                                '#F59E0B', // 橙色
                                '#8B5CF6'  // 紫色
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }
            
            // 绑定刷新按钮事件
            document.getElementById('refreshStats').addEventListener('click', function() {
                const icon = this.querySelector('i');
                icon.classList.add('animate-spin');
                
                loadStatistics();
                
                setTimeout(() => {
                    icon.classList.remove('animate-spin');
                }, 1000);
            });
            
            // 初始加载
            loadStatistics();
            
            // 响应式布局处理
            function handleResize() {
                if (window.innerWidth < 768) {
                    // 移动端优化
                    if (userChart) userChart.options.plugins.legend.display = false;
                    if (exhibitionChart) exhibitionChart.options.plugins.legend.display = false;
                    if (bookCategoryChart) bookCategoryChart.options.plugins.legend.display = false;
                } else {
                    // 桌面端还原
                    if (userChart) userChart.options.plugins.legend.display = true;
                    if (exhibitionChart) exhibitionChart.options.plugins.legend.display = true;
                    if (bookCategoryChart) bookCategoryChart.options.plugins.legend.display = true;
                }
                
                // 更新图表
                if (userChart) userChart.update();
                if (exhibitionChart) exhibitionChart.update();
                if (bookCategoryChart) bookCategoryChart.update();
            }
            
            // 监听窗口大小变化
            window.addEventListener('resize', handleResize);
            
            // 初始调用一次
            handleResize();
        });
    </script>
</body>
</html> 