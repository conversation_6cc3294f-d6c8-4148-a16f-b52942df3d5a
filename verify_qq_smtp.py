#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ邮箱SMTP连接验证脚本

此脚本用于验证QQ邮箱的SMTP连接配置是否正确。
测试465端口（SSL）和587端口（STARTTLS）的连接方式。
"""

import smtplib
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_qq_smtp_465():
    """测试QQ邮箱465端口SSL连接"""
    print("=" * 50)
    print("测试QQ邮箱465端口SSL连接")
    print("=" * 50)
    
    try:
        print("正在连接 smtp.qq.com:465 (SSL)...")
        server = smtplib.SMTP_SSL('smtp.qq.com', 465)
        server.ehlo()
        print("✓ SSL连接成功")
        
        # 注意：这里不会实际登录，因为需要真实的邮箱和授权码
        print("连接建立成功，可以进行登录测试")
        server.quit()
        return True
        
    except Exception as e:
        print(f"✗ SSL连接失败: {e}")
        return False

def test_qq_smtp_587():
    """测试QQ邮箱587端口STARTTLS连接"""
    print("\n" + "=" * 50)
    print("测试QQ邮箱587端口STARTTLS连接")
    print("=" * 50)
    
    try:
        print("正在连接 smtp.qq.com:587 (STARTTLS)...")
        server = smtplib.SMTP('smtp.qq.com', 587)
        server.ehlo()
        print("✓ 初始连接成功")
        
        print("正在启用STARTTLS...")
        server.starttls()
        server.ehlo()
        print("✓ STARTTLS启用成功")
        
        print("连接建立成功，可以进行登录测试")
        server.quit()
        return True
        
    except Exception as e:
        print(f"✗ STARTTLS连接失败: {e}")
        return False

def test_connection_with_config(smtp_host, smtp_port, use_tls, use_ssl):
    """使用我们的逻辑测试连接"""
    print(f"\n测试配置: {smtp_host}:{smtp_port}, TLS={use_tls}, SSL={use_ssl}")
    
    try:
        # 根据端口和配置智能选择连接方式
        if smtp_port == 465:
            # 465端口：标准SSL端口，使用SMTP_SSL
            print(f"使用SSL连接到 {smtp_host}:{smtp_port} (465端口)")
            server = smtplib.SMTP_SSL(smtp_host, smtp_port)
            server.ehlo()
        elif smtp_port == 587:
            # 587端口：STARTTLS端口，使用SMTP + STARTTLS
            print(f"使用STARTTLS连接到 {smtp_host}:{smtp_port} (587端口)")
            server = smtplib.SMTP(smtp_host, smtp_port)
            server.ehlo()
            server.starttls()
            server.ehlo()
        elif use_ssl:
            # 其他端口但启用了SSL，使用SMTP_SSL
            print(f"使用SSL连接到 {smtp_host}:{smtp_port} (SSL配置)")
            server = smtplib.SMTP_SSL(smtp_host, smtp_port)
            server.ehlo()
        elif use_tls:
            # 其他端口但启用了TLS，使用STARTTLS
            print(f"使用STARTTLS连接到 {smtp_host}:{smtp_port} (TLS配置)")
            server = smtplib.SMTP(smtp_host, smtp_port)
            server.ehlo()
            server.starttls()
            server.ehlo()
        else:
            # 普通连接（不推荐）
            print(f"使用普通连接到 {smtp_host}:{smtp_port}")
            server = smtplib.SMTP(smtp_host, smtp_port)
            server.ehlo()
        
        print("✓ 连接成功")
        server.quit()
        return True
        
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False

def main():
    """主测试函数"""
    print("QQ邮箱SMTP连接验证")
    print("此测试验证不同端口和配置的连接方式")
    print("注意：此测试仅验证连接，不进行实际登录\n")
    
    # 测试465端口SSL连接
    ssl_success = test_qq_smtp_465()
    
    # 测试587端口STARTTLS连接
    starttls_success = test_qq_smtp_587()
    
    # 测试我们的智能连接逻辑
    print("\n" + "=" * 50)
    print("测试智能连接逻辑")
    print("=" * 50)
    
    test_cases = [
        ('smtp.qq.com', 465, 0, 1, 'QQ邮箱465端口+SSL'),
        ('smtp.qq.com', 465, 1, 1, 'QQ邮箱465端口+TLS+SSL'),
        ('smtp.qq.com', 587, 1, 0, 'QQ邮箱587端口+TLS'),
        ('smtp.qq.com', 587, 1, 1, 'QQ邮箱587端口+TLS+SSL'),
    ]
    
    results = []
    for host, port, tls, ssl, desc in test_cases:
        print(f"\n{desc}:")
        success = test_connection_with_config(host, port, tls, ssl)
        results.append((desc, success))
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    for desc, success in results:
        status = "✓ 成功" if success else "✗ 失败"
        print(f"{desc}: {status}")
    
    print(f"\n基础连接测试:")
    print(f"465端口SSL: {'✓ 成功' if ssl_success else '✗ 失败'}")
    print(f"587端口STARTTLS: {'✓ 成功' if starttls_success else '✗ 失败'}")
    
    print("\n结论:")
    if ssl_success and starttls_success:
        print("✓ QQ邮箱的465和587端口都可以正常连接")
        print("✓ 推荐使用465端口+SSL配置，更稳定")
        print("✓ 587端口+STARTTLS也可以作为备选")
    else:
        print("⚠ 部分连接测试失败，请检查网络环境")
    
    print("\n配置建议:")
    print("- 推荐配置：smtp.qq.com:465 + use_ssl=1 + use_tls=0")
    print("- 备选配置：smtp.qq.com:587 + use_ssl=0 + use_tls=1")
    print("- 系统会根据端口自动选择正确的连接方式")

if __name__ == "__main__":
    main()
