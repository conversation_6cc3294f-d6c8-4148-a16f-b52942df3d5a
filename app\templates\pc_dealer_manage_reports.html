<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>报备管理</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        .tab-button {
            transition: all 0.3s ease;
        }
        .tab-button.active {
            border-bottom: 2px solid #3b82f6;
            color: #2563eb;
        }
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-pending {
            background-color: #FEF3C7;
            color: #D97706;
        }
        .status-approved {
            background-color: #D1FAE5;
            color: #059669;
        }
        .status-rejected {
            background-color: #FEE2E2;
            color: #DC2626;
        }
        .status-completed {
            background-color: #E0E7FF;
            color: #4F46E5;
        }
        .status-cancelled {
            background-color: #F3F4F6;
            color: #6B7280;
        }
        /* 模态框动画 */
        .modal-anim-enter {
            opacity: 0;
            transform: scale(0.95);
        }
        .modal-anim-enter-active {
            opacity: 1;
            transform: scale(1);
            transition: opacity 300ms, transform 300ms;
        }
        .modal-anim-exit {
            opacity: 1;
        }
        .modal-anim-exit-active {
            opacity: 0;
            transform: scale(0.95);
            transition: opacity 200ms, transform 200ms;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto p-4">
        <div class="flex items-center mb-6">
            <button id="applyReportBtn" class="px-5 py-2 bg-green-500 text-white font-medium rounded-md shadow-sm hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200 flex items-center">
                <i class="fas fa-plus mr-2"></i>报备申请
            </button>
        </div>
        
        <!-- 标签页 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="flex border-b">
                <button class="tab-button flex-1 py-3 px-4 text-center font-medium active" data-status="all">
                    全部报备
                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800" id="count-all">0</span>
                </button>
                <button class="tab-button flex-1 py-3 px-4 text-center font-medium" data-status="pending">
                    待审核
                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800" id="count-pending">0</span>
                </button>
                <button class="tab-button flex-1 py-3 px-4 text-center font-medium" data-status="approved">
                    已通过
                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800" id="count-approved">0</span>
                </button>
                <button class="tab-button flex-1 py-3 px-4 text-center font-medium" data-status="rejected">
                    已拒绝
                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800" id="count-rejected">0</span>
                </button>
            </div>
        </div>
        
        <!-- 搜索栏 -->
        <div class="bg-white p-4 rounded-lg shadow mb-4">
            <div class="flex">
                <input type="text" id="searchInput" placeholder="搜索样书名称、学校..." 
                       class="flex-1 border border-gray-300 rounded-l-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <button id="searchBtn" class="bg-blue-500 text-white px-6 py-2 rounded-r-lg hover:bg-blue-600 transition-colors duration-200">
                    <i class="fas fa-search mr-2"></i>搜索
                </button>
            </div>
        </div>
        
        <!-- 报备列表 -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-4 bg-white border-b border-gray-200 flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-clipboard-list text-blue-500 mr-2 text-lg"></i>
                    <span class="text-gray-700 font-medium text-lg">报备列表</span>
                </div>
                <span id="reportCount" class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">0 条</span>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                样书信息
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                推广学校
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                申请时间
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                发货费率
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                结算费率
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                推广费率
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                状态
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody id="reportTableBody" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页控件 -->
            <div id="pagination" class="px-6 py-3 flex items-center justify-between border-t border-gray-200">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button id="prevPageMobile" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </button>
                    <button id="nextPageMobile" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span id="startItem">1</span> 到第 <span id="endItem">10</span> 条，共 <span id="totalItems">0</span> 条
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <button id="prevPage" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">上一页</span>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <div id="pageNumbers" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                <!-- 页码将在这里动态生成 -->
                            </div>
                            <button id="nextPage" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">下一页</span>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模态框容器 -->
    <div id="modalContainer" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-2xl mx-auto overflow-hidden">
            <div class="bg-white text-gray-800 px-4 py-3 flex justify-between items-center border-b border-gray-200">
                <h3 id="modalTitle" class="font-medium text-lg">报备详情</h3>
                <button id="modalClose" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modalBody" class="p-4 max-h-[70vh] overflow-y-auto">
                <!-- 模态框内容将在这里动态插入 -->
            </div>
        </div>
    </div>
    
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 max-w-xs"></div>
    
    <script>
        // 获取样书费率的通用函数
        function getBookRates(book) {
            if (!book) return { shipping: '-', settlement: '-', promotion: '-' };
            
            const shipping = book.shipping_discount ? (book.shipping_discount * 100).toFixed(2) + '%' : '-';
            const settlement = book.settlement_discount ? (book.settlement_discount * 100).toFixed(2) + '%' : '-';
            let promotion;
            
            if (book.promotion_rate !== null && book.promotion_rate !== undefined) {
                promotion = (book.promotion_rate * 100).toFixed(2) + '%';
            } else if (book.shipping_discount && book.settlement_discount) {
                // 如果没有明确设置推广费率，则默认为发货折扣-结算折扣
                const calcRate = (book.shipping_discount - book.settlement_discount) * 100;
                promotion = calcRate.toFixed(2) + '%';
            } else {
                promotion = '-';
            }
            
            return {
                shipping: shipping,
                settlement: settlement, 
                promotion: promotion
            };
        }
        
        $(document).ready(function() {
            // 全局变量
            let currentStatus = 'all';
            let searchText = '';
            let currentPage = 1;
            let totalPages = 1;
            let totalItems = 0;
            let pageSize = 10;
            let publisherSortOrder = ''; // 存储出版社排序顺序
            let statusCounts = {
                all: 0,
                pending: 0,
                approved: 0,
                rejected: 0
            };
            
            // 消息容器
            const messageContainer = $('<div class="fixed top-4 right-4 z-50 space-y-2"></div>');
            $('body').append(messageContainer);
            
            // 绑定报备申请按钮点击事件
            $('#applyReportBtn').on('click', function() {
                const pageUrl = '/pc_dealer_report_samples'; // 报备申请页面路径
                const windowName = 'reportSamplesWindow'; // 窗口名称
                
                // 设置窗口参数
                const windowWidth = 1000;
                const windowHeight = 700;
                const left = (screen.width - windowWidth) / 2;
                const top = (screen.height - windowHeight) / 2;
                const windowFeatures = `width=${windowWidth},height=${windowHeight},left=${left},top=${top},resizable=yes,scrollbars=yes,status=yes`;
                
                // 在新窗口中打开报备申请页面
                window.open(pageUrl, windowName, windowFeatures);
            });
            
            // 模态框容器
            const modalContainer = $('#modalContainer');
            const modalClose = $('#modalClose');
            
            // 绑定关闭模态框事件
            modalClose.on('click', function() {
                modalContainer.addClass('hidden');
            });
            
            // 初始化加载
            loadReports();
            loadStatusCounts();
            
            // 绑定标签页点击事件
            $('.tab-button').on('click', function() {
                $('.tab-button').removeClass('active');
                $(this).addClass('active');
                
                currentStatus = $(this).data('status');
                currentPage = 1;
                loadReports();
            });
            
            // 绑定搜索按钮点击事件
            $('#searchBtn').on('click', function() {
                searchText = $('#searchInput').val().trim();
                currentPage = 1;
                loadReports();
            });
            
            // 绑定搜索框回车事件
            $('#searchInput').on('keypress', function(e) {
                if (e.which === 13) {
                    searchText = $(this).val().trim();
                    currentPage = 1;
                    loadReports();
                }
            });
            
            // 绑定分页按钮事件
            $('#prevPage, #prevPageMobile').on('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    loadReports();
                }
            });
            
            $('#nextPage, #nextPageMobile').on('click', function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    loadReports();
                }
            });
            
            // 加载各状态的报备数量
            function loadStatusCounts() {
                $.ajax({
                    url: '/api/dealer/get_report_counts',
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 0) {
                            statusCounts = response.data;
                            updateStatusCounters();
                        }
                    }
                });
            }
            
            // 更新状态计数器
            function updateStatusCounters() {
                $('#count-all').text(statusCounts.all || 0);
                $('#count-pending').text(statusCounts.pending || 0);
                $('#count-approved').text(statusCounts.approved || 0);
                $('#count-rejected').text(statusCounts.rejected || 0);
            }
            
            // 加载报备列表
            function loadReports() {
                $('#reportTableBody').html(`
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                        </td>
                    </tr>
                `);
                
                $.ajax({
                    url: '/api/dealer/get_reports',
                    type: 'GET',
                    data: {
                        status: currentStatus,
                        search: searchText,
                        page: currentPage,
                        limit: pageSize,
                        publisher_sort_order: publisherSortOrder
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            const { reports, total, current_page, publisher_sort_order } = response.data;
                            
                            // 更新总页数和报备数量
                            currentPage = current_page;
                            totalItems = total; // 保存总条目数
                            totalPages = Math.ceil(total / pageSize); // 计算总页数
                            $('#reportCount').text(`${total} 条`);
                            
                            // 保存出版社排序顺序，用于下次请求
                            if (publisher_sort_order) {
                                publisherSortOrder = publisher_sort_order;
                            }
                            
                            // 更新分页按钮状态
                            updatePaginationButtons();
                            
                            // 渲染报备列表
                            if (reports && reports.length > 0) {
                                // 按出版社分组
                                const groupedReports = {};
                                
                                // 分组
                                reports.forEach(report => {
                                    const publisherName = report.publisher_name || '未知出版社';
                                    if (!groupedReports[publisherName]) {
                                        groupedReports[publisherName] = [];
                                    }
                                    groupedReports[publisherName].push(report);
                                });
                                
                                let html = '';
                                
                                // 循环渲染每个分组
                                Object.keys(groupedReports).forEach(publisherName => {
                                    // 添加出版社分组标题
                                    html += `
                                        <tr>
                                            <td colspan="8" class="px-6 py-3 bg-gray-50">
                                                <h3 class="text-md font-medium text-gray-700">
                                                    <i class="fas fa-book-open text-blue-500 mr-2"></i>${publisherName}
                                                </h3>
                                            </td>
                                        </tr>
                                    `;
                                    
                                    // 渲染该出版社的报备
                                    groupedReports[publisherName].forEach(report => {
                                        const statusClass = getStatusClass(report.status);
                                        const statusText = getStatusText(report.status);
                                        
                                        html += `
                                            <tr class="hover:bg-gray-50 transition-colors">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        <div class="ml-4">
                                                            <div class="text-sm font-medium text-gray-900">${report.sample_name}</div>
                                                            <div class="text-sm text-gray-500">
                                                                ${report.author ? '作者: ' + report.author : ''}
                                                                ${report.isbn ? ' | ISBN: ' + report.isbn : ''}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900">${report.school_name}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900">${formatDate(report.created_at)}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900">${report.shipping_discount ? (report.shipping_discount * 100).toFixed(2) + '%' : '-'}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900">${report.settlement_discount ? (report.settlement_discount * 100).toFixed(2) + '%' : '-'}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900">${getReportPromotionRate(report)}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="status-badge ${statusClass}">${statusText}</span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <button class="text-green-600 hover:text-green-900 mr-3 view-detail-btn" data-id="${report.id}">
                                                        <i class="fas fa-eye mr-1"></i>查看详情
                                                    </button>
                                                    ${report.status === 'pending' ? 
                                                        `<button class="text-red-600 hover:text-red-900 revoke-btn" data-id="${report.id}">
                                                            <i class="fas fa-times mr-1"></i>撤销
                                                        </button>` : 
                                                        ''
                                                    }
                                                </td>
                                            </tr>
                                        `;
                                    });
                                });
                                
                                $('#reportTableBody').html(html);
                                
                                // 绑定查看详情按钮事件
                                $('.view-detail-btn').on('click', function() {
                                    const reportId = $(this).data('id');
                                    showReportDetail(reportId);
                                });
                                
                                // 绑定撤销按钮事件
                                $('.revoke-btn').on('click', function() {
                                    const reportId = $(this).data('id');
                                    showRevokeConfirmation(reportId);
                                });
                            } else {
                                $('#reportTableBody').html(`
                                    <tr>
                                        <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                            暂无报备记录
                                        </td>
                                    </tr>
                                `);
                            }
                        } else {
                            $('#reportTableBody').html(`
                                <tr>
                                    <td colspan="8" class="px-6 py-4 text-center text-red-500">
                                        加载失败: ${response.message}
                                    </td>
                                </tr>
                            `);
                        }
                    },
                    error: function() {
                        $('#reportTableBody').html(`
                            <tr>
                                <td colspan="8" class="px-6 py-4 text-center text-red-500">
                                    网络错误，请稍后重试
                                </td>
                            </tr>
                        `);
                    }
                });
            }
            
            // 更新分页按钮状态
            function updatePaginationButtons() {
                // 禁用或启用上一页按钮
                if (currentPage <= 1) {
                    $('#prevPage').addClass('opacity-50 cursor-not-allowed').attr('disabled', true);
                } else {
                    $('#prevPage').removeClass('opacity-50 cursor-not-allowed').attr('disabled', false);
                }
                
                // 禁用或启用下一页按钮
                if (currentPage >= totalPages) {
                    $('#nextPage').addClass('opacity-50 cursor-not-allowed').attr('disabled', true);
                } else {
                    $('#nextPage').removeClass('opacity-50 cursor-not-allowed').attr('disabled', false);
                }
                
                // 更新页码显示
                const startItem = totalItems > 0 ? (currentPage - 1) * pageSize + 1 : 0;
                const endItem = Math.min(startItem + pageSize - 1, totalItems);
                
                // 使用文本而不是对象引用
                $('#startItem').text(startItem);
                $('#endItem').text(endItem);
                $('#totalItems').text(totalItems);
                
                // 更新页码按钮
                $('#pageNumbers').text(`${currentPage} / ${totalPages}`);
            }
            
            // 获取报备的推广费率
            function getReportPromotionRate(report) {
                if (report.promotion_rate !== null && report.promotion_rate !== undefined) {
                    return (report.promotion_rate * 100).toFixed(2) + '%';
                } else if (report.shipping_discount && report.settlement_discount) {
                    const calcRate = (report.shipping_discount - report.settlement_discount) * 100;
                    return calcRate.toFixed(2) + '%';
                } else {
                    return '-';
                }
            }
            
            // 显示报备详情
            function showReportDetail(reportId) {
                $('#modalTitle').text('报备详情');
                $('#modalBody').html('<div class="text-center py-4"><i class="fas fa-spinner fa-spin mr-2"></i>加载中...</div>');
                modalContainer.removeClass('hidden');
                
                $.ajax({
                    url: '/api/dealer/get_report_detail',
                    type: 'GET',
                    data: { id: reportId },
                    success: function(response) {
                        if (response.code === 0) {
                            const report = response.data;
                            const statusClass = getStatusClass(report.status);
                            const statusText = getStatusText(report.status);
                            
                            let detailHtml = `
                                <div class="space-y-4">
                                    <div class="flex justify-between items-center">
                                        <h3 class="text-lg font-medium text-gray-900">${report.sample_name}</h3>
                                        <span class="status-badge ${statusClass}">${statusText}</span>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">出版社</p>
                                            <p class="mt-1">${report.publisher_name || '未知'}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">ISBN</p>
                                            <p class="mt-1">${report.isbn || '未知'}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">作者</p>
                                            <p class="mt-1">${report.author || '未知'}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">推广学校</p>
                                            <p class="mt-1">${report.school_name}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">申请时间</p>
                                            <p class="mt-1">${formatDate(report.created_at)}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">更新时间</p>
                                            <p class="mt-1">${formatDate(report.updated_at)}</p>
                                        </div>
                                    </div>
                                    
                                    <div class="bg-blue-50 p-4 rounded-md">
                                        <h4 class="text-md font-medium text-blue-800 mb-2">费率信息</h4>
                                        <div class="grid grid-cols-3 gap-4">
                                            <div>
                                                <p class="text-sm font-medium text-gray-500">发货费率</p>
                                                <p class="mt-1 text-blue-700">${report.shipping_discount ? (report.shipping_discount * 100).toFixed(2) + '%' : '-'}</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-500">结算费率</p>
                                                <p class="mt-1 text-blue-700">${report.settlement_discount ? (report.settlement_discount * 100).toFixed(2) + '%' : '-'}</p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-500">推广费率</p>
                                                <p class="mt-1 text-blue-700">${getReportPromotionRate(report)}</p>
                                            </div>
                                        </div>
                                    </div>
                            `;
                            
                            // 如果有冲突理由和附件
                            if (report.conflict_reason) {
                                detailHtml += `
                                    <div class="mt-4">
                                        <p class="text-sm font-medium text-gray-500">冲突处理理由</p>
                                        <p class="mt-1 p-2 bg-gray-50 rounded">${report.conflict_reason}</p>
                                    </div>
                                `;
                            }
                            
                            if (report.attachment) {
                                detailHtml += `
                                    <div class="mt-4">
                                        <p class="text-sm font-medium text-gray-500">证明材料</p>
                                        <a href="${report.attachment}" target="_blank" class="mt-1 inline-flex items-center text-gray-600 hover:underline">
                                            <i class="fas fa-file-alt mr-1"></i> 查看附件
                                        </a>
                                    </div>
                                `;
                            }
                            
                            // 如果有拒绝理由
                            if (report.reason && report.status === 'rejected') {
                                detailHtml += `
                                    <div class="mt-4">
                                        <p class="text-sm font-medium text-red-500">拒绝理由</p>
                                        <p class="mt-1 p-2 bg-red-50 rounded text-red-700">${report.reason}</p>
                                    </div>
                                `;
                            }
                            
                            // 操作按钮部分
                            detailHtml += `
                                <div class="mt-6 flex justify-end">
                                    ${report.status === 'pending' ? 
                                        `<button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md revoke-confirm-btn" data-id="${report.id}">
                                            <i class="fas fa-times mr-1"></i>撤销报备
                                        </button>` : 
                                        ''
                                    }
                                    <button class="ml-3 bg-blue-100 hover:bg-blue-200 text-blue-800 px-4 py-2 rounded-md close-modal-btn">
                                        关闭
                                    </button>
                                </div>
                            </div>
                            `;
                            
                            $('#modalBody').html(detailHtml);
                            
                            // 绑定关闭按钮事件
                            $('.close-modal-btn').on('click', function() {
                                modalContainer.addClass('hidden');
                            });
                            
                            // 绑定撤销确认按钮事件
                            $('.revoke-confirm-btn').on('click', function() {
                                const reportId = $(this).data('id');
                                showRevokeConfirmation(reportId);
                            });
                        } else {
                            $('#modalBody').html(`
                                <div class="text-center py-4 text-red-500">
                                    ${response.message || '加载报备详情失败'}
                                </div>
                                <div class="flex justify-center mt-4">
                                    <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md close-modal-btn">
                                        关闭
                                    </button>
                                </div>
                            `);
                            
                            $('.close-modal-btn').on('click', function() {
                                modalContainer.addClass('hidden');
                            });
                        }
                    },
                    error: function() {
                        $('#modalBody').html(`
                            <div class="text-center py-4 text-red-500">
                                网络错误，请稍后重试
                            </div>
                            <div class="flex justify-center mt-4">
                                <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md close-modal-btn">
                                    关闭
                                </button>
                            </div>
                        `);
                        
                        $('.close-modal-btn').on('click', function() {
                            modalContainer.addClass('hidden');
                        });
                    }
                });
            }
            
            // 显示撤销确认对话框
            function showRevokeConfirmation(reportId) {
                const confirmHtml = `
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-yellow-500 text-4xl mb-4"></i>
                        <h3 class="text-xl font-medium text-gray-900 mb-2">确认撤销报备？</h3>
                        <p class="text-gray-600 mb-6">撤销后将无法恢复，请确认是否继续。</p>
                        
                        <div class="flex justify-center space-x-4">
                            <button id="confirmRevokeBtn" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
                                确认撤销
                            </button>
                            <button id="cancelRevokeBtn" class="px-4 py-2 bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">
                                取消
                            </button>
                        </div>
                    </div>
                `;
                
                $('#modalTitle').text('撤销报备');
                $('#modalBody').html(confirmHtml);
                modalContainer.removeClass('hidden');
                
                // 绑定确认撤销按钮事件
                $('#confirmRevokeBtn').on('click', function() {
                    revokeReport(reportId);
                });
                
                // 绑定取消按钮事件
                $('#cancelRevokeBtn').on('click', function() {
                    modalContainer.addClass('hidden');
                });
            }
            
            // 撤销报备
            function revokeReport(reportId) {
                $.ajax({
                    url: '/api/dealer/revoke_report',
                    type: 'POST',
                    data: { id: reportId },
                    success: function(response) {
                        modalContainer.addClass('hidden');
                        
                        if (response.code === 0) {
                            showMessage('报备已成功撤销', 'success');
                            loadReports(); // 重新加载报备列表
                            loadStatusCounts(); // 重新加载状态计数
                        } else {
                            showMessage(response.message || '撤销报备失败', 'error');
                        }
                    },
                    error: function() {
                        modalContainer.addClass('hidden');
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                });
            }
            
            // 获取状态对应的CSS类
            function getStatusClass(status) {
                switch (status) {
                    case 'pending': return 'status-pending';
                    case 'approved': 
                    case 'approved_no_shipping':
                    case 'approved_pending_shipping':
                    case 'approved_shipped':
                        return 'status-approved';
                    case 'rejected': return 'status-rejected';
                    default: return '';
                }
            }
            
            // 获取状态对应的文本
            function getStatusText(status) {
                switch (status) {
                    case 'pending': return '待审核';
                    case 'approved': return '已通过';
                    case 'rejected': return '已拒绝';
                    default: return '未知状态';
                }
            }
            
            // 格式化日期
            function formatDate(dateStr) {
                if (!dateStr) return '未知';
                
                const date = new Date(dateStr);
                if (isNaN(date.getTime())) return dateStr;
                
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
            
            // 显示消息提示
            function showMessage(message, type = 'info') {
                const colors = {
                    success: 'bg-green-500',
                    error: 'bg-red-500',
                    info: 'bg-gray-600',
                    warning: 'bg-yellow-500'
                };
                
                const messageElement = $(`<div class="${colors[type]} text-white px-4 py-3 rounded-lg shadow-md mb-2"></div>`);
                messageElement.text(message);
                
                messageContainer.append(messageElement);
                
                setTimeout(() => {
                    messageElement.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 3000);
            }
        });
    </script>
</body>
</html>