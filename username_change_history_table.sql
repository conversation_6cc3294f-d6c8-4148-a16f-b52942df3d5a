-- 用户名修改时间表
-- 记录用户ID、原用户名、新用户名、修改时间
-- 用户ID为唯一键，当表中没有某个用户的记录时，则代表它没改过用户名
-- 没改过用户名的用户可以修改用户名，改过用户名的用户只能每半年内修改一次

DROP TABLE IF EXISTS `username_change_history`;
CREATE TABLE `username_change_history` (
  `user_id` int NOT NULL COMMENT '用户ID',
  `old_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原用户名',
  `new_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '新用户名',
  `change_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`user_id`) USING BTREE,
  CONSTRAINT `fk_username_change_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户名修改时间表' ROW_FORMAT = DYNAMIC;
