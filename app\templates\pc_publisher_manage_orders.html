<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        
        /* 标签页样式 */
        .tab-active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
        }
        
        /* 状态标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid;
            line-height: 1;
            gap: 0.25rem;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .tag-pre-settlement {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border-color: #fbbf24;
        }
        
        .tag-pending-payment {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1e40af;
            border-color: #60a5fa;
        }
        
        .tag-settled {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border-color: #34d399;
        }
        
        /* 卡片样式 */
        .order-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .order-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }
        
        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
            margin: 4px 0;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #94a3b8;
            border-radius: 4px;
            border: 1px solid #e2e8f0;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #64748b;
        }
        
        /* 加载动画 */
        .loading-skeleton {
            background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>
</head>

<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-[9999] space-y-2"></div>

    <!-- 主容器 -->
    <div class="container mx-auto px-6 py-8">
        <!-- 页面头部 -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-slate-800">订单管理</h1>
                        <p class="text-slate-600">管理您的订单信息</p>
                    </div>
                </div>
                <button onclick="openUploadModal()" 
                        class="btn-primary h-12 px-6 text-white rounded-xl flex items-center space-x-2 shadow-lg">
                    <i class="fas fa-plus"></i>
                    <span>上传订单</span>
                </button>
            </div>
        </div>

        <!-- 筛选和标签页区域 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-6 mb-6">
            <!-- 标签页 -->
            <div class="flex space-x-1 mb-6">
                <button id="allTab" class="tab-button tab-active px-6 py-3 rounded-xl font-medium transition-all">
                    <i class="fas fa-list mr-2"></i>全部订单
                </button>
                <button id="preSettlementTab" class="tab-button px-6 py-3 rounded-xl font-medium text-slate-600 hover:text-slate-800 transition-all">
                    <i class="fas fa-clock mr-2"></i>预结算
                </button>
                <button id="pendingPaymentTab" class="tab-button px-6 py-3 rounded-xl font-medium text-slate-600 hover:text-slate-800 transition-all">
                    <i class="fas fa-credit-card mr-2"></i>待支付
                </button>
                <button id="settledTab" class="tab-button px-6 py-3 rounded-xl font-medium text-slate-600 hover:text-slate-800 transition-all">
                    <i class="fas fa-check-circle mr-2"></i>已结算
                </button>
            </div>

            <!-- 筛选区域 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- 搜索框 -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">搜索</label>
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索教材名称、ISBN、学校..." 
                               class="w-full h-12 pl-4 pr-12 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                            <i class="fas fa-search text-slate-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 学校筛选 -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">学校</label>
                    <div class="custom-select" id="schoolFilterContainer">
                        <div class="custom-select-trigger">
                            <span class="custom-select-text">全部学校</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div class="custom-select-dropdown">
                            <div class="custom-select-search">
                                <input type="text" placeholder="搜索学校..." />
                            </div>
                            <div class="custom-select-options" id="schoolFilterOptions">
                                <!-- 学校选项将动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-end space-x-3">
                    <button onclick="resetFilters()" 
                            class="h-12 px-6 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors flex items-center space-x-2">
                        <i class="fas fa-undo"></i>
                        <span>重置</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 订单列表 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden">
            <!-- 列表头部 -->
            <div class="px-6 py-4 border-b border-slate-200 bg-slate-50/50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h3 class="text-lg font-semibold text-slate-800">订单列表</h3>
                        <span id="totalCount" class="text-sm text-slate-500">共 0 条</span>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div id="loadingSpinner" class="flex items-center justify-center py-12">
                <div class="loading-skeleton w-8 h-8 rounded-full"></div>
                <span class="ml-3 text-slate-600">加载中...</span>
            </div>

            <!-- 订单列表内容 -->
            <div id="ordersList" class="hidden">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-slate-50">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">教材信息</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">学校/经销商</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">数量</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">金额</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">时间</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody id="ordersTableBody" class="bg-white divide-y divide-slate-200">
                            <!-- 订单行将动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 空状态 -->
            <div id="noOrdersMessage" class="hidden text-center py-12">
                <div class="w-24 h-24 mx-auto mb-4 bg-slate-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-shopping-cart text-slate-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-slate-900 mb-2">暂无订单</h3>
                <p class="text-slate-500">还没有任何订单记录</p>
            </div>
        </div>

        <!-- 分页组件 -->
        <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
            <div class="flex items-center">
                <p class="text-sm text-gray-700 mr-4">
                    第 <span id="currentPage" class="font-medium">1</span> 页，
                    共 <span id="totalPages" class="font-medium">1</span> 页，
                    共 <span id="totalItems" class="font-medium">0</span> 条
                </p>
            </div>
            <div class="flex gap-1">
                <button id="firstBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button id="prevBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    上一页
                </button>
                <div id="pageNumbers" class="flex gap-1">
                    <!-- 页码将动态生成 -->
                </div>
                <button id="nextBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    下一页
                </button>
                <button id="lastBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 上传订单模态框 -->
    <div id="uploadModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-xl font-semibold text-slate-800">上传订单</h3>
                    <button onclick="closeModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- 模态框内容 -->
                <div class="p-6 overflow-y-auto max-h-[70vh] custom-scrollbar">
                    <form id="uploadOrderForm">
                        <div class="space-y-6">
                            <!-- 学校选择 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">
                                    <i class="fas fa-school mr-2 text-blue-500"></i>学校
                                </label>
                                <div class="custom-select" id="schoolSelectContainer">
                                    <div class="custom-select-trigger">
                                        <span class="custom-select-text">请选择学校</span>
                                        <i class="fas fa-chevron-down custom-select-arrow"></i>
                                    </div>
                                    <div class="custom-select-dropdown">
                                        <div class="custom-select-search">
                                            <input type="text" placeholder="搜索学校..." />
                                        </div>
                                        <div class="custom-select-options" id="schoolSelectOptions">
                                            <!-- 学校选项将动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 教材选择 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">
                                    <i class="fas fa-book mr-2 text-green-500"></i>教材
                                </label>
                                <div class="border-2 border-dashed border-slate-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors bg-slate-50">
                                    <div class="mb-4">
                                        <i class="fas fa-book text-4xl text-slate-400 mb-3"></i>
                                        <p class="text-slate-600">点击选择教材</p>
                                    </div>
                                    <button type="button" onclick="openBookSelector()"
                                            class="btn-primary px-6 py-3 text-white rounded-xl flex items-center space-x-2 mx-auto">
                                        <i class="fas fa-search"></i>
                                        <span>选择教材</span>
                                    </button>
                                </div>

                                <!-- 已选择的教材显示 -->
                                <div id="selectedBooksDisplay" class="mt-4 space-y-3 hidden">
                                    <h4 class="text-sm font-medium text-slate-700">已选择的教材：</h4>
                                    <div id="selectedBooksList" class="space-y-2">
                                        <!-- 已选择的教材将动态显示 -->
                                    </div>
                                </div>
                            </div>

                            <!-- 发货数量 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">
                                    <i class="fas fa-boxes mr-2 text-orange-500"></i>发货数量
                                </label>
                                <input type="number" id="shippedQuantity" min="1"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="请输入发货数量">
                            </div>

                            <!-- 备注 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">
                                    <i class="fas fa-comment mr-2 text-purple-500"></i>备注（可选）
                                </label>
                                <textarea id="orderRemark" rows="3"
                                          class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                          placeholder="请输入备注信息..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 模态框按钮 -->
                <div class="p-6 border-t border-slate-200 flex justify-end space-x-3">
                    <button onclick="closeModal()"
                            class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                        取消
                    </button>
                    <button onclick="submitUploadOrder()"
                            class="btn-primary px-6 py-3 text-white rounded-xl flex items-center space-x-2">
                        <i class="fas fa-upload"></i>
                        <span>提交订单</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 对账模态框 -->
    <div id="reconciliationModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-xl font-semibold text-slate-800">订单对账</h3>
                    <button onclick="closeReconciliationModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- 模态框内容 -->
                <div class="p-6">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-slate-800 mb-2">数量不一致</h4>
                        <p class="text-slate-600">检测到订单数量不一致，请确认对账信息</p>
                    </div>

                    <div class="space-y-4">
                        <div class="bg-slate-50 rounded-xl p-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-slate-600">出版社数量：</span>
                                <span id="publisherQuantity" class="font-semibold text-slate-800">0</span>
                            </div>
                        </div>
                        <div class="bg-slate-50 rounded-xl p-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-slate-600">经销商数量：</span>
                                <span id="dealerQuantity" class="font-semibold text-slate-800">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模态框按钮 -->
                <div class="p-6 border-t border-slate-200 flex justify-end space-x-3">
                    <button onclick="closeReconciliationModal()"
                            class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                        稍后处理
                    </button>
                    <button onclick="confirmReconciliation()"
                            class="btn-primary px-6 py-3 text-white rounded-xl flex items-center space-x-2">
                        <i class="fas fa-check"></i>
                        <span>确认对账</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单详情模态框 -->
    <div id="orderDetailModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200 flex-shrink-0">
                    <h3 class="text-xl font-semibold text-slate-800">订单详情</h3>
                    <button onclick="closeOrderDetailModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- 模态框内容 -->
                <div class="flex-1 overflow-y-auto custom-scrollbar">
                    <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 基本信息 -->
                        <div class="bg-slate-50 rounded-xl p-4">
                            <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                                <i class="fas fa-info-circle text-blue-500 mr-2"></i>基本信息
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-slate-600">订单号：</span>
                                    <span id="detailOrderNumber" class="font-medium text-slate-800">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">教材名称：</span>
                                    <span id="detailBookName" class="font-medium text-slate-800">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">ISBN：</span>
                                    <span id="detailISBN" class="font-medium text-slate-800">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">学校：</span>
                                    <span id="detailSchoolName" class="font-medium text-slate-800">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">创建时间：</span>
                                    <span id="detailCreatedAt" class="font-medium text-slate-800">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- 数量和金额信息 -->
                        <div class="bg-slate-50 rounded-xl p-4">
                            <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                                <i class="fas fa-calculator text-green-500 mr-2"></i>数量金额
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-slate-600">出版社数量：</span>
                                    <span id="detailPublisherQuantity" class="font-medium text-slate-800">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">经销商数量：</span>
                                    <span id="detailDealerQuantity" class="font-medium text-slate-800">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">单价：</span>
                                    <span id="detailUnitPrice" class="font-medium text-slate-800">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">总金额：</span>
                                    <span id="detailTotalPrice" class="font-medium text-slate-800">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">退货数量：</span>
                                    <span id="detailReturnedQuantity" class="font-medium text-slate-800">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- 状态信息 -->
                        <div class="bg-slate-50 rounded-xl p-4">
                            <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                                <i class="fas fa-flag text-orange-500 mr-2"></i>状态信息
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-slate-600">对账状态：</span>
                                    <span id="detailReconciliationStatus" class="tag">-</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-slate-600">支付状态：</span>
                                    <span id="detailPaymentStatus" class="tag">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">出版社确认：</span>
                                    <span id="detailPublisherConfirm" class="font-medium text-slate-800">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">经销商确认：</span>
                                    <span id="detailDealerConfirm" class="font-medium text-slate-800">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- 经销商信息 -->
                        <div class="bg-slate-50 rounded-xl p-4">
                            <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                                <i class="fas fa-building text-purple-500 mr-2"></i>经销商信息
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-slate-600">经销商：</span>
                                    <span id="detailDealerName" class="font-medium text-slate-800">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">公司：</span>
                                    <span id="detailDealerCompany" class="font-medium text-slate-800">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">联系电话：</span>
                                    <span id="detailDealerPhone" class="font-medium text-slate-800">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-slate-600">匹配订单ID：</span>
                                    <span id="detailMatchedOrderId" class="font-medium text-slate-800">-</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 备注信息 -->
                    <div class="mt-6 bg-slate-50 rounded-xl p-4">
                        <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                            <i class="fas fa-comment text-blue-500 mr-2"></i>备注信息
                        </h4>
                        <div id="detailRemark" class="text-slate-700 bg-white rounded-lg p-3 min-h-[60px]">
                            暂无备注
                        </div>
                    </div>

                    <!-- 对账历史 -->
                    <div class="mt-6 bg-slate-50 rounded-xl p-4">
                        <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                            <i class="fas fa-history text-indigo-500 mr-2"></i>对账历史
                        </h4>
                        <div id="detailReconciliationHistory" class="space-y-2">
                            <!-- 对账历史将动态加载 -->
                        </div>
                    </div>
                    </div>
                </div>

                <!-- 模态框按钮 -->
                <div class="p-6 border-t border-slate-200 flex justify-end space-x-3 flex-shrink-0">
                    <button onclick="closeOrderDetailModal()"
                            class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 无匹配订单提示模态框 -->
    <div id="noMatchModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-xl font-semibold text-slate-800">无法对账</h3>
                    <button onclick="closeNoMatchModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- 模态框内容 -->
                <div class="p-6 text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-info-circle text-blue-600 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-800 mb-2">此订单暂无匹配的经销商订单</h4>
                    <p class="text-slate-600 mb-4">该订单为出版社直接上传，目前没有对应的经销商订单需要对账。</p>
                    <div class="bg-slate-50 rounded-lg p-4 mb-4">
                        <div class="text-sm text-slate-700">
                            <div class="flex justify-between mb-2">
                                <span>教材名称：</span>
                                <span id="noMatchBookName" class="font-medium">-</span>
                            </div>
                            <div class="flex justify-between mb-2">
                                <span>学校：</span>
                                <span id="noMatchSchoolName" class="font-medium">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>出版社数量：</span>
                                <span id="noMatchQuantity" class="font-medium">-</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-xs text-slate-500">如需对账，请等待经销商上传对应的订单后再操作。</p>
                </div>

                <!-- 模态框按钮 -->
                <div class="p-6 border-t border-slate-200 flex justify-center">
                    <button onclick="closeNoMatchModal()"
                            class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors">
                        我知道了
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }

        .custom-select-trigger {
            width: 100%;
            height: 48px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
        }

        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }

        /* 滚动条样式 */
        .custom-select-options::-webkit-scrollbar {
            width: 4px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }
    </style>

    <script>
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let pageSize = 10;
        let currentStatus = '';
        let searchTerm = '';
        let selectedSchoolId = '';
        let selectedBooks = [];
        let schoolFilterSelect = null;
        let schoolSelectForOrder = null;
        let messageId = 0;

        // 页面加载完成后初始化
        $(document).ready(function() {
            init();
        });

        // 初始化函数
        function init() {
            // 初始化自定义选择器
            initCustomSelects();

            // 绑定事件
            bindEvents();

            // 加载数据
            loadSchools();
            loadOrders();
        }

        // 初始化自定义选择器
        function initCustomSelects() {
            // 筛选区域的学校选择器
            schoolFilterSelect = new CustomSelect('schoolFilterContainer', {
                placeholder: '全部学校',
                onSelect: function(value, text) {
                    selectedSchoolId = value;
                    resetPagination();
                    loadOrders();
                }
            });

            // 订单上传的学校选择器
            schoolSelectForOrder = new CustomSelect('schoolSelectContainer', {
                placeholder: '请选择学校',
                onSelect: function(value, text) {
                    // 学校选择逻辑
                }
            });
        }

        // 绑定事件
        function bindEvents() {
            // 标签页切换
            $('.tab-button').click(function() {
                $('.tab-button').removeClass('tab-active').addClass('text-slate-600');
                $(this).removeClass('text-slate-600').addClass('tab-active');

                const tabId = $(this).attr('id');
                if (tabId === 'allTab') {
                    currentStatus = '';
                } else if (tabId === 'preSettlementTab') {
                    currentStatus = 'pre_settlement';
                } else if (tabId === 'pendingPaymentTab') {
                    currentStatus = 'pending_payment';
                } else if (tabId === 'settledTab') {
                    currentStatus = 'settled';
                }

                resetPagination();
                loadOrders();
            });

            // 搜索框
            $('#searchInput').on('input', debounce(function() {
                searchTerm = $(this).val().trim();
                resetPagination();
                loadOrders();
            }, 300));

            // 分页按钮
            $('#firstBtn').click(() => goToPage(1));
            $('#prevBtn').click(() => goToPage(currentPage - 1));
            $('#nextBtn').click(() => goToPage(currentPage + 1));
            $('#lastBtn').click(() => goToPage(totalPages));
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 重置分页
        function resetPagination() {
            currentPage = 1;
        }

        // 跳转到指定页
        function goToPage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadOrders();
            }
        }

        // 加载学校数据
        function loadSchools() {
            $.ajax({
                url: '/api/common/get_schools',
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        const schools = response.data || [];
                        const schoolOptions = [
                            {value: '', text: '全部学校'},
                            ...schools.map(school => ({
                                value: school.id,
                                text: school.name
                            }))
                        ];

                        schoolFilterSelect.setOptions(schoolOptions);
                        schoolSelectForOrder.setOptions(schools.map(school => ({
                            value: school.id,
                            text: school.name
                        })));
                    }
                },
                error: function() {
                    showMessage('加载学校数据失败', 'error');
                }
            });
        }

        // 加载订单列表
        function loadOrders() {
            showLoading();

            const params = {
                search: searchTerm,
                page: currentPage,
                limit: pageSize
            };

            if (currentStatus) {
                params.reconciliation_status = currentStatus;
            }

            if (selectedSchoolId) {
                params.school_id = selectedSchoolId;
            }

            $.ajax({
                url: '/api/publisher/get_orders',
                type: 'GET',
                data: params,
                success: function(response) {
                    hideLoading();

                    if (response.code === 0) {
                        const orders = response.data.orders || [];
                        const pagination = response.data.pagination || {};

                        renderOrders(orders);
                        updatePagination(pagination);

                        if (orders.length > 0) {
                            showOrdersList();
                        } else {
                            showNoOrders();
                        }
                    } else {
                        showMessage('加载订单失败: ' + response.message, 'error');
                        showNoOrders();
                    }
                },
                error: function() {
                    hideLoading();
                    showMessage('网络错误，请稍后再试', 'error');
                    showNoOrders();
                }
            });
        }

        // 安全的数字格式化函数
        function formatPrice(price) {
            const num = parseFloat(price || 0);
            return isNaN(num) ? '0.00' : num.toFixed(2);
        }

        // 安全的数量格式化函数
        function formatQuantity(quantity) {
            const num = parseInt(quantity || 0);
            return isNaN(num) ? 0 : num;
        }

        // 渲染订单列表
        function renderOrders(orders) {
            const tbody = $('#ordersTableBody');
            tbody.empty();

            orders.forEach(order => {
                const statusInfo = getStatusInfo(order.reconciliation_status || 'pre_settlement');
                const row = `
                    <tr class="hover:bg-slate-50">
                        <td class="px-6 py-4">
                            <div class="flex flex-col">
                                <div class="text-sm font-medium text-slate-900">${order.sample_name || '未知教材'}</div>
                                <div class="text-xs text-slate-500">ISBN: ${order.isbn || '未知'}</div>
                                <div class="text-xs text-slate-500">订单号: ${order.order_number || '无'}</div>
                                ${order.remark ? `<div class="text-xs text-slate-500 mt-1">备注: ${order.remark}</div>` : ''}
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-slate-900">${order.dealer_company || '直接上传'}</div>
                            <div class="text-xs text-slate-500">${order.school_name || '未知学校'}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-slate-900">${formatQuantity(order.shipped_quantity)} 本</div>
                            ${order.matched_order_id ?
                                `<div class="text-xs text-slate-500">经销商: ${formatQuantity(order.dealer_quantity)}本</div>` :
                                `<div class="text-xs text-slate-400">无经销商匹配订单</div>`}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-slate-900">¥${formatPrice(order.total_price)}</div>
                            <div class="text-xs text-slate-500">单价: ¥${formatPrice(order.unit_price)}</div>
                        </td>
                        <td class="px-6 py-4">
                            <span class="tag ${statusInfo.class}">
                                <i class="${statusInfo.icon}"></i>
                                <span>${statusInfo.text}</span>
                            </span>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-slate-900">${order.created_at || '未知'}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex space-x-2">
                                <button onclick="viewOrderDetail(${order.id})"
                                        class="text-blue-600 hover:text-blue-800 text-sm">
                                    <i class="fas fa-eye mr-1"></i>查看
                                </button>
                                ${order.reconciliation_status === 'pre_settlement' ?
                                    `<button onclick="showReconciliationModal(${order.id})"
                                             class="text-orange-600 hover:text-orange-800 text-sm">
                                        <i class="fas fa-balance-scale mr-1"></i>对账
                                    </button>` : ''}
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // 获取状态信息
        function getStatusInfo(status) {
            const statusMap = {
                'pre_settlement': {
                    class: 'tag-pre-settlement',
                    icon: 'fas fa-clock',
                    text: '预结算'
                },
                'pending_payment': {
                    class: 'tag-pending-payment',
                    icon: 'fas fa-credit-card',
                    text: '待支付'
                },
                'settled': {
                    class: 'tag-settled',
                    icon: 'fas fa-check-circle',
                    text: '已结算'
                }
            };
            return statusMap[status] || statusMap['pre_settlement'];
        }

        // 更新分页信息
        function updatePagination(pagination) {
            totalPages = pagination.total_pages || 1;
            const total = pagination.total || 0;

            $('#currentPage').text(currentPage);
            $('#totalPages').text(totalPages);
            $('#totalItems').text(total);
            $('#totalCount').text(total);

            // 更新按钮状态
            $('#firstBtn').prop('disabled', currentPage <= 1);
            $('#prevBtn').prop('disabled', currentPage <= 1);
            $('#nextBtn').prop('disabled', currentPage >= totalPages);
            $('#lastBtn').prop('disabled', currentPage >= totalPages);

            // 渲染页码
            renderPageNumbers();
        }

        // 渲染页码按钮
        function renderPageNumbers() {
            const container = $('#pageNumbers');
            container.empty();

            const pageNumbers = getPageNumbers(currentPage, totalPages);

            pageNumbers.forEach(pageNumber => {
                if (pageNumber === '...') {
                    container.append(`
                        <span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700">
                            ...
                        </span>
                    `);
                } else {
                    const isActive = pageNumber === currentPage;
                    const activeClass = isActive ? 'bg-blue-50 text-blue-600 border-blue-500' : 'bg-white text-gray-700';

                    container.append(`
                        <button onclick="goToPage(${pageNumber})"
                                class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md hover:bg-gray-50 ${activeClass}">
                            ${pageNumber}
                        </button>
                    `);
                }
            });
        }

        // 获取页码数组
        function getPageNumbers(currentPage, totalPages) {
            const pageNumbers = [];

            if (totalPages <= 7) {
                for (let i = 1; i <= totalPages; i++) {
                    pageNumbers.push(i);
                }
            } else {
                pageNumbers.push(1);

                if (currentPage <= 4) {
                    pageNumbers.push(2, 3, 4, 5);
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages);
                } else if (currentPage >= totalPages - 3) {
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
                    pageNumbers.push(totalPages);
                } else {
                    pageNumbers.push('...');
                    pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages);
                }
            }

            return pageNumbers;
        }

        // 显示/隐藏相关函数
        function showLoading() {
            $('#loadingSpinner').removeClass('hidden');
            $('#ordersList').addClass('hidden');
            $('#noOrdersMessage').addClass('hidden');
        }

        function hideLoading() {
            $('#loadingSpinner').addClass('hidden');
        }

        function showOrdersList() {
            $('#ordersList').removeClass('hidden');
            $('#noOrdersMessage').addClass('hidden');
        }

        function showNoOrders() {
            $('#ordersList').addClass('hidden');
            $('#noOrdersMessage').removeClass('hidden');
        }

        // 重置筛选
        function resetFilters() {
            $('#searchInput').val('');
            searchTerm = '';
            selectedSchoolId = '';
            schoolFilterSelect.reset();

            // 重置标签页
            $('.tab-button').removeClass('tab-active').addClass('text-slate-600');
            $('#allTab').removeClass('text-slate-600').addClass('tab-active');
            currentStatus = '';

            resetPagination();
            loadOrders();
        }

        // 打开上传订单模态框
        function openUploadModal() {
            selectedBooks = [];
            $('#uploadModal').removeClass('hidden');
            $('#uploadOrderForm')[0].reset();
            $('#selectedBooksDisplay').addClass('hidden');
            schoolSelectForOrder.reset();
        }

        // 关闭模态框
        function closeModal() {
            $('#uploadModal').addClass('hidden');
        }

        // 打开公共样书选择器
        function openBookSelector() {
            // 使用公共样书选择器
            window.open('/common/book_selector', 'bookSelector',
                       'width=1200,height=800,scrollbars=yes,resizable=yes');

            // 监听来自选择器的消息
            window.addEventListener('message', function(event) {
                if (event.data.type === 'SELECTED_BOOKS_FROM_SELECTOR') {
                    handleBookSelection(event.data.books);
                }
            }, { once: true });
        }

        // 处理样书选择回调
        function handleBookSelection(books) {
            if (books && books.length > 0) {
                selectedBooks = books;
                displaySelectedBooks();
            }
        }

        // 显示已选择的教材
        function displaySelectedBooks() {
            const container = $('#selectedBooksList');
            container.empty();

            if (selectedBooks.length > 0) {
                $('#selectedBooksDisplay').removeClass('hidden');

                selectedBooks.forEach((book, index) => {
                    const bookItem = `
                        <div class="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-slate-900">${book.name}</div>
                                <div class="text-xs text-slate-500">ISBN: ${book.isbn || '无'}</div>
                                <div class="text-xs text-slate-500">价格: ¥${formatPrice(book.price)}</div>
                            </div>
                            <button onclick="removeSelectedBook(${index})"
                                    class="text-red-500 hover:text-red-700 ml-3">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;
                    container.append(bookItem);
                });
            } else {
                $('#selectedBooksDisplay').addClass('hidden');
            }
        }

        // 移除已选择的教材
        function removeSelectedBook(index) {
            selectedBooks.splice(index, 1);
            displaySelectedBooks();
        }

        // 提交上传订单
        function submitUploadOrder() {
            const schoolId = schoolSelectForOrder.getValue();
            const shippedQuantity = $('#shippedQuantity').val();
            const remark = $('#orderRemark').val();

            // 验证表单
            if (!schoolId) {
                showMessage('请选择学校', 'error');
                return;
            }

            if (selectedBooks.length === 0) {
                showMessage('请选择教材', 'error');
                return;
            }

            if (!shippedQuantity || shippedQuantity <= 0) {
                showMessage('请输入有效的发货数量', 'error');
                return;
            }

            // 准备提交数据
            const orderData = {
                school_id: schoolId,
                books: selectedBooks.map(book => ({
                    book_id: book.id,
                    shipped_quantity: parseInt(shippedQuantity),
                    remark: remark
                }))
            };

            // 提交订单
            $.ajax({
                url: '/api/publisher/upload_orders',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(orderData),
                success: function(response) {
                    if (response.code === 0) {
                        // 检查是否需要对账
                        if (response.need_reconciliation === true) {
                            closeModal();

                            // 显示对账模态框
                            if (response.order_id) {
                                showReconciliationModal(response.order_id);
                            }

                            showMessage('订单上传成功，数量不一致，需要对账', 'warning');
                        } else if (response.need_reconciliation === false && response.matched_order && response.matched_order.reconciliation_status === 'pending_payment') {
                            showMessage('订单上传成功，已匹配到经销商订单，数量一致，已进入待支付状态', 'success');
                            closeModal();
                        } else {
                            const message = response.message || '订单上传成功';
                            showMessage(message, 'success');
                            closeModal();
                        }

                        loadOrders();
                    } else {
                        showMessage('订单上传失败: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后再试', 'error');
                }
            });
        }

        // 显示对账模态框
        function showReconciliationModal(orderId) {
            // 获取订单详情
            $.ajax({
                url: `/api/publisher/get_order_detail?id=${orderId}`,
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        const order = response.data;

                        // 检查是否有匹配的经销商订单
                        if (!order.matched_order_id) {
                            // 显示无匹配订单提示模态框
                            showNoMatchModal(order);
                        } else {
                            // 显示正常的对账模态框
                            $('#publisherQuantity').text(formatQuantity(order.shipped_quantity));
                            $('#dealerQuantity').text(formatQuantity(order.dealer_quantity));
                            $('#reconciliationModal').removeClass('hidden');
                            $('#reconciliationModal').data('order-id', orderId);
                        }
                    } else {
                        showMessage('获取订单详情失败: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后再试', 'error');
                }
            });
        }

        // 关闭对账模态框
        function closeReconciliationModal() {
            $('#reconciliationModal').addClass('hidden');
        }

        // 确认对账
        function confirmReconciliation() {
            const orderId = $('#reconciliationModal').data('order-id');

            $.ajax({
                url: `/api/publisher/confirm_reconciliation/${orderId}`,
                type: 'POST',
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('对账确认成功', 'success');
                        closeReconciliationModal();
                        loadOrders();
                    } else {
                        showMessage('对账确认失败: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后再试', 'error');
                }
            });
        }

        // 查看订单详情
        function viewOrderDetail(orderId) {
            // 获取订单详情
            $.ajax({
                url: `/api/publisher/get_order_detail?id=${orderId}`,
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        showOrderDetailModal(response.data);
                    } else {
                        showMessage('获取订单详情失败: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后再试', 'error');
                }
            });
        }

        // 消息提示函数
        function showMessage(message, type = 'info') {
            const messageContainer = $('#messageContainer');
            const id = ++messageId;

            const typeClasses = {
                'success': 'bg-green-500 text-white',
                'error': 'bg-red-500 text-white',
                'warning': 'bg-yellow-500 text-white',
                'info': 'bg-blue-500 text-white'
            };

            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };

            const messageElement = $(`
                <div id="message-${id}" class="flex items-center p-4 rounded-lg shadow-lg ${typeClasses[type]} transform translate-x-full transition-transform duration-300">
                    <i class="${icons[type]} mr-3"></i>
                    <span>${message}</span>
                    <button onclick="removeMessage(${id})" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `);

            messageContainer.append(messageElement);

            // 动画显示
            setTimeout(() => {
                messageElement.removeClass('translate-x-full');
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }

        // 移除消息
        function removeMessage(id) {
            const messageElement = $(`#message-${id}`);
            messageElement.addClass('translate-x-full');
            setTimeout(() => {
                messageElement.remove();
            }, 300);
        }

        // 自定义选择器类
        class CustomSelect {
            constructor(containerId, options = {}) {
                this.container = $(`#${containerId}`);
                this.options = options;
                this.selectedValue = '';
                this.selectedText = options.placeholder || '请选择';
                this.isOpen = false;
                this.allOptions = [];

                this.init();
            }

            init() {
                this.bindEvents();
            }

            bindEvents() {
                const trigger = this.container.find('.custom-select-trigger');
                const dropdown = this.container.find('.custom-select-dropdown');
                const searchInput = this.container.find('.custom-select-search input');

                // 点击触发器
                trigger.on('click', (e) => {
                    e.stopPropagation();
                    this.toggle();
                });

                // 搜索
                searchInput.on('input', (e) => {
                    this.filterOptions(e.target.value);
                });

                // 点击外部关闭
                $(document).on('click', (e) => {
                    if (!this.container.is(e.target) && this.container.has(e.target).length === 0) {
                        this.close();
                    }
                });
            }

            toggle() {
                if (this.isOpen) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                this.container.addClass('active');
                this.isOpen = true;
                this.container.find('.custom-select-search input').focus();
            }

            close() {
                this.container.removeClass('active');
                this.isOpen = false;
                this.container.find('.custom-select-search input').val('');
                this.filterOptions('');
            }

            setOptions(options) {
                this.allOptions = options;
                this.renderOptions(options);
            }

            renderOptions(options) {
                const optionsContainer = this.container.find('.custom-select-options');
                optionsContainer.empty();

                if (options.length === 0) {
                    optionsContainer.append(`
                        <div class="custom-select-option no-results">
                            暂无选项
                        </div>
                    `);
                    return;
                }

                options.forEach(option => {
                    const optionElement = $(`
                        <div class="custom-select-option" data-value="${option.value}">
                            ${option.text}
                        </div>
                    `);

                    if (option.value === this.selectedValue) {
                        optionElement.addClass('selected');
                    }

                    optionElement.on('click', () => {
                        this.selectOption(option.value, option.text);
                    });

                    optionsContainer.append(optionElement);
                });
            }

            filterOptions(searchTerm) {
                const filteredOptions = this.allOptions.filter(option =>
                    option.text.toLowerCase().includes(searchTerm.toLowerCase())
                );
                this.renderOptions(filteredOptions);
            }

            selectOption(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.container.find('.custom-select-text').text(text);
                this.close();

                if (this.options.onSelect) {
                    this.options.onSelect(value, text);
                }
            }

            getValue() {
                return this.selectedValue;
            }

            getText() {
                return this.selectedText;
            }

            reset() {
                this.selectedValue = '';
                this.selectedText = this.options.placeholder || '请选择';
                this.container.find('.custom-select-text').text(this.selectedText);
                this.container.find('.custom-select-option').removeClass('selected');
            }
        }

        // 显示订单详情模态框
        function showOrderDetailModal(order) {
            // 填充基本信息
            $('#detailOrderNumber').text(order.order_number || '无');
            $('#detailBookName').text(order.sample_name || '未知教材');
            $('#detailISBN').text(order.isbn || '无');
            $('#detailSchoolName').text(order.school_name || '未知学校');
            $('#detailCreatedAt').text(order.created_at || '未知');

            // 填充数量金额信息
            $('#detailPublisherQuantity').text(formatQuantity(order.shipped_quantity) + ' 本');
            $('#detailDealerQuantity').text(order.matched_order_id ?
                formatQuantity(order.dealer_quantity) + ' 本' : '无经销商匹配订单');
            $('#detailUnitPrice').text('¥' + formatPrice(order.unit_price));
            $('#detailTotalPrice').text('¥' + formatPrice(order.total_price));
            $('#detailReturnedQuantity').text(formatQuantity(order.returned_quantity) + ' 本');

            // 填充状态信息
            const reconciliationStatus = getStatusInfo(order.reconciliation_status || 'pre_settlement');
            $('#detailReconciliationStatus').removeClass().addClass('tag ' + reconciliationStatus.class).html(`
                <i class="${reconciliationStatus.icon}"></i>
                <span>${reconciliationStatus.text}</span>
            `);

            const paymentStatus = order.payment_status == 1 ?
                { class: 'tag-settled', icon: 'fas fa-check-circle', text: '已支付' } :
                { class: 'tag-pre-settlement', icon: 'fas fa-clock', text: '未支付' };
            $('#detailPaymentStatus').removeClass().addClass('tag ' + paymentStatus.class).html(`
                <i class="${paymentStatus.icon}"></i>
                <span>${paymentStatus.text}</span>
            `);

            $('#detailPublisherConfirm').text(getConfirmStatusText(order.publisher_confirm_status));
            $('#detailDealerConfirm').text(getConfirmStatusText(order.dealer_confirm_status));

            // 填充经销商信息（优先显示公司名称）
            $('#detailDealerName').text(order.dealer_company || order.dealer_name || '直接上传');
            $('#detailDealerCompany').text(order.dealer_company || '无');
            $('#detailDealerPhone').text(order.dealer_phone || '无');
            $('#detailMatchedOrderId').text(order.matched_order_id || '无');

            // 填充备注信息
            $('#detailRemark').text(order.remark || '暂无备注');

            // 加载对账历史
            loadReconciliationHistory(order.id);

            // 显示模态框
            $('#orderDetailModal').removeClass('hidden');
        }

        // 获取确认状态文本
        function getConfirmStatusText(status) {
            const statusMap = {
                'confirmed': '已确认',
                'unconfirmed': '未确认',
                'pending': '待确认'
            };
            return statusMap[status] || '未知';
        }

        // 加载对账历史
        function loadReconciliationHistory(orderId) {
            const container = $('#detailReconciliationHistory');
            container.html('<div class="text-center text-slate-500">加载中...</div>');

            $.ajax({
                url: `/api/publisher/get_order_reconciliation_history?order_id=${orderId}`,
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        const history = response.data || [];
                        renderReconciliationHistory(history);
                    } else {
                        container.html('<div class="text-center text-slate-500">加载失败</div>');
                    }
                },
                error: function() {
                    container.html('<div class="text-center text-slate-500">加载失败</div>');
                }
            });
        }

        // 渲染对账历史
        function renderReconciliationHistory(history) {
            const container = $('#detailReconciliationHistory');
            container.empty();

            if (history.length === 0) {
                container.html('<div class="text-center text-slate-500">暂无对账历史</div>');
                return;
            }

            history.forEach(item => {
                const roleText = getRoleText(item.user_role);

                // 主要内容：优先显示备注内容（具体操作描述）
                let mainContent = item.remark;
                let showActionType = false;

                // 如果没有备注，才显示操作类型，但要淡化处理
                if (!mainContent) {
                    mainContent = getActionText(item.action_type);
                    showActionType = true;
                }

                const historyItem = `
                    <div class="bg-white rounded-lg p-3 border border-slate-200">
                        <div class="flex justify-between items-start mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-medium text-slate-800">${item.user_name || '未知用户'}</span>
                                <span class="text-xs px-2 py-1 bg-slate-100 text-slate-600 rounded">${roleText}</span>
                                ${showActionType ? `<span class="text-xs px-2 py-1 bg-slate-50 text-slate-400 rounded">${getActionText(item.action_type)}</span>` : ''}
                            </div>
                            <span class="text-xs text-slate-500">${item.created_at}</span>
                        </div>
                        <div class="text-sm ${showActionType ? 'text-slate-500' : 'text-slate-800 font-medium'} mb-1">${mainContent}</div>
                        ${item.old_quantity && item.new_quantity ?
                            `<div class="text-xs text-slate-500 bg-slate-50 rounded px-2 py-1 inline-block">数量变更：${item.old_quantity} → ${item.new_quantity}</div>` : ''}
                    </div>
                `;
                container.append(historyItem);
            });
        }

        // 获取操作类型文本
        function getActionText(actionType) {
            const actionMap = {
                // 基础操作
                'create': '创建订单',
                'update': '更新订单',
                'upload': '上传订单',
                'modify': '修改订单',
                'delete': '删除订单',
                'edit': '编辑订单',
                'add': '添加订单',

                // 对账相关
                'confirm_reconciliation': '确认对账',
                'reconcile': '对账处理',
                'reconciliation': '对账操作',
                'quantity_adjustment': '数量调整',
                'adjust': '调整数量',
                'accumulate': '累加订单',

                // 匹配相关
                'auto_match': '自动匹配',
                'manual_match': '手动匹配',
                'match': '匹配订单',
                'unmatch': '取消匹配',

                // 确认相关
                'confirm': '确认操作',
                'approve': '审批通过',
                'reject': '拒绝操作',
                'accept': '接受',
                'decline': '拒绝',

                // 支付相关
                'payment': '支付操作',
                'pay': '支付',
                'paid': '已支付',
                'unpaid': '未支付',
                'payment_confirm': '确认支付',
                'settlement': '结算',
                'settle': '结算',

                // 状态变更
                'status_change': '状态变更',
                'submit': '提交',
                'cancel': '取消',
                'complete': '完成',
                'pending': '待处理',
                'processing': '处理中',
                'finished': '已完成',

                // 系统操作
                'system': '系统操作',
                'auto': '自动操作',
                'manual': '手动操作',
                'import': '导入',
                'export': '导出',
                'sync': '同步',
                'backup': '备份',
                'restore': '恢复',

                // 其他常见操作
                'view': '查看',
                'download': '下载',
                'print': '打印',
                'send': '发送',
                'receive': '接收',
                'transfer': '转移',
                'copy': '复制',
                'move': '移动'
            };
            return actionMap[actionType] || actionType;
        }

        // 获取角色文本
        function getRoleText(role) {
            const roleMap = {
                'publisher': '出版社',
                'dealer': '经销商',
                'admin': '管理员'
            };
            return roleMap[role] || role;
        }

        // 关闭订单详情模态框
        function closeOrderDetailModal() {
            $('#orderDetailModal').addClass('hidden');
        }

        // 显示无匹配订单模态框
        function showNoMatchModal(order) {
            $('#noMatchBookName').text(order.sample_name || '未知教材');
            $('#noMatchSchoolName').text(order.school_name || '未知学校');
            $('#noMatchQuantity').text(formatQuantity(order.shipped_quantity) + ' 本');
            $('#noMatchModal').removeClass('hidden');
        }

        // 关闭无匹配订单模态框
        function closeNoMatchModal() {
            $('#noMatchModal').addClass('hidden');
        }
    </script>
</body>
</html>
