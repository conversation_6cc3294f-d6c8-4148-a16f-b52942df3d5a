<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>教材巡展</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
        }
        .status-draft { background-color: #e5e7eb; color: #4b5563; border: 1px solid #d1d5db; }
        .status-published { background-color: #d1fae5; color: #065f46; border: 1px solid #6ee7b7; }
        .status-cancelled { background-color: #fee2e2; color: #b91c1c; border: 1px solid #fca5a5; }
        .status-ended { background-color: #dbeafe; color: #1e40af; border: 1px solid #93c5fd; }
        .status-registerable { background-color: #fef3c7; color: #92400e; border: 1px solid #fcd34d; }
        .status-registration-closed { background-color: #f3f4f6; color: #6b7280; border: 1px solid #d1d5db; }
        .tab-active { border-bottom: 2px solid #3b82f6; color: #2563eb; }
        .exhibition-card { transition: all 0.2s; border-left: 4px solid transparent; }
        .exhibition-card:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
        .exhibition-card.draft { border-left: 4px solid #9ca3af; }
        .exhibition-card.published { border-left: 4px solid #34d399; }
        .exhibition-card.cancelled { border-left: 4px solid #f87171; }
        .exhibition-card.ended { border-left: 4px solid #60a5fa; }
        .required-field::after { content: '*'; color: #ef4444; margin-left: 0.25rem; }
        .message-toast { transition: all 0.3s ease; opacity: 1; transform: translateY(0); }
        .animate-fadeIn { animation: fadeIn 0.3s ease-in; }
        .animate-fadeOut { animation: fadeOut 0.3s ease-out; }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-20px); }
        }
        
        /* 移动端优化 */
        @media (max-width: 640px) {
            .container { padding-left: 0.75rem; padding-right: 0.75rem; }
            .tab-btn { font-size: 0.8rem; padding: 0.5rem 0.25rem; }
            .status-badge { font-size: 0.65rem; padding: 0.15rem 0.35rem; }
            .modal-content { width: 95% !important; }
            .exhibition-card { padding: 0.75rem !important; }
            .form-section { padding: 0.75rem !important; }
            th, td { padding: 0.5rem 0.3rem !important; font-size: 0.75rem !important; }
        }
        
        /* 平板优化 */
        @media (min-width: 641px) and (max-width: 1024px) {
            .container { max-width: 100% !important; }
            .modal-content { width: 90% !important; }
        }
        
        /* 模态框优化 */
        .modal-content {
            max-height: 90vh;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- <h1 class="text-2xl font-bold text-center text-blue-600 mb-6">书展活动管理</h1> -->
        
        <!-- 筛选和搜索区域 -->
        <div class="bg-white rounded-lg shadow p-4 mb-6">
            <div class="flex flex-col gap-4">
                <div class="flex flex-col sm:flex-row sm:flex-wrap gap-4">
                    <div class="relative w-full sm:w-48">
                        <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">状态筛选</label>
                        <select id="statusFilter" class="block w-full bg-white border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-8 shadow-sm">
                            <option value="all">全部状态</option>
                            <option value="draft">未发布</option>
                            <option value="published">已发布</option>
                            <option value="cancelled">已取消</option>
                            <option value="ended">已结束</option>
                            <option value="registerable">可报名</option>
                            <option value="registration-closed">报名截止</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700" style="top: 22px;">
                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                        </div>
                    </div>
                    
                    <div class="relative w-full sm:w-48">
                        <label for="dateFilter" class="block text-sm font-medium text-gray-700 mb-1">时间筛选</label>
                        <select id="dateFilter" class="block w-full bg-white border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-8 shadow-sm">
                            <option value="all">全部时间</option>
                            <option value="7days">最近7天</option>
                            <option value="30days">最近30天</option>
                            <option value="90days">最近90天</option>
                            <option value="custom">自定义时间</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700" style="top: 22px;">
                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                        </div>
                    </div>
                    
                    <div id="customDateContainer" class="hidden w-full sm:w-auto">
                        <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                            <input type="date" id="startDate" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
                            <span class="text-center">至</span>
                            <input type="date" id="endDate" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center w-full">
                    <input type="text" id="searchInput" placeholder="搜索书展主题..." 
                           class="border border-gray-300 rounded-l-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm w-full">
                    <button id="searchBtn" class="bg-blue-500 text-white px-4 py-2 rounded-r-md hover:bg-blue-600 shadow-sm">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 添加书展按钮 -->
        <div class="mb-6">
            <button id="addExhibitionBtn" class="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 shadow-sm flex items-center">
                <i class="fas fa-plus mr-2"></i> 添加书展活动
            </button>
        </div>
        
        <!-- 标签页导航 -->
        <div class="bg-white rounded-lg shadow mb-6 overflow-x-auto">
            <div class="flex border-b min-w-max">
                <button class="tab-btn tab-active py-3 font-medium text-center px-3 sm:px-4 whitespace-nowrap" data-tab="all">
                    全部书展
                    <span class="ml-1 bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full text-xs" id="allCount">0</span>
                </button>
                <button class="tab-btn py-3 font-medium text-center px-3 sm:px-4 whitespace-nowrap" data-tab="initiated">
                    我发起
                    <span class="ml-1 bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full text-xs" id="initiatedCount">0</span>
                </button>
                <button class="tab-btn py-3 font-medium text-center px-3 sm:px-4 whitespace-nowrap" data-tab="published">
                    已发布
                    <span class="ml-1 bg-green-100 text-green-700 px-2 py-0.5 rounded-full text-xs" id="publishedCount">0</span>
                </button>
                <button class="tab-btn py-3 font-medium text-center px-3 sm:px-4 whitespace-nowrap" data-tab="draft">
                    未发布
                    <span class="ml-1 bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full text-xs" id="draftCount">0</span>
                </button>
                <button class="tab-btn py-3 font-medium text-center px-3 sm:px-4 whitespace-nowrap" data-tab="ended">
                    已结束
                    <span class="ml-1 bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full text-xs" id="endedCount">0</span>
                </button>
                <button class="tab-btn py-3 font-medium text-center px-3 sm:px-4 whitespace-nowrap" data-tab="cancelled">
                    已取消
                    <span class="ml-1 bg-red-100 text-red-700 px-2 py-0.5 rounded-full text-xs" id="cancelledCount">0</span>
                </button>
            </div>
        </div>
        
        <!-- 书展列表区域 -->
        <div id="exhibitionsContainer" class="space-y-4">
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-spinner fa-spin text-2xl mb-3"></i>
                <p>加载中，请稍候...</p>
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div id="pagination" class="mt-6 flex justify-center">
            <div class="flex bg-white rounded-lg shadow overflow-hidden">
                <button id="prevPageBtn" class="px-4 py-2 border-r border-gray-200 hover:bg-gray-50 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div id="pageNumbers" class="flex"></div>
                <button id="nextPageBtn" class="px-4 py-2 border-l border-gray-200 hover:bg-gray-50 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 书展详情模态框 -->
    <div id="detailModalContainer" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center overflow-y-auto">
        <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-4xl mx-auto my-8">
            <div class="bg-blue-500 text-white px-4 py-3 flex justify-between items-center">
                <h3 id="detailModalTitle" class="font-medium">书展活动详情</h3>
                <button class="modal-close-btn text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="detailModalBody" class="p-4 max-h-[70vh] overflow-y-auto">
                <!-- 详情内容将在这里动态插入 -->
            </div>
            <div id="detailModalFooter" class="px-4 py-3 bg-gray-50 flex justify-end space-x-2 rounded-b-lg">
                <button id="detailModalCloseBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">关闭</button>
                <div id="detailModalActionBtns" class="flex space-x-2">
                    <!-- 操作按钮将在这里动态插入 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 新增/编辑书展模态框 -->
    <div id="editModalContainer" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center overflow-y-auto">
        <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-4xl mx-auto my-8">
            <div class="bg-blue-500 text-white px-4 py-3 flex justify-between items-center">
                <h3 id="editModalTitle" class="font-medium">添加书展活动</h3>
                <button class="modal-close-btn text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-4 max-h-[70vh] overflow-y-auto">
                <form id="exhibitionForm" class="space-y-4">
                    <input type="hidden" id="exhibitionId" name="exhibitionId">
                    
                    <!-- 基本信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-lg mb-3 text-gray-700">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="title" class="block text-sm font-medium text-gray-700 mb-1 required-field">书展主题</label>
                                <input type="text" id="title" name="title" placeholder="例如：25秋肇庆学院教材巡展" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label for="school" class="block text-sm font-medium text-gray-700 mb-1">发起学校</label>
                                <input type="text" id="school" name="school" class="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-100" readonly>
                            </div>
                            <div>
                                <label for="logoUpload" class="block text-sm font-medium text-gray-700 mb-1">活动Logo</label>
                                <div class="flex items-center">
                                    <label class="flex items-center justify-center border border-gray-300 border-dashed rounded-md w-full py-2 px-3 cursor-pointer hover:bg-gray-50">
                                        <i class="fas fa-upload mr-2 text-gray-500"></i>
                                        <span id="logoFileName" class="text-gray-500">选择文件上传</span>
                                        <input type="file" id="logoUpload" name="logoUpload" class="hidden" accept="image/*">
                                    </label>
                                </div>
                                <div id="logoPreviewContainer" class="mt-2 hidden">
                                    <img id="logoPreview" class="h-24 object-contain border rounded-md p-2" src="" alt="Logo预览">
                                    <button type="button" id="removeLogo" class="text-red-500 text-sm mt-1">
                                        <i class="fas fa-times mr-1"></i>移除
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">书展介绍</label>
                                <textarea id="description" name="description" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 时间信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-lg mb-3 text-gray-700">时间信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="startTime" class="block text-sm font-medium text-gray-700 mb-1 required-field">开始时间</label>
                                <input type="datetime-local" id="startTime" name="startTime" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label for="endTime" class="block text-sm font-medium text-gray-700 mb-1 required-field">结束时间</label>
                                <input type="datetime-local" id="endTime" name="endTime" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label for="registrationDeadline" class="block text-sm font-medium text-gray-700 mb-1 required-field">报名截止时间</label>
                                <input type="datetime-local" id="registrationDeadline" name="registrationDeadline" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 地点信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-lg mb-3 text-gray-700">地点信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="location" class="block text-sm font-medium text-gray-700 mb-1 required-field">具体地点</label>
                                <input type="text" id="location" name="location" placeholder="例如：第一教学楼108室" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label for="schoolAddress" class="block text-sm font-medium text-gray-700 mb-1 required-field">学校地址</label>
                                <input type="text" id="schoolAddress" name="schoolAddress" placeholder="例如：广州市天河区中山大道西2号" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 报备要求 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-lg mb-3 text-gray-700">进校报备</h4>
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <div class="flex items-center mb-3">
                                    <input type="checkbox" id="requiresRegistration" name="requiresRegistration" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                    <label for="requiresRegistration" class="ml-2 text-sm font-medium text-gray-700">是否需要进校报备</label>
                                </div>
                                <div id="registrationRequirementsContainer" class="hidden">
                                    <div class="mb-3">
                                        <label for="registrationRequirements" class="block text-sm font-medium text-gray-700 mb-1">报备要求</label>
                                        <textarea id="registrationRequirements" name="registrationRequirements" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                    </div>
                                    <div>
                                        <label for="registrationQrcode" class="block text-sm font-medium text-gray-700 mb-1">报备二维码</label>
                                        <div class="flex items-center">
                                            <label class="flex items-center justify-center border border-gray-300 border-dashed rounded-md w-full py-2 px-3 cursor-pointer hover:bg-gray-50">
                                                <i class="fas fa-upload mr-2 text-gray-500"></i>
                                                <span id="qrcodeFileName" class="text-gray-500">选择文件上传</span>
                                                <input type="file" id="registrationQrcode" name="registrationQrcode" class="hidden" accept="image/*">
                                            </label>
                                        </div>
                                        <div id="qrcodePreviewContainer" class="mt-2 hidden">
                                            <img id="qrcodePreview" class="h-24 object-contain border rounded-md p-2" src="" alt="二维码预览">
                                            <button type="button" id="removeQrcode" class="text-red-500 text-sm mt-1">
                                                <i class="fas fa-times mr-1"></i>移除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="allowsParking" name="allowsParking" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                    <label for="allowsParking" class="ml-2 text-sm font-medium text-gray-700">学校是否允许停车</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 其他要求 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-lg mb-3 text-gray-700">其他要求</h4>
                        <div>
                            <textarea id="requirements" name="requirements" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>
                    </div>
                    
                    <!-- 发起人信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-lg mb-3 text-gray-700">发起人信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="initiatorName" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                                <input type="text" id="initiatorName" name="initiatorName" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label for="initiatorPhone" class="block text-sm font-medium text-gray-700 mb-1">手机号</label>
                                <input type="tel" id="initiatorPhone" name="initiatorPhone" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label for="initiatorDepartment" class="block text-sm font-medium text-gray-700 mb-1">部门</label>
                                <input type="text" id="initiatorDepartment" name="initiatorDepartment" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label for="initiatorPosition" class="block text-sm font-medium text-gray-700 mb-1">职位</label>
                                <input type="text" id="initiatorPosition" name="initiatorPosition" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label for="initiatorEmail" class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                                <input type="email" id="initiatorEmail" name="initiatorEmail" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="px-4 py-3 bg-gray-50 flex justify-end space-x-2 rounded-b-lg">
                <button id="cancelEditBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">取消</button>
                <button id="saveAsDraftBtn" class="bg-blue-100 text-blue-700 px-4 py-2 rounded-md hover:bg-blue-200">保存为草稿</button>
                <button id="publishBtn" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">直接发布</button>
            </div>
        </div>
    </div>
    
    <!-- 参展人员模态框 -->
    <div id="participantsModalContainer" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-3xl mx-auto">
            <div class="bg-blue-500 text-white px-4 py-3 flex justify-between items-center">
                <h3 class="font-medium">查看参展人员</h3>
                <button class="modal-close-btn text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="participantsModalBody" class="p-4 max-h-[70vh] overflow-y-auto">
                <!-- 参展人员列表将在这里动态插入 -->
            </div>
            <div class="px-4 py-3 bg-gray-50 flex justify-end rounded-b-lg">
                <button id="closeParticipantsBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">关闭</button>
            </div>
        </div>
    </div>
    
    <!-- 确认模态框 -->
    <div id="confirmModalContainer" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-md mx-auto">
            <div class="bg-blue-500 text-white px-4 py-3 flex justify-between items-center">
                <h3 id="confirmModalTitle" class="font-medium">确认操作</h3>
                <button class="modal-close-btn text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="confirmModalBody" class="p-4">
                <!-- 确认内容将在这里动态插入 -->
            </div>
            <div class="px-4 py-3 bg-gray-50 flex justify-end space-x-2 rounded-b-lg">
                <button id="cancelConfirmBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">取消</button>
                <button id="confirmBtn" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">确认</button>
            </div>
        </div>
    </div>
    
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 max-w-xs"></div>

    <script>
    // 全局变量
    let currentPage = 1;
    let totalPages = 1;
    let pageSize = 10;
    let currentTab = 'all';
    let currentStatus = 'all';
    let searchText = '';
    let startDate = '';
    let endDate = '';

    // 页面加载完成后执行
    $(document).ready(function() {
        // 初始化加载书展列表
        loadExhibitions();
        
        // 绑定搜索按钮点击事件
        $('#searchBtn').click(function() {
            searchText = $('#searchInput').val();
            currentPage = 1;
            loadExhibitions();
        });
        
        // 绑定搜索框回车事件
        $('#searchInput').keypress(function(e) {
            if (e.which === 13) {
                searchText = $(this).val();
                currentPage = 1;
                loadExhibitions();
            }
        });
        
        // 绑定状态筛选事件
        $('#statusFilter').change(function() {
            currentStatus = $(this).val();
            currentPage = 1;
            loadExhibitions();
        });
        
        // 绑定日期筛选事件
        $('#dateFilter').change(function() {
            const value = $(this).val();
            
            // 隐藏自定义日期选择框
            $('#customDateContainer').addClass('hidden');
            
            if (value === 'custom') {
                // 显示自定义日期选择框
                $('#customDateContainer').removeClass('hidden');
            } else {
                // 清空日期范围
                startDate = '';
                endDate = '';
                
                // 设置预定义日期范围
                if (value !== 'all') {
                    const today = new Date();
                    endDate = formatDate(today);
                    
                    if (value === '7days') {
                        const date = new Date();
                        date.setDate(date.getDate() - 7);
                        startDate = formatDate(date);
                    } else if (value === '30days') {
                        const date = new Date();
                        date.setDate(date.getDate() - 30);
                        startDate = formatDate(date);
                    } else if (value === '90days') {
                        const date = new Date();
                        date.setDate(date.getDate() - 90);
                        startDate = formatDate(date);
                    }
                }
                
                // 加载数据
                currentPage = 1;
                loadExhibitions();
            }
        });
        
        // 自定义日期选择事件
        $('#startDate, #endDate').change(function() {
            startDate = $('#startDate').val();
            endDate = $('#endDate').val();
            
            if (startDate && endDate) {
                currentPage = 1;
                loadExhibitions();
            }
        });
        
        // 绑定标签切换事件
        $('.tab-btn').click(function() {
            // 移除所有标签的激活状态
            $('.tab-btn').removeClass('tab-active');
            
            // 设置当前标签为激活状态
            $(this).addClass('tab-active');
            
            // 更新当前标签
            currentTab = $(this).data('tab');
            
            // 重新加载数据
            currentPage = 1;
            loadExhibitions();
        });
        
        // 绑定分页事件
        $('#prevPageBtn').click(function() {
            if (currentPage > 1) {
                currentPage--;
                loadExhibitions();
            }
        });
        
        $('#nextPageBtn').click(function() {
            if (currentPage < totalPages) {
                currentPage++;
                loadExhibitions();
            }
        });
        
        // 绑定添加书展按钮点击事件
        $('#addExhibitionBtn').click(function() {
            // 重置表单
            resetExhibitionForm();
            
            // 设置标题
            $('#editModalTitle').text('添加书展活动');
            
            // 显示模态框
            $('#editModalContainer').removeClass('hidden');
        });
        
        // 绑定模态框关闭按钮点击事件
        $('.modal-close-btn, #detailModalCloseBtn, #cancelEditBtn, #cancelConfirmBtn').click(function() {
            // 隐藏相应的模态框
            $(this).closest('.fixed').addClass('hidden');
        });
        
        // 绑定关闭参展人员模态框按钮
        $('#closeParticipantsBtn').click(function() {
            // 只关闭参展人员模态框
            $('#participantsModalContainer').addClass('hidden');
        });
        
        // 绑定表单中的进校报备复选框事件
        $('#requiresRegistration').change(function() {
            if ($(this).is(':checked')) {
                $('#registrationRequirementsContainer').removeClass('hidden');
            } else {
                $('#registrationRequirementsContainer').addClass('hidden');
            }
        });
        
        // 绑定Logo上传预览
        $('#logoUpload').change(function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#logoPreview').attr('src', e.target.result);
                    $('#logoPreviewContainer').removeClass('hidden');
                    $('#logoFileName').text(file.name);
                }
                reader.readAsDataURL(file);
            }
        });
        
        // 绑定二维码上传预览
        $('#registrationQrcode').change(function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#qrcodePreview').attr('src', e.target.result);
                    $('#qrcodePreviewContainer').removeClass('hidden');
                    $('#qrcodeFileName').text(file.name);
                }
                reader.readAsDataURL(file);
            }
        });
        
        // 绑定移除Logo按钮点击事件
        $('#removeLogo').click(function() {
            $('#logoUpload').val('');
            $('#logoPreviewContainer').addClass('hidden');
            $('#logoFileName').text('选择文件上传');
        });
        
        // 绑定移除二维码按钮点击事件
        $('#removeQrcode').click(function() {
            $('#registrationQrcode').val('');
            $('#qrcodePreviewContainer').addClass('hidden');
            $('#qrcodeFileName').text('选择文件上传');
        });
        
        // 绑定保存为草稿按钮点击事件
        $('#saveAsDraftBtn').click(function() {
            saveExhibition('draft');
        });
        
        // 绑定直接发布按钮点击事件
        $('#publishBtn').click(function() {
            saveExhibition('published');
        });
    });

    // 加载书展列表
    function loadExhibitions() {
        // 显示加载中状态
        $('#exhibitionsContainer').html(`
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-spinner fa-spin text-2xl mb-3"></i>
                <p>加载中，请稍候...</p>
            </div>
        `);
        
        // 构建API请求参数
        const params = {
            page: currentPage,
            limit: pageSize,
            tab: currentTab,
            status: currentStatus,
            search: searchText
        };
        
        // 添加日期筛选参数
        if (startDate) {
            params.start_date = startDate;
        }
        
        if (endDate) {
            params.end_date = endDate;
        }
        
        // 发送请求
        $.ajax({
            url: '/api/teacher/get_exhibitions',
            type: 'GET',
            data: params,
            success: function(response) {
                if (response.code === 0) {
                    renderExhibitions(response.data);
                    updateStatusCounts(response.data.status_counts);
                    renderPagination(response.data.total);
                } else {
                    showMessage(response.message || '加载失败', 'error');
                    $('#exhibitionsContainer').html(`
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-exclamation-triangle text-2xl mb-3 text-yellow-500"></i>
                            <p>${response.message || '加载失败'}</p>
                        </div>
                    `);
                }
            },
            error: function() {
                showMessage('网络错误，请稍后再试', 'error');
                $('#exhibitionsContainer').html(`
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-exclamation-triangle text-2xl mb-3 text-yellow-500"></i>
                        <p>网络错误，请稍后再试</p>
                    </div>
                `);
            }
        });
    }

    // 渲染书展列表
    function renderExhibitions(data) {
        const exhibitions = data.exhibitions;
        
        if (exhibitions.length === 0) {
            $('#exhibitionsContainer').html(`
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-search text-2xl mb-3"></i>
                    <p>没有找到符合条件的书展活动</p>
                </div>
            `);
            return;
        }
        
        let html = '';
        
        exhibitions.forEach(exhibition => {
            // 获取状态样式和文本
            const statusInfo = getStatusInfo(exhibition.status);
            
            // 获取报名状态
            const registrationStatus = getRegistrationStatus(exhibition);
            
            // 构建卡片HTML
            html += `
                <div class="exhibition-card ${exhibition.status} bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow">
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3">
                        <div class="mb-2 sm:mb-0">
                            <h3 class="text-lg font-medium text-gray-800 mb-2">${exhibition.title}</h3>
                            <div class="flex flex-wrap gap-2">
                                <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
                                <span class="status-badge ${registrationStatus.class}">${registrationStatus.text}</span>
                            </div>
                        </div>
                        <div class="flex space-x-2 mt-2 sm:mt-0">
                            <button class="view-exhibition-btn bg-blue-50 text-blue-600 px-3 py-1 rounded-md hover:bg-blue-100 transition-colors" data-id="${exhibition.id}">
                                <i class="fas fa-eye mr-1"></i>查看
                            </button>
                            ${exhibition.is_initiator && exhibition.status !== 'cancelled' && exhibition.status !== 'ended' ? `
                                <button class="edit-exhibition-btn bg-green-50 text-green-600 px-3 py-1 rounded-md hover:bg-green-100 transition-colors" data-id="${exhibition.id}">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                            ` : ''}
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-3">
                        <div>
                            <p class="text-sm text-gray-500">学校</p>
                            <p class="text-gray-800">${exhibition.school_name}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">地点</p>
                            <p class="text-gray-800">${exhibition.location}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">学校地址</p>
                            <p class="text-gray-800">${exhibition.school_address || '无'}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">开始时间</p>
                            <p class="text-gray-800">${exhibition.start_time}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">结束时间</p>
                            <p class="text-gray-800">${exhibition.end_time}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">报名截止</p>
                            <p class="text-gray-800">${exhibition.registration_deadline}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">允许停车</p>
                            <p class="text-gray-800">${exhibition.allows_parking ? '是' : '否'}</p>
                        </div>
                    </div>
                    
                    <div class="flex flex-wrap justify-between items-center text-sm">
                        <div class="flex items-center text-gray-500">
                            <span class="mr-4"><i class="fas fa-user mr-1"></i>${exhibition.contact_name}</span>
                            <span><i class="fas fa-phone mr-1"></i>${exhibition.contact_phone}</span>
                        </div>
                        <div class="flex items-center text-gray-500 mt-2 sm:mt-0">
                            <span class="mr-4"><i class="fas fa-users mr-1"></i>已报名: ${exhibition.registrations_count}</span>
                            <span><i class="fas fa-clock mr-1"></i>创建于 ${exhibition.created_at}</span>
                        </div>
                    </div>
                </div>
            `;
        });
        
        $('#exhibitionsContainer').html(html);
        
        // 绑定查看按钮点击事件
        $('.view-exhibition-btn').click(function() {
            const id = $(this).data('id');
            viewExhibitionDetail(id);
        });
        
        // 绑定编辑按钮点击事件
        $('.edit-exhibition-btn').click(function() {
            const id = $(this).data('id');
            editExhibition(id);
        });
    }

    // 更新状态数量
    function updateStatusCounts(counts) {
        $('#allCount').text(counts.all || 0);
        $('#initiatedCount').text(counts.initiated || 0);
        $('#publishedCount').text(counts.published || 0);
        $('#draftCount').text(counts.draft || 0);
        $('#endedCount').text(counts.ended || 0);
        $('#cancelledCount').text(counts.cancelled || 0);
    }

    // 渲染分页
    function renderPagination(total) {
        totalPages = Math.ceil(total / pageSize);
        
        // 禁用或启用上一页、下一页按钮
        $('#prevPageBtn').prop('disabled', currentPage <= 1);
        $('#nextPageBtn').prop('disabled', currentPage >= totalPages);
        
        // 生成页码
        let pageHtml = '';
        const maxPageButtons = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
        let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
        
        if (endPage - startPage + 1 < maxPageButtons) {
            startPage = Math.max(1, endPage - maxPageButtons + 1);
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === currentPage;
            pageHtml += `
                <button class="page-number px-4 py-2 border-r border-gray-200 ${isActive ? 'bg-blue-50 text-blue-600 font-medium' : 'hover:bg-gray-50 text-gray-600'}" data-page="${i}">
                    ${i}
                </button>
            `;
        }
        
        $('#pageNumbers').html(pageHtml);
        
        // 绑定页码点击事件
        $('.page-number').click(function() {
            currentPage = parseInt($(this).data('page'));
            loadExhibitions();
        });
    }

    // 获取状态信息
    function getStatusInfo(status) {
        const statusMap = {
            'draft': { text: '未发布', class: 'status-draft' },
            'published': { text: '已发布', class: 'status-published' },
            'cancelled': { text: '已取消', class: 'status-cancelled' },
            'ended': { text: '已结束', class: 'status-ended' },
            'registerable': { text: '可报名', class: 'status-registerable' },
            'registration-closed': { text: '报名截止', class: 'status-registration-closed' }
        };
        
        return statusMap[status] || { text: '未知', class: '' };
    }

    // 重置书展表单
    function resetExhibitionForm() {
        $('#exhibitionForm')[0].reset();
        $('#exhibitionId').val('');
        $('#logoPreviewContainer').addClass('hidden');
        $('#qrcodePreviewContainer').addClass('hidden');
        $('#logoFileName').text('选择文件上传');
        $('#qrcodeFileName').text('选择文件上传');
        $('#registrationRequirementsContainer').addClass('hidden');
        
        // 获取当前学校名称
        $.ajax({
            url: '/api/teacher/get_teacher_info',
            type: 'GET',
            success: function(response) {
                if (response.code === 0 && response.data) {
                    $('#school').val(response.data.school_name || '');
                }
            }
        });
        
        // 获取当前用户信息填充发起人信息
        $.ajax({
            url: '/api/common/get_user_info',
            type: 'GET',
            success: function(response) {
                if (response.status === 'success' && response.user_info) {
                    $('#initiatorName').val(response.user_info.name || '');
                    $('#initiatorPhone').val(response.user_info.phone_number || '');
                }
            }
        });
    }

    // 格式化日期
    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    // 显示消息提示
    function showMessage(message, type = 'success') {
        const id = Date.now();
        const typeClass = type === 'success' ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200';
        const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';
        
        const messageHtml = `
            <div id="message-${id}" class="message-toast ${typeClass} px-4 py-3 rounded-lg shadow-md border mb-3 animate-fadeIn">
                <div class="flex items-center">
                    <i class="fas fa-${icon} mr-2"></i>
                    <span>${message}</span>
                </div>
            </div>
        `;
        
        $('#messageContainer').append(messageHtml);
        
        // 3秒后自动移除
        setTimeout(function() {
            $(`#message-${id}`).removeClass('animate-fadeIn').addClass('animate-fadeOut');
            setTimeout(function() {
                $(`#message-${id}`).remove();
            }, 300);
        }, 3000);
    }

    // 查看书展详情
    function viewExhibitionDetail(id) {
        $.ajax({
            url: '/api/teacher/get_exhibition_detail',
            type: 'GET',
            data: { id: id },
            success: function(response) {
                if (response.code === 0) {
                    renderExhibitionDetail(response.data);
                    $('#detailModalContainer').removeClass('hidden');
                } else {
                    showMessage(response.message || '获取详情失败', 'error');
                }
            },
            error: function() {
                showMessage('网络错误，请稍后再试', 'error');
            }
        });
    }

    // 渲染书展详情
    function renderExhibitionDetail(exhibition) {
        // 设置模态框标题
        $('#detailModalTitle').text(exhibition.title);
        
        // 获取状态信息
        const statusInfo = getStatusInfo(exhibition.status);
        
        // 获取报名状态
        const registrationStatus = getRegistrationStatus(exhibition);
        
        // 构建基本信息HTML
        let html = `
            <div class="space-y-6">
                <div>
                    <div class="flex flex-wrap gap-2 mb-2">
                        <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
                        <span class="status-badge ${registrationStatus.class}">${registrationStatus.text}</span>
                    </div>
                    <h3 class="text-xl font-medium text-gray-800">${exhibition.title}</h3>
                    ${exhibition.logo_url ? `
                        <div class="flex justify-center mt-4">
                            <img src="${exhibition.logo_url}" alt="活动Logo" class="object-contain max-h-32 border rounded p-1">
                        </div>
                    ` : ''}
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 bg-gray-50 p-4 rounded-lg">
                    <div>
                        <h4 class="font-medium text-gray-700 mb-2">基本信息</h4>
                        <div class="space-y-2">
                            <div class="flex">
                                <span class="text-gray-500 w-24">发起学校:</span>
                                <span class="text-gray-800">${exhibition.school_name}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">地点:</span>
                                <span class="text-gray-800">${exhibition.location}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">学校地址:</span>
                                <span class="text-gray-800">${exhibition.school_address || '无'}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">开始时间:</span>
                                <span class="text-gray-800">${exhibition.start_time}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">结束时间:</span>
                                <span class="text-gray-800">${exhibition.end_time}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">报名截止:</span>
                                <span class="text-gray-800">${exhibition.registration_deadline}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">允许停车:</span>
                                <span class="text-gray-800">${exhibition.allows_parking ? '是' : '否'}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-700 mb-2">发起人信息</h4>
                        <div class="space-y-2">
                            <div class="flex">
                                <span class="text-gray-500 w-24">姓名:</span>
                                <span class="text-gray-800">${exhibition.initiator.name}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">电话:</span>
                                <span class="text-gray-800">${exhibition.initiator.phone}</span>
                            </div>
                            ${exhibition.initiator.department ? `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">部门:</span>
                                    <span class="text-gray-800 ml-2">${exhibition.initiator.department}</span>
                                </div>
                            ` : ''}
                            ${exhibition.initiator.position ? `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">职务:</span>
                                    <span class="text-gray-800 ml-2">${exhibition.initiator.position}</span>
                                </div>
                            ` : ''}
                            ${exhibition.initiator.email ? `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">邮箱:</span>
                                    <span class="text-gray-800 ml-2">${exhibition.initiator.email}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
                
                ${exhibition.description ? `
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">书展介绍</h4>
                        <p class="text-gray-800 whitespace-pre-line">${exhibition.description}</p>
                    </div>
                ` : ''}
                
                ${exhibition.requires_campus_registration ? `
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">进校报备信息</h4>
                        ${exhibition.registration_requirements ? `
                            <div class="mb-3">
                                <p class="text-gray-800 whitespace-pre-line">${exhibition.registration_requirements}</p>
                            </div>
                        ` : ''}
                        ${exhibition.registration_qrcode ? `
                            <div class="flex justify-center">
                                <img src="${exhibition.registration_qrcode}" alt="报备二维码" class="max-h-48 object-contain border rounded p-2">
                            </div>
                        ` : ''}
                    </div>
                ` : ''}
                
                ${exhibition.requirements ? `
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">其他要求</h4>
                        <p class="text-gray-800 whitespace-pre-line">${exhibition.requirements}</p>
                    </div>
                ` : ''}
            </div>`;
        
        // 如果是发起人，显示报名信息
        if (exhibition.is_initiator && exhibition.registrations && exhibition.registrations.length > 0) {
            html += `
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-medium text-gray-700 mb-3">参展单位 (${exhibition.registrations.length})</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead>
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位名称</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参展人数</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报名时间</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
            `;
            
            exhibition.registrations.forEach(reg => {
                html += `
                    <tr>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${reg.company_name || '-'}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${reg.participants_count || 0}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm">
                            <span class="inline-flex px-2 py-1 text-xs rounded-full ${reg.status === 'registered' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                ${reg.status === 'registered' ? '已报名' : '已取消'}
                            </span>
                        </td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${reg.created_at || '-'}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm">
                            <button class="view-participants-btn text-blue-600 hover:text-blue-800" data-id="${reg.id}">
                                <i class="fas fa-users mr-1"></i>查看人员
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }
        
        html += `</div>`;
        
        // 设置详情内容
        $('#detailModalBody').html(html);
        
        // 设置操作按钮
        let actionBtnsHtml = '';
        
        if (exhibition.is_initiator) {
            if (exhibition.status === 'draft') {
                actionBtnsHtml += `
                    <button id="publishExhibitionBtn" class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600" data-id="${exhibition.id}">
                        <i class="fas fa-check-circle mr-1"></i>发布书展
                    </button>
                    <button id="editExhibitionBtn" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600" data-id="${exhibition.id}">
                        <i class="fas fa-edit mr-1"></i>编辑
                    </button>
                    <button id="cancelExhibitionBtn" class="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600" data-id="${exhibition.id}">
                        <i class="fas fa-times-circle mr-1"></i>取消书展
                    </button>
                `;
            } else if (exhibition.status === 'published') {
                actionBtnsHtml += `
                    <button id="endExhibitionBtn" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600" data-id="${exhibition.id}">
                        <i class="fas fa-flag-checkered mr-1"></i>结束书展
                    </button>
                    <button id="cancelExhibitionBtn" class="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600" data-id="${exhibition.id}">
                        <i class="fas fa-times-circle mr-1"></i>取消书展
                    </button>
                `;
            } else if (exhibition.status === 'cancelled') {
                actionBtnsHtml += `
                    <button id="createFromTemplateBtn" class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600" data-id="${exhibition.id}">
                        <i class="fas fa-copy mr-1"></i>作为模板创建
                    </button>
                `;
            }
        }
        
        $('#detailModalActionBtns').html(actionBtnsHtml);
        
        // 绑定查看参展人员按钮点击事件
        $('.view-participants-btn').click(function() {
            const regId = $(this).data('id');
            viewParticipants(regId);
        });
        
        // 绑定发布书展按钮点击事件
        $('#publishExhibitionBtn').click(function() {
            const id = $(this).data('id');
            showConfirmModal('确认发布', '发布后将通知相关单位并接受报名，是否继续？', function() {
                changeExhibitionStatus(id, 'published');
            });
        });
        
        // 绑定结束书展按钮点击事件
        $('#endExhibitionBtn').click(function() {
            const id = $(this).data('id');
            showConfirmModal('确认结束', '结束后将不再接受新的报名，是否继续？', function() {
                changeExhibitionStatus(id, 'ended');
            });
        });
        
        // 绑定取消书展按钮点击事件
        $('#cancelExhibitionBtn').click(function() {
            const id = $(this).data('id');
            showConfirmModal('确认取消', '取消后将通知已报名单位，是否继续？', function() {
                changeExhibitionStatus(id, 'cancelled');
            });
        });
        
        // 绑定编辑按钮点击事件
        $('#editExhibitionBtn').click(function() {
            const id = $(this).data('id');
            // 关闭详情模态框
            $('#detailModalContainer').addClass('hidden');
            // 编辑书展
            editExhibition(id);
        });
        
        // 绑定作为模板创建按钮点击事件
        $('#createFromTemplateBtn').click(function() {
            const id = $(this).data('id');
            // 关闭详情模态框
            $('#detailModalContainer').addClass('hidden');
            // 使用当前书展数据创建新书展
            createFromTemplate(id);
        });
    }

    // 查看参展人员
    function viewParticipants(registrationId) {
        $.ajax({
            url: '/api/teacher/get_exhibition_participants',
            type: 'GET',
            data: { registration_id: registrationId },
            success: function(response) {
                if (response.code === 0) {
                    renderParticipants(response.data);
                    $('#participantsModalContainer').removeClass('hidden');
                } else {
                    showMessage(response.message || '获取参展人员失败', 'error');
                }
            },
            error: function() {
                showMessage('网络错误，请稍后再试', 'error');
            }
        });
    }

    // 渲染参展人员
    function renderParticipants(participants) {
        if (!participants || participants.length === 0) {
            $('#participantsModalBody').html(`
                <div class="text-center py-4 text-gray-500">
                    <i class="fas fa-users text-2xl mb-2"></i>
                    <p>暂无参展人员信息</p>
                </div>
            `);
            return;
        }
        
        let html = `
            <div class="mb-4">
                <h4 class="font-medium text-gray-700">单位信息</h4>
                <div class="mt-2">
                    <span class="text-gray-500">单位名称:</span>
                    <span class="text-gray-800 ml-2">${participants[0].company_name || '-'}</span>
                </div>
            </div>
            
            <h4 class="font-medium text-gray-700 mb-3">参展人员列表</h4>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">手机号</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否联系人</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
        `;
        
        participants.forEach(participant => {
            html += `
                <tr>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${participant.name}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${participant.phone}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">${participant.role || '-'}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">
                        <span class="inline-flex px-2 py-1 text-xs rounded-full ${participant.is_contact ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}">
                            ${participant.is_contact ? '是' : '否'}
                        </span>
                    </td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                </table>
            </div>
        `;
        
        $('#participantsModalBody').html(html);
    }

    // 编辑书展
    function editExhibition(id) {
        // 重置表单
        resetExhibitionForm();
        
        // 设置标题
        $('#editModalTitle').text('编辑书展活动');
        
        // 获取书展详情
        $.ajax({
            url: '/api/teacher/get_exhibition_detail',
            type: 'GET',
            data: { id: id },
            success: function(response) {
                if (response.code === 0) {
                    const exhibition = response.data;
                    
                    // 检查书展状态，已取消和已结束的书展不能编辑
                    if (exhibition.status === 'cancelled') {
                        showMessage('已取消的活动不能编辑，可使用"作为模板创建"创建新活动', 'error');
                        return;
                    }
                    
                    if (exhibition.status === 'ended') {
                        showMessage('已结束的活动不能编辑', 'error');
                        return;
                    }
                    
                    fillExhibitionForm(exhibition);
                    $('#editModalContainer').removeClass('hidden');
                } else {
                    showMessage(response.message || '获取书展详情失败', 'error');
                }
            },
            error: function() {
                showMessage('网络错误，请稍后再试', 'error');
            }
        });
    }

    // 填充书展表单
    function fillExhibitionForm(exhibition) {
        // 设置隐藏字段
        $('#exhibitionId').val(exhibition.id);
        
        // 设置基本信息
        $('#title').val(exhibition.title);
        $('#school').val(exhibition.school_name);
        $('#description').val(exhibition.description);
        
        // 设置时间信息
        if (exhibition.start_time) {
            const startTime = new Date(exhibition.start_time.replace(' ', 'T'));
            $('#startTime').val(formatDateTime(startTime));
        }
        
        if (exhibition.end_time) {
            const endTime = new Date(exhibition.end_time.replace(' ', 'T'));
            $('#endTime').val(formatDateTime(endTime));
        }
        
        if (exhibition.registration_deadline) {
            const deadline = new Date(exhibition.registration_deadline.replace(' ', 'T'));
            $('#registrationDeadline').val(formatDateTime(deadline));
        }
        
        // 设置地点信息
        $('#location').val(exhibition.location);
        $('#schoolAddress').val(exhibition.school_address || '');
        
        // 设置进校报备信息
        $('#requiresRegistration').prop('checked', exhibition.requires_campus_registration === 1);
        $('#registrationRequirements').val(exhibition.registration_requirements);
        
        if (exhibition.requires_campus_registration === 1) {
            $('#registrationRequirementsContainer').removeClass('hidden');
        } else {
            $('#registrationRequirementsContainer').addClass('hidden');
        }
        
        // 设置停车选项
        $('#allowsParking').prop('checked', exhibition.allows_parking === 1);
        
        // 设置其他要求
        $('#requirements').val(exhibition.requirements);
        
        // 设置发起人信息
        if (exhibition.initiator) {
            $('#initiatorName').val(exhibition.initiator.name);
            $('#initiatorPhone').val(exhibition.initiator.phone);
            $('#initiatorDepartment').val(exhibition.initiator.department);
            $('#initiatorPosition').val(exhibition.initiator.position);
            $('#initiatorEmail').val(exhibition.initiator.email);
        }
        
        // 设置Logo预览
        if (exhibition.logo_url) {
            $('#logoPreview').attr('src', exhibition.logo_url);
            $('#logoPreviewContainer').removeClass('hidden');
            $('#logoFileName').text('当前已有Logo');
        } else {
            $('#logoPreviewContainer').addClass('hidden');
            $('#logoFileName').text('选择文件上传');
        }
        
        // 设置二维码预览
        if (exhibition.registration_qrcode) {
            $('#qrcodePreview').attr('src', exhibition.registration_qrcode);
            $('#qrcodePreviewContainer').removeClass('hidden');
            $('#qrcodeFileName').text('当前已有二维码');
        } else {
            $('#qrcodePreviewContainer').addClass('hidden');
            $('#qrcodeFileName').text('选择文件上传');
        }
    }

    // 保存书展
    function saveExhibition(status) {
        // 验证表单
        if (!validateExhibitionForm()) {
            return;
        }
        
        // 创建FormData对象
        const formData = new FormData($('#exhibitionForm')[0]);
        
        // 添加状态
        formData.append('status', status);
        
        // 发送请求
        $.ajax({
            url: '/api/teacher/save_exhibition',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.code === 0) {
                    showMessage(response.message || '保存成功');
                    // 隐藏模态框
                    $('#editModalContainer').addClass('hidden');
                    // 重新加载书展列表
                    loadExhibitions();
                } else {
                    showMessage(response.message || '保存失败', 'error');
                }
            },
            error: function() {
                showMessage('网络错误，请稍后再试', 'error');
            }
        });
    }

    // 验证书展表单
    function validateExhibitionForm() {
        // 验证必填字段
        const requiredFields = [
            { id: 'title', name: '书展主题' },
            { id: 'startTime', name: '开始时间' },
            { id: 'endTime', name: '结束时间' },
            { id: 'registrationDeadline', name: '报名截止时间' },
            { id: 'location', name: '具体地点' },
            { id: 'schoolAddress', name: '学校地址' },
            { id: 'initiatorName', name: '发起人姓名' },
            { id: 'initiatorPhone', name: '发起人手机号' }
        ];
        
        for (const field of requiredFields) {
            if (!$(`#${field.id}`).val()) {
                showMessage(`请填写${field.name}`, 'error');
                return false;
            }
        }
        
        // 验证时间逻辑
        const startTime = new Date($('#startTime').val());
        const endTime = new Date($('#endTime').val());
        const deadline = new Date($('#registrationDeadline').val());
        
        if (endTime <= startTime) {
            showMessage('结束时间必须晚于开始时间', 'error');
            return false;
        }
        
        if (deadline >= startTime) {
            showMessage('报名截止时间必须早于开始时间', 'error');
            return false;
        }
        
        // 验证手机号
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test($('#initiatorPhone').val())) {
            showMessage('请输入正确的手机号', 'error');
            return false;
        }
        
        // 验证邮箱（如果有填写）
        const email = $('#initiatorEmail').val();
        if (email && email.trim() !== '') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('请输入正确的邮箱地址', 'error');
                return false;
            }
        }
        
        return true;
    }

    // 更改书展状态
    function changeExhibitionStatus(id, status) {
        $.ajax({
            url: '/api/teacher/change_exhibition_status',
            type: 'POST',
            data: { id, status },
            success: function(response) {
                if (response.code === 0) {
                    showMessage(response.message || '状态更新成功');
                    // 隐藏确认模态框和详情模态框
                    $('#confirmModalContainer').addClass('hidden');
                    $('#detailModalContainer').addClass('hidden');
                    // 重新加载书展列表
                    loadExhibitions();
                } else {
                    showMessage(response.message || '状态更新失败', 'error');
                    // 隐藏确认模态框
                    $('#confirmModalContainer').addClass('hidden');
                }
            },
            error: function() {
                showMessage('网络错误，请稍后再试', 'error');
                // 隐藏确认模态框
                $('#confirmModalContainer').addClass('hidden');
            }
        });
    }

    // 显示确认模态框
    function showConfirmModal(title, message, callback) {
        // 设置标题和内容
        $('#confirmModalTitle').text(title);
        $('#confirmModalBody').html(`<p class="text-gray-700">${message}</p>`);
        
        // 绑定确认按钮点击事件
        $('#confirmBtn').off('click').on('click', callback);
        
        // 显示模态框
        $('#confirmModalContainer').removeClass('hidden');
    }

    // 格式化日期时间
    function formatDateTime(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }
    
    // 获取报名状态信息
    function getRegistrationStatus(exhibition) {
        // 如果书展状态不是已发布，则不能报名
        if (exhibition.status !== 'published') {
            return { text: '报名已关闭', class: 'status-registration-closed' };
        }
        
        // 获取当前时间和报名截止时间
        const now = new Date();
        const deadline = new Date(exhibition.registration_deadline.replace(' ', 'T'));
        
        // 如果当前时间已经超过报名截止时间，则报名已截止
        if (now > deadline) {
            return { text: '报名已截止', class: 'status-registration-closed' };
        }
        
        // 否则可以报名
        return { text: '可报名', class: 'status-registerable' };
    }
    
    // 使用已有书展作为模板创建新书展
    function createFromTemplate(id) {
        $.ajax({
            url: '/api/teacher/get_exhibition_detail',
            type: 'GET',
            data: { id: id },
            success: function(response) {
                if (response.code === 0) {
                    // 重置表单
                    resetExhibitionForm();
                    
                    // 设置标题
                    $('#editModalTitle').text('创建新书展（基于模板）');
                    
                    const exhibition = response.data;
                    
                    // 设置基本信息，但修改标题以表明是复制的
                    $('#title').val(exhibition.title + '（复制）');
                    $('#school').val(exhibition.school_name);
                    $('#description').val(exhibition.description);
                    
                    // 设置地点信息
                    $('#location').val(exhibition.location);
                    $('#schoolAddress').val(exhibition.school_address || '');
                    
                    // 设置进校报备信息
                    $('#requiresRegistration').prop('checked', exhibition.requires_campus_registration === 1);
                    $('#registrationRequirements').val(exhibition.registration_requirements);
                    
                    if (exhibition.requires_campus_registration === 1) {
                        $('#registrationRequirementsContainer').removeClass('hidden');
                    } else {
                        $('#registrationRequirementsContainer').addClass('hidden');
                    }
                    
                    // 设置停车选项
                    $('#allowsParking').prop('checked', exhibition.allows_parking === 1);
                    
                    // 设置其他要求
                    $('#requirements').val(exhibition.requirements);
                    
                    // 设置发起人信息
                    if (exhibition.initiator) {
                        $('#initiatorName').val(exhibition.initiator.name);
                        $('#initiatorPhone').val(exhibition.initiator.phone);
                        $('#initiatorDepartment').val(exhibition.initiator.department);
                        $('#initiatorPosition').val(exhibition.initiator.position);
                        $('#initiatorEmail').val(exhibition.initiator.email);
                    }
                    
                    // 显示模态框
                    $('#editModalContainer').removeClass('hidden');
                } else {
                    showMessage(response.message || '获取书展详情失败', 'error');
                }
            },
            error: function() {
                showMessage('网络错误，请稍后再试', 'error');
            }
        });
    }
    </script>
</body>
</html> 