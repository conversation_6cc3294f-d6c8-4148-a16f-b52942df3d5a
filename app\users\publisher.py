import os
import uuid
import pandas as pd
import tempfile
import numpy as np
from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for, current_app,send_file
from app.config import get_db_connection
from datetime import datetime, timedelta
import xlsxwriter
import pymysql
import json
import zipfile
from datetime import datetime
import pandas as pd
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl import Workbook
import re
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
from io import BytesIO
# 导入邮件通知相关函数
from app.services.sample_notification_service import SampleNotificationService, notify_teacher_request_result, notify_teacher_tracking_update
from werkzeug.utils import secure_filename
##将上传的样书封面图片保存到当前文件所在目录的上层目录的static/upload/sample_covers目录下
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'static', 'upload', 'sample_covers')

publisher_bp = Blueprint('publisher', __name__)

# 样书特色相关辅助函数
def insert_sample_features(cursor, sample_id, feature_ids):
    """插入样书特色关联数据"""
    if not feature_ids:
        return
    
    # 删除旧的关联
    delete_sql = "DELETE FROM sample_book_features WHERE sample_id = %s"
    cursor.execute(delete_sql, (sample_id,))
    
    # 插入新的关联
    for feature_id in feature_ids:
        insert_sql = "INSERT INTO sample_book_features (sample_id, feature_id) VALUES (%s, %s)"
        cursor.execute(insert_sql, (sample_id, feature_id))

def get_sample_features(cursor, sample_id):
    """获取样书的特色ID列表"""
    sql = """
        SELECT feature_id, bf.name as feature_name
        FROM sample_book_features sbf
        JOIN book_features bf ON sbf.feature_id = bf.id
        WHERE sbf.sample_id = %s
        ORDER BY bf.id
    """
    cursor.execute(sql, (sample_id,))
    results = cursor.fetchall()
    return {
        'feature_ids': [row['feature_id'] for row in results],
        'feature_names': [row['feature_name'] for row in results]
    }

def get_samples_with_features(cursor, base_sql, params):
    """获取样书列表并附加特色信息"""
    cursor.execute(base_sql, params)
    samples = cursor.fetchall()

    for sample in samples:
        sample_id = sample['id']
        features_info = get_sample_features(cursor, sample_id)
        sample['feature_ids'] = features_info['feature_ids']
        sample['feature_names'] = features_info['feature_names']
        # 保持向后兼容
        sample['feature_name'] = ', '.join(features_info['feature_names']) if features_info['feature_names'] else None

        # 格式化日期字段
        if sample.get('publication_date'):
            sample['publication_date'] = sample['publication_date'].strftime('%Y-%m-%d')

    return samples

# 获取待处理的样书申请
@publisher_bp.route('/get_pending_requests', methods=['GET'])
def get_pending_requests():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    search = request.args.get('search', '')
    date_filter = request.args.get('date_filter', 'all')
    
    offset = (page - 1) * limit
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建日期筛选条件
            date_condition = ""
            if date_filter == 'today':
                date_condition = "AND DATE(sr.request_date) = CURDATE()"
            elif date_filter == 'week':
                date_condition = "AND YEARWEEK(sr.request_date, 1) = YEARWEEK(CURDATE(), 1)"
            elif date_filter == 'month':
                date_condition = "AND YEAR(sr.request_date) = YEAR(CURDATE()) AND MONTH(sr.request_date) = MONTH(CURDATE())"
            
            # 查询待处理申请的订单编号和基本信息
            sql = f"""
                SELECT DISTINCT sr.order_number, u.name as teacher_name, u.user_id as teacher_id, 
                       s.name as teacher_institution, u.phone_number as teacher_phone,
                       MIN(sr.request_date) as request_date, 
                       SUM(sr.quantity) as book_count,
                       MAX(sr.request_reason) as request_reason,
                       MAX(sr.purpose) as purpose,
                       MAX(sr.address_id) as address_id,
                       MAX(tc.course_name) as course_name,
                       MAX(tc.semester) as semester
                FROM sample_requests sr
                JOIN users u ON sr.teacher_id = u.user_id
                LEFT JOIN schools s ON u.teacher_school_id = s.id
                JOIN sample_books sb ON sr.textbook_id = sb.id
                LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                WHERE sb.publisher_id = %s 
                AND sr.status = 'pending'
                AND sr.order_number IS NOT NULL
                AND (
                    sb.name LIKE %s OR 
                    u.name LIKE %s OR 
                    sb.isbn LIKE %s OR
                    sr.order_number LIKE %s
                )
                {date_condition}
                GROUP BY sr.order_number, u.name, u.user_id, s.name, u.phone_number
                ORDER BY MIN(sr.request_date) DESC
                LIMIT %s OFFSET %s
            """
            
            cursor.execute(sql, (publisher_id, f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%', limit, offset))
            orders = cursor.fetchall()
            
            # 获取总订单数
            count_sql = f"""
                SELECT COUNT(DISTINCT sr.order_number) as total
                FROM sample_requests sr
                JOIN users u ON sr.teacher_id = u.user_id
                JOIN sample_books sb ON sr.textbook_id = sb.id
                WHERE sb.publisher_id = %s 
                AND sr.status = 'pending'
                AND sr.order_number IS NOT NULL
                AND (
                    sb.name LIKE %s OR 
                    u.name LIKE %s OR 
                    sb.isbn LIKE %s OR
                    sr.order_number LIKE %s
                )
                {date_condition}
            """
            
            cursor.execute(count_sql, (publisher_id, f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'))
            total = cursor.fetchone()['total']
            
            # 获取每个订单的书籍详情
            result = []
            for order in orders:
                order_number = order['order_number']
                
                # 获取该订单下的所有样书
                books_sql = """
                    SELECT sr.request_id, sb.name as book_name, sb.isbn, sb.author, sr.quantity,
                           tc.course_name, tc.semester, sr.request_reason as book_remarks
                    FROM sample_requests sr
                    JOIN sample_books sb ON sr.textbook_id = sb.id
                    LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                    WHERE sr.order_number = %s AND sb.publisher_id = %s
                    ORDER BY sr.request_id
                """
                cursor.execute(books_sql, (order_number, publisher_id))
                books = cursor.fetchall()
                
                # 构建完整的订单信息
                order_info = {
                    'order_number': order_number,
                    'teacher_name': order['teacher_name'],
                    'teacher_id': order['teacher_id'],
                    'teacher_institution': order['teacher_institution'],
                    'teacher_phone': order['teacher_phone'],
                    'request_date': order['request_date'].strftime('%Y-%m-%d %H:%M:%S') if order['request_date'] else '',
                    'book_count': order['book_count'],
                    'request_reason': order['request_reason'],
                    'purpose': order['purpose'],
                    'address_id': order['address_id'],
                    'books': books,
                    'course_name': order['course_name'] or '未指定',
                    'semester': order['semester'] or '未指定'
                }
                
                result.append(order_info)
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": result,
                "total": total
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取申请失败: {str(e)}"})
    finally:
        connection.close()

# 获取已处理的样书申请
@publisher_bp.route('/get_processed_requests', methods=['GET'])
def get_processed_requests():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    search = request.args.get('search', '')
    date_filter = request.args.get('date_filter', 'all')
    status_filter = request.args.get('status_filter', 'all')  # 新增参数
    
    offset = (page - 1) * limit
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建日期筛选条件
            date_condition = ""
            if date_filter == 'today':
                date_condition = "AND DATE(sr.request_date) = CURDATE()"
            elif date_filter == 'week':
                date_condition = "AND YEARWEEK(sr.request_date, 1) = YEARWEEK(CURDATE(), 1)"
            elif date_filter == 'month':
                date_condition = "AND YEAR(sr.request_date) = YEAR(CURDATE()) AND MONTH(sr.request_date) = MONTH(CURDATE())"
            
            # 构建状态筛选条件
            status_condition = "AND sr.status IN ('approved', 'rejected')"
            if status_filter == 'approved_not_shipped':
                status_condition = "AND sr.status = 'approved' AND (sr.tracking_number IS NULL OR sr.tracking_number = '')"
            elif status_filter == 'shipped':
                status_condition = "AND sr.status = 'approved' AND sr.tracking_number IS NOT NULL AND sr.tracking_number != ''"
            elif status_filter == 'rejected':
                status_condition = "AND sr.status = 'rejected'"
            
            # 查询已处理申请的订单编号和基本信息
            sql = f"""
                SELECT DISTINCT sr.order_number, u.name as teacher_name, u.user_id as teacher_id, 
                       s.name as teacher_institution, u.phone_number as teacher_phone,
                       MIN(sr.request_date) as request_date, MIN(sr.approval_date) as approval_date, MAX(sr.shipping_date) as shipping_date,
                       MAX(sr.status) as status, MAX(sr.tracking_number) as tracking_number, MAX(sr.shipping_company) as shipping_company,
                       SUM(sr.quantity) as book_count,
                       MAX(sr.request_reason) as request_reason,
                       MAX(sr.purpose) as purpose,
                       MAX(sr.address_id) as address_id,
                       MAX(sr.reject_reason) as reject_reason,
                       MAX(tc.course_name) as course_name,
                       MAX(tc.semester) as semester
                FROM sample_requests sr
                JOIN users u ON sr.teacher_id = u.user_id
                LEFT JOIN schools s ON u.teacher_school_id = s.id
                JOIN sample_books sb ON sr.textbook_id = sb.id
                LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                WHERE sb.publisher_id = %s 
                {status_condition}
                AND sr.order_number IS NOT NULL
                AND (
                    sb.name LIKE %s OR 
                    u.name LIKE %s OR 
                    sb.isbn LIKE %s OR
                    sr.order_number LIKE %s
                )
                {date_condition}
                GROUP BY sr.order_number, u.name, u.user_id, s.name, u.phone_number
                ORDER BY 
                    CASE 
                        WHEN MAX(sr.status) = 'approved' AND (MAX(sr.tracking_number) IS NULL OR MAX(sr.tracking_number) = '') THEN 1
                        WHEN MAX(sr.status) = 'approved' AND MAX(sr.tracking_number) IS NOT NULL AND MAX(sr.tracking_number) != '' THEN 2
                        WHEN MAX(sr.status) = 'rejected' THEN 3
                    END,
                    MIN(sr.approval_date) DESC, MIN(sr.request_date) DESC
                LIMIT %s OFFSET %s
            """
            
            cursor.execute(sql, (publisher_id, f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%', limit, offset))
            orders = cursor.fetchall()
            
            # 获取总订单数
            count_sql = f"""
                SELECT COUNT(DISTINCT sr.order_number) as total
                FROM sample_requests sr
                JOIN users u ON sr.teacher_id = u.user_id
                JOIN sample_books sb ON sr.textbook_id = sb.id
                WHERE sb.publisher_id = %s 
                {status_condition}
                AND sr.order_number IS NOT NULL
                AND (
                    sb.name LIKE %s OR 
                    u.name LIKE %s OR 
                    sb.isbn LIKE %s OR
                    sr.order_number LIKE %s
                )
                {date_condition}
            """
            
            cursor.execute(count_sql, (publisher_id, f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'))
            total = cursor.fetchone()['total']
            
            # 获取每个订单的书籍详情
            result = []
            for order in orders:
                order_number = order['order_number']
                
                # 获取该订单下的所有样书
                books_sql = """
                    SELECT sr.request_id, sb.name as book_name, sb.isbn, sb.author, sr.quantity,
                           tc.course_name, tc.semester, sr.request_reason as book_remarks
                    FROM sample_requests sr
                    JOIN sample_books sb ON sr.textbook_id = sb.id
                    LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                    WHERE sr.order_number = %s AND sb.publisher_id = %s
                    ORDER BY sr.request_id
                """
                cursor.execute(books_sql, (order_number, publisher_id))
                books = cursor.fetchall()
                
                # 构建完整的订单信息
                order_info = {
                    'order_number': order_number,
                    'teacher_name': order['teacher_name'],
                    'teacher_id': order['teacher_id'],
                    'teacher_institution': order['teacher_institution'],
                    'teacher_phone': order['teacher_phone'],
                    'request_date': order['request_date'].strftime('%Y-%m-%d %H:%M:%S') if order['request_date'] else '',
                    'approval_date': order['approval_date'].strftime('%Y-%m-%d %H:%M:%S') if order['approval_date'] else '',
                    'shipping_date': order['shipping_date'].strftime('%Y-%m-%d %H:%M:%S') if order['shipping_date'] else '',
                    'status': order['status'],
                    'tracking_number': order['tracking_number'],
                    'shipping_company': order['shipping_company'],
                    'book_count': order['book_count'],
                    'request_reason': order['request_reason'],
                    'purpose': order['purpose'],
                    'address_id': order['address_id'],
                    'reject_reason': order['reject_reason'],
                    'books': books,
                    'course_name': order['course_name'] or '未指定',
                    'semester': order['semester'] or '未指定'
                }
                
                result.append(order_info)
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": result,
                "total": total
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取申请失败: {str(e)}"})
    finally:
        connection.close()

# 获取申请的收货地址
@publisher_bp.route('/get_request_address', methods=['GET'])
def get_request_address():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    request_id = request.args.get('request_id')
    
    if not request_id:
        return jsonify({"code": 1, "message": "请求ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证申请是否属于该出版社
            sql = """
                SELECT sa.* 
                FROM shipping_addresses sa
                JOIN sample_requests sr ON sa.address_id = sr.address_id
                JOIN sample_books sb ON sr.textbook_id = sb.id
                WHERE sr.request_id = %s AND sb.publisher_id = %s
            """
            
            cursor.execute(sql, (request_id, publisher_id))
            address = cursor.fetchone()
            
            if not address:
                return jsonify({"code": 1, "message": "未找到地址信息或无权访问"})
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": address
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取地址失败: {str(e)}"})
    finally:
        connection.close()


# 导出样书申请数据 - 新版本
@publisher_bp.route('/export_sample_requests', methods=['GET'])
def export_sample_requests():
    """导出样书申请记录 - 增强版"""
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    try:
        publisher_id = session['user_id']
        export_type = request.args.get('type', 'all')  # all, pending, processed
        status_filter = request.args.get('status_filter', 'all')  # all, approved_not_shipped, shipped, rejected
        date_filter = request.args.get('date_filter', 'all')
        search = request.args.get('search', '')
        
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 获取出版社公司信息
                cursor.execute("""
                    SELECT pc.name as company_name 
                    FROM users u 
                    JOIN publisher_companies pc ON u.publisher_company_id = pc.id 
                    WHERE u.user_id = %s
                """, (publisher_id,))
                publisher_info = cursor.fetchone()
                company_name = publisher_info['company_name'] if publisher_info else '未知出版社'
                
                # 构建查询条件
                conditions = ["sb.publisher_id = %s"]
                params = [publisher_id]
                
                # 状态筛选
                if export_type == 'pending':
                    conditions.append("sr.status = 'pending'")
                elif export_type == 'processed':
                    conditions.append("sr.status IN ('approved', 'rejected')")
                    
                    if status_filter == 'approved_not_shipped':
                        conditions.append("sr.status = 'approved' AND (sr.tracking_number IS NULL OR sr.tracking_number = '')")
                    elif status_filter == 'shipped':
                        conditions.append("sr.status = 'approved' AND sr.tracking_number IS NOT NULL AND sr.tracking_number != ''")
                    elif status_filter == 'rejected':
                        conditions.append("sr.status = 'rejected'")
                
                # 日期筛选
                if date_filter == 'today':
                    conditions.append("DATE(sr.request_date) = CURDATE()")
                elif date_filter == 'week':
                    conditions.append("YEARWEEK(sr.request_date, 1) = YEARWEEK(CURDATE(), 1)")
                elif date_filter == 'month':
                    conditions.append("YEAR(sr.request_date) = YEAR(CURDATE()) AND MONTH(sr.request_date) = MONTH(CURDATE())")
                
                # 搜索筛选
                if search:
                    search_condition = """(
                        sb.name LIKE %s OR 
                        sb.isbn LIKE %s OR 
                        u.name LIKE %s OR 
                        sr.order_number LIKE %s OR
                        sa.name LIKE %s
                    )"""
                    conditions.append(search_condition)
                    search_param = f'%{search}%'
                    params.extend([search_param] * 5)
                
                # 构建完整查询
                where_clause = " AND ".join(conditions)
                
                sql = f"""
                    SELECT 
                        sr.request_id,
                        sr.order_number,
                        sr.request_date,
                        sr.approval_date,
                        sr.shipping_date,
                        sr.status,
                        sr.tracking_number,
                        sr.shipping_company,
                        sr.quantity,
                        sr.purpose,
                        sr.request_reason,
                        sr.reject_reason,
                        
                        -- 教师信息
                        u.name as teacher_name,
                        u.phone_number as teacher_phone,
                        u.contact_info as teacher_contact,
                        s.name as teacher_school,
                        
                        -- 样书信息
                        sb.name as book_name,
                        sb.isbn,
                        sb.author,
                        sb.price,
                        
                        -- 课程信息
                        tc.course_name,
                        tc.semester,
                        tc.course_type,
                        tc.student_count,
                        
                        -- 收货地址信息
                        sa.name as recipient_name,
                        sa.phone_number as recipient_phone,
                        sa.province,
                        sa.city,
                        sa.district,
                        sa.detailed_address
                        
                    FROM sample_requests sr
                    JOIN sample_books sb ON sr.textbook_id = sb.id
                    JOIN users u ON sr.teacher_id = u.user_id
                    LEFT JOIN schools s ON u.teacher_school_id = s.id
                    LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                    LEFT JOIN shipping_addresses sa ON sr.address_id = sa.address_id
                    WHERE {where_clause}
                    ORDER BY sr.order_number, 
                        CASE 
                            WHEN sr.status = 'pending' THEN 1
                            WHEN sr.status = 'approved' THEN 2
                            WHEN sr.status = 'rejected' THEN 3
                            ELSE 4
                        END,
                        sr.request_date DESC
                """
                
                cursor.execute(sql, params)
                requests = cursor.fetchall()
                
                if not requests:
                    return jsonify({"code": 1, "message": "暂无数据可导出"})
                
                # 数据处理
                data = []
                for req in requests:
                    # 格式化日期
                    request_date = req['request_date'].strftime('%Y-%m-%d %H:%M:%S') if req['request_date'] else ''
                    approval_date = req['approval_date'].strftime('%Y-%m-%d %H:%M:%S') if req['approval_date'] else ''
                    shipping_date = req['shipping_date'].strftime('%Y-%m-%d %H:%M:%S') if req['shipping_date'] else ''
                    
                    # 格式化状态
                    status_map = {
                        'pending': '待审核',
                        'approved': '已通过',
                        'rejected': '已拒绝'
                    }
                    status_text = status_map.get(req['status'], req['status'])
                    
                    # 判断是否已发货
                    if req['status'] == 'approved' and req['tracking_number']:
                        status_text = '已发货'
                    
                    # 构建完整地址
                    address_parts = [req['province'], req['city'], req['district'], req['detailed_address']]
                    full_address = ''.join(filter(None, address_parts))
                    
                    data.append({
                        '订单号': req['order_number'],
                        '申请日期': request_date,
                        '处理日期': approval_date,
                        '发货日期': shipping_date,
                        '状态': status_text,
                        
                        # 教师信息
                        '申请教师': req['teacher_name'] or '',
                        '教师电话': req['teacher_phone'] or '',
                        '所在学校': req['teacher_school'] or '',
                        
                        # 样书信息  
                        '样书名称': req['book_name'] or '',
                        'ISBN': req['isbn'] or '',
                        '作者': req['author'] or '',
                        '定价': f"¥{req['price']}" if req['price'] else '',
                        '申请数量': req['quantity'] or 1,
                        '申请用途': req['purpose'] or '',
                        '申请说明': req['request_reason'] or '',
                        
                        # 课程信息
                        '主讲课程': req['course_name'] or '',
                        '开课学期': req['semester'] or '',
                        '课程类型': req['course_type'] or '',
                        '选课人数': req['student_count'] or '',
                        
                        # 收货信息
                        '收件人': req['recipient_name'] or '',
                        '收件电话': req['recipient_phone'] or '',
                        '收货地址': full_address,
                        
                        # 物流信息
                        '快递公司': req['shipping_company'] or '',
                        '快递单号': req['tracking_number'] or '',
                        '拒绝原因': req['reject_reason'] or ''
                    })
                
                # 创建Excel文件
                df = pd.DataFrame(data)
                
                # 创建临时文件
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
                
                # 写入Excel
                with pd.ExcelWriter(temp_file.name, engine='xlsxwriter') as writer:
                    df.to_excel(writer, sheet_name='样书申请记录', index=False)
                    
                    # 获取xlsxwriter对象进行格式化
                    workbook = writer.book
                    worksheet = writer.sheets['样书申请记录']
                    
                    # 设置列宽 - 根据内容类型设置不同的列宽
                    column_widths = {
                        '订单号': 15,
                        '申请日期': 20,
                        '处理日期': 20,
                        '发货日期': 20,
                        '状态': 10,
                        '申请教师': 12,
                        '教师电话': 15,
                        '所在学校': 25,
                        '样书名称': 30,
                        'ISBN': 15,
                        '作者': 20,
                        '定价': 10,
                        '申请数量': 10,
                        '申请用途': 15,
                        '申请说明': 25,
                        '主讲课程': 20,
                        '开课学期': 10,
                        '课程类型': 10,
                        '选课人数': 10,
                        '收件人': 12,
                        '收件电话': 15,
                        '收货地址': 40,
                        '快递公司': 12,
                        '快递单号': 20,
                        '拒绝原因': 25
                    }
                    
                    for i, col in enumerate(df.columns):
                        # 使用预设的列宽，如果没有则动态计算
                        if col in column_widths:
                            worksheet.set_column(i, i, column_widths[col])
                        else:
                            max_len = max(
                                df[col].astype(str).map(len).max() if len(df) > 0 else 0,
                                len(col)
                            ) + 2
                            # 限制最大列宽
                            max_len = min(max_len, 30)
                            worksheet.set_column(i, i, max_len)
                    
                    # 添加表头格式
                    header_format = workbook.add_format({
                        'bold': True,
                        'text_wrap': True,
                        'valign': 'top',
                        'fg_color': '#D7E4BC',
                        'border': 1
                    })
                    
                    # 应用表头格式
                    for col_num, value in enumerate(df.columns.values):
                        worksheet.write(0, col_num, value, header_format)
                
                # 生成文件名
                current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
                type_text = ''
                if export_type == 'pending':
                    type_text = '待处理'
                elif export_type == 'processed':
                    type_text = '已处理'
                else:
                    type_text = '全部'
                
                filename = f"{company_name}_样书申请记录_{type_text}_{current_time}.xlsx"
                
                return send_file(
                    temp_file.name,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
        except Exception as e:
            return jsonify({"code": 1, "message": f"导出失败: {str(e)}"})
        finally:
            connection.close()
            
    except Exception as e:
        return jsonify({"code": 1, "message": f"导出失败: {str(e)}"}) 
# 样书目录管理相关API

@publisher_bp.route('/get_directories', methods=['GET'])
def get_directories():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询该出版社的所有目录
            sql = """
                SELECT d.id, d.name, d.parent_id, COUNT(sb.id) as sample_count
                FROM directories d
                LEFT JOIN sample_books sb ON d.id = sb.parent_id
                WHERE d.publisher_id = %s
                GROUP BY d.id
                ORDER BY d.name
            """
            cursor.execute(sql, (publisher_id,))
            directories = cursor.fetchall()
            
            # 查询总样书数量
            sql = """
                SELECT COUNT(*) as total
                FROM sample_books
                WHERE publisher_id = %s
            """
            cursor.execute(sql, (publisher_id,))
            total_result = cursor.fetchone()
            total_samples = total_result['total'] if total_result else 0
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": directories,
                "total_samples": total_samples
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取目录失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/add_directory', methods=['POST'])
def add_directory():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    name = request.form.get('name')
    parent_id = request.form.get('parent_id')
    
    if not name:
        return jsonify({"code": 1, "message": "目录名称不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查是否存在同名目录
            check_sql = """
                SELECT id FROM directories
                WHERE publisher_id = %s AND name = %s AND (parent_id = %s OR (parent_id IS NULL AND %s IS NULL))
            """
            cursor.execute(check_sql, (publisher_id, name, parent_id, parent_id))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "同级目录下已存在同名目录"})
            
            # 添加新目录
            if parent_id:
                sql = """
                    INSERT INTO directories (name, parent_id, publisher_id)
                    VALUES (%s, %s, %s)
                """
                cursor.execute(sql, (name, parent_id, publisher_id))
            else:
                sql = """
                    INSERT INTO directories (name, publisher_id)
                    VALUES (%s, %s)
                """
                cursor.execute(sql, (name, publisher_id))
            
            connection.commit()
            
            return jsonify({
                "code": 0,
                "message": "添加成功",
                "directory_id": cursor.lastrowid
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"添加目录失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/update_directory', methods=['POST'])
def update_directory():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    directory_id = request.form.get('directory_id')
    name = request.form.get('name')
    parent_id = request.form.get('parent_id')
    
    if not directory_id or not name:
        return jsonify({"code": 1, "message": "目录ID和名称不能为空"})
    
    if parent_id == 'root':
        parent_id = None
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查目录是否存在且属于该出版社
            check_sql = """
                SELECT id FROM directories
                WHERE id = %s AND publisher_id = %s
            """
            cursor.execute(check_sql, (directory_id, publisher_id))
            if not cursor.fetchone():
                return jsonify({"code": 1, "message": "目录不存在或无权限修改"})
            
            # 检查是否存在同名目录
            check_name_sql = """
                SELECT id FROM directories
                WHERE publisher_id = %s AND name = %s AND id != %s
                AND (parent_id = %s OR (parent_id IS NULL AND %s IS NULL))
            """
            cursor.execute(check_name_sql, (publisher_id, name, directory_id, parent_id, parent_id))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "同级目录下已存在同名目录"})
            
            # 检查是否会形成循环引用
            if parent_id:
                # 获取所有子目录ID
                def get_all_children(dir_id):
                    children = []
                    sql = "SELECT id FROM directories WHERE parent_id = %s"
                    cursor.execute(sql, (dir_id,))
                    for child in cursor.fetchall():
                        child_id = child['id']
                        children.append(child_id)
                        children.extend(get_all_children(child_id))
                    return children
                
                children = get_all_children(directory_id)
                if int(parent_id) in children or int(parent_id) == int(directory_id):
                    return jsonify({"code": 1, "message": "不能将目录移动到其子目录下或自身下"})
            
            # 更新目录
            if parent_id:
                sql = """
                    UPDATE directories
                    SET name = %s, parent_id = %s
                    WHERE id = %s AND publisher_id = %s
                """
                cursor.execute(sql, (name, parent_id, directory_id, publisher_id))
            else:
                sql = """
                    UPDATE directories
                    SET name = %s, parent_id = NULL
                    WHERE id = %s AND publisher_id = %s
                """
                cursor.execute(sql, (name, directory_id, publisher_id))
            
            connection.commit()
            
            return jsonify({
                "code": 0,
                "message": "修改成功"
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"修改目录失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/delete_directory', methods=['POST'])
def delete_directory():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    directory_id = request.form.get('directory_id')
    
    if not directory_id:
        return jsonify({"code": 1, "message": "目录ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查目录是否存在且属于该出版社
            check_sql = """
                SELECT id FROM directories
                WHERE id = %s AND publisher_id = %s
            """
            cursor.execute(check_sql, (directory_id, publisher_id))
            if not cursor.fetchone():
                return jsonify({"code": 1, "message": "目录不存在或无权限删除"})
            
            # 删除目录（级联删除会自动删除子目录和样书）
            sql = """
                DELETE FROM directories
                WHERE id = %s AND publisher_id = %s
            """
            cursor.execute(sql, (directory_id, publisher_id))
            
            connection.commit()
            
            return jsonify({
                "code": 0,
                "message": "删除成功"
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"删除目录失败: {str(e)}"})
    finally:
        connection.close()

# 样书管理相关API

@publisher_bp.route('/get_samples', methods=['GET'])
def get_samples():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    directory_id = request.args.get('directory_id')
    search = request.args.get('search', '')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 12))
    
    offset = (page - 1) * limit
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件
            where_clause = "sb.publisher_id = %s"
            params = [publisher_id]
            
            if directory_id:
                where_clause += " AND sb.parent_id = %s"
                params.append(directory_id)
            
            if search:
                where_clause += " AND (sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s)"
                params.extend([f'%{search}%', f'%{search}%', f'%{search}%'])
            
            # 查询样书
            sql = f"""
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info,
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.discount_info, sb.attachment_link,
                       sb.color_system, sb.publisher_name, sb.shipping_discount,
                       sb.settlement_discount, sb.promotion_rate, sb.publication_date,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name
                FROM sample_books sb
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE {where_clause}
                ORDER BY sb.id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([limit, offset])
            
            samples = get_samples_with_features(cursor, sql, params)
            
            # 查询总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM sample_books sb
                WHERE {where_clause}
            """
            
            cursor.execute(count_sql, params[:-2])  # 去掉limit和offset参数
            result = cursor.fetchone()
            total = result['total']
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "samples": samples,
                    "total": total,
                    "page": page,
                    "limit": limit,
                    "directory_id": directory_id
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书列表失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/get_sample_detail', methods=['GET'])
def get_sample_detail():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    sample_id = request.args.get('sample_id')
    
    if not sample_id:
        return jsonify({"code": 1, "message": "样书ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询样书详情
            sql = """
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info,
                       sb.attachment_link, sb.parent_id, sb.discount_info,
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.color_system, sb.courseware,sb.courseware_download_url,
                       sb.sample_download_url, sb.online_reading_url,
                       sb.resources, sb.resource_download_url, sb.publisher_name,
                       sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                       sb.publication_date,
                       d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE sb.id = %s AND sb.publisher_id = %s
            """
            cursor.execute(sql, (sample_id, publisher_id))
            sample = cursor.fetchone()
            
            if not sample:
                return jsonify({"code": 1, "message": "样书不存在或无权限查看"})
            
            # 获取样书特色信息
            features_info = get_sample_features(cursor, sample_id)
            sample['feature_ids'] = features_info['feature_ids']
            sample['feature_names'] = features_info['feature_names']
            # 保持向后兼容
            sample['feature_name'] = ', '.join(features_info['feature_names']) if features_info['feature_names'] else None

            # 格式化日期字段
            if sample.get('publication_date'):
                sample['publication_date'] = sample['publication_date'].strftime('%Y-%m-%d')

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": sample
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书详情失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/add_sample', methods=['POST'])
def add_sample():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    
    # 获取表单数据
    name = request.form.get('name')
    author = request.form.get('author')
    isbn = request.form.get('isbn')
    price = request.form.get('price')
    awards = request.form.get('awards')
    directory_id = request.form.get('directory_id')
    
    # 获取折扣相关字段
    shipping_discount = request.form.get('shipping_discount')
    settlement_discount = request.form.get('settlement_discount')
    promotion_rate = request.form.get('promotion_rate')
    
    # 获取版别信息
    publisher_name = request.form.get('publisher_name')
    
    # 获取新增的表单数据
    level = request.form.get('level')  # 层次：中职、本科、高职、技校
    book_type = request.form.get('book_type')  # 类型
    
    # 国家规划相关
    national_regulation = request.form.get('national_regulation', '0')
    national_regulation_level_id = request.form.get('national_regulation_level_id')
    
    # 省级规划相关
    provincial_regulation = request.form.get('provincial_regulation', '0')
    provincial_regulation_level_id = request.form.get('provincial_regulation_level_id')
    
    # 样书资源相关
    material_type = request.form.get('material_type', '纸质教材')  # 类型（数字教材、纸质教材）
    
    # 获取多选特色ID列表
    feature_ids = []
    feature_ids_str = request.form.get('feature_ids')
    if feature_ids_str:
        try:
            # 支持逗号分隔的特色ID或JSON数组格式
            if feature_ids_str.startswith('['):
                import json
                feature_ids = json.loads(feature_ids_str)
            else:
                feature_ids = [int(id.strip()) for id in feature_ids_str.split(',') if id.strip()]
        except:
            feature_ids = []
    
    color_system = request.form.get('color_system')  # 色系（彩色、双色、四色）
    courseware = request.form.get('courseware')  # 课件描述
    
    # 资源链接
    sample_download_url = request.form.get('sample_download_url')  # 样章下载
    online_reading_url = request.form.get('online_reading_url')  # 在线试读
    resources = request.form.get('resources')  # 资源描述
    resource_download_url = request.form.get('resource_download_url')  # 资源下载
    courseware_download_url = request.form.get('courseware_download_url')  # 课件下载

    # 出版时间
    publication_date = request.form.get('publication_date')  # 出版时间
    
    # 验证必填字段
    if not name or not author or not isbn or not price or not publication_date or not level:
        return jsonify({"code": 1, "message": "样书名称、作者、ISBN号、价格、出版时间和层次为必填项"})

    # 验证目录
    if not directory_id:
        return jsonify({"code": 1, "message": "请选择一个目录"})

    # 验证版别
    if not publisher_name:
        return jsonify({"code": 1, "message": "请选择版别"})
    
    # 验证价格
    try:
        price_value = float(price)
        if price_value <= 0:
            return jsonify({"code": 1, "message": "价格必须大于0"})
    except:
        return jsonify({"code": 1, "message": "价格必须是数值"})
    
    # 初始化折扣值为null
    discount_value = None
    
    # 验证发货折扣、结算折扣和推广费率（改为非必填）
    shipping_discount_value = None
    settlement_discount_value = None
    promotion_rate_value = None

    # 发货折扣验证（非必填）
    if shipping_discount and shipping_discount.strip():
        try:
            shipping_discount_value = float(shipping_discount)
            if shipping_discount_value <= 0 or shipping_discount_value > 1:
                return jsonify({"code": 1, "message": "发货折扣必须是0-1之间的数值"})
        except:
            return jsonify({"code": 1, "message": "发货折扣必须是数值"})

    # 结算折扣验证（非必填）
    if settlement_discount and settlement_discount.strip():
        try:
            settlement_discount_value = float(settlement_discount)
            if settlement_discount_value <= 0 or settlement_discount_value > 1:
                return jsonify({"code": 1, "message": "结算折扣必须是0-1之间的数值"})
        except:
            return jsonify({"code": 1, "message": "结算折扣必须是数值"})

    # 验证发货折扣必须大于或等于结算折扣（只在两个都有值时验证）
    if shipping_discount_value is not None and settlement_discount_value is not None:
        if shipping_discount_value < settlement_discount_value:
            return jsonify({"code": 1, "message": "发货折扣必须大于或等于结算折扣"})
    
    # 如果提供了推广费率，则验证；否则计算默认值
    if promotion_rate and promotion_rate.strip():
        try:
            promotion_rate_value = float(promotion_rate)
            if promotion_rate_value < 0 or promotion_rate_value > 1:
                return jsonify({"code": 1, "message": "推广费率必须是0-1之间的数值"})
        except:
            return jsonify({"code": 1, "message": "推广费率必须是数值"})
    else:
        # 只有在两个折扣都有值时才计算默认推广费率
        if shipping_discount_value is not None and settlement_discount_value is not None:
            promotion_rate_value = shipping_discount_value - settlement_discount_value
        else:
            promotion_rate_value = None
    
    # 验证国家规划和省级规划
    try:
        national_regulation = int(national_regulation)
        if national_regulation not in [0, 1]:
            return jsonify({"code": 1, "message": "国家规划标志必须为0或1"})
    except:
        national_regulation = 0
    
    try:
        provincial_regulation = int(provincial_regulation)
        if provincial_regulation not in [0, 1]:
            return jsonify({"code": 1, "message": "省级规划标志必须为0或1"})
    except:
        provincial_regulation = 0
    
    # 处理规划级别ID，确保是整数或None
    if national_regulation_level_id == '':
        national_regulation_level_id = None
    elif national_regulation_level_id is not None:
        try:
            national_regulation_level_id = int(national_regulation_level_id)
        except:
            national_regulation_level_id = None
    
    if provincial_regulation_level_id == '':
        provincial_regulation_level_id = None
    elif provincial_regulation_level_id is not None:
        try:
            provincial_regulation_level_id = int(provincial_regulation_level_id)
        except:
            provincial_regulation_level_id = None
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取当前用户的出版社单位ID
            cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (publisher_id,))
            user_info = cursor.fetchone()
            if not user_info or not user_info['publisher_company_id']:
                return jsonify({"code": 1, "message": "用户未关联出版社单位"})

            company_id = user_info['publisher_company_id']

            # 检查ISBN是否在同一出版社单位内已存在
            check_sql = """
                SELECT sb.id, sb.name FROM sample_books sb
                JOIN users u ON sb.publisher_id = u.user_id
                WHERE u.publisher_company_id = %s AND sb.isbn = %s
            """
            cursor.execute(check_sql, (company_id, isbn))
            existing_book = cursor.fetchone()
            if existing_book:
                return jsonify({"code": 1, "message": f"本单位内已有ISBN为{isbn}的样书：{existing_book['name']}"})

            
            # 插入样书
            sql = """
            INSERT INTO sample_books (
                name, author, isbn, price, discount_info, award_info, publisher_id, parent_id,
                level, book_type, national_regulation, national_regulation_level_id,
                provincial_regulation, provincial_regulation_level_id, material_type,
                color_system, courseware, sample_download_url, online_reading_url,
                resources, resource_download_url, publisher_name, shipping_discount,
                settlement_discount, promotion_rate, courseware_download_url,
                publication_date
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s,
                %s, %s, %s,
                %s, %s, %s, %s,
                %s, %s, %s, %s,
                %s, %s, %s, %s
            )
        """
            cursor.execute(sql, (
                name, author, isbn, price, discount_value, awards, publisher_id, directory_id,
                level, book_type, national_regulation, national_regulation_level_id,
                provincial_regulation, provincial_regulation_level_id, material_type,
                color_system, courseware, sample_download_url, online_reading_url,
                resources, resource_download_url, publisher_name, shipping_discount_value,
                settlement_discount_value, promotion_rate_value, courseware_download_url,
                publication_date
            ))
            
            sample_id = cursor.lastrowid
            
            # 处理封面文件上传
            attachment_link = None
            if 'cover_file' in request.files:
                cover_file = request.files['cover_file']
                if cover_file and cover_file.filename:
                    try:
                        # 先获取旧封面链接用于删除
                        old_cover_sql = "SELECT attachment_link FROM sample_books WHERE id = %s"
                        cursor.execute(old_cover_sql, (sample_id,))
                        old_cover_result = cursor.fetchone()
                        old_cover = old_cover_result['attachment_link'] if old_cover_result else None
                        
                        # 创建上传目录
                        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
                        
                        # 生成唯一文件名
                        filename = secure_filename(cover_file.filename)
                        name_part, ext = os.path.splitext(filename)
                        unique_filename = f"{uuid.uuid4().hex}_{name_part}{ext}"
                        
                        # 保存文件
                        file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
                        cover_file.save(file_path)
                        
                        # 生成访问链接
                        attachment_link = f"/static/upload/sample_covers/{unique_filename}"
                        
                        # 更新数据库中的封面链接
                        update_cover_sql = """
                            UPDATE sample_books 
                            SET attachment_link = %s 
                            WHERE id = %s
                        """
                        cursor.execute(update_cover_sql, (attachment_link, sample_id))
                        
                        # 删除旧封面文件
                        if old_cover and old_cover != attachment_link:
                            old_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), old_cover.lstrip('/'))
                            if os.path.exists(old_file_path):
                                try:
                                    os.remove(old_file_path)
                                except:
                                    pass
                        
                    except Exception as file_error:
                        # 文件上传失败不影响样书创建，只记录错误
                        print(f"封面文件上传失败: {str(file_error)}")
            
            # 插入样书特色关联
            if feature_ids:
                insert_sample_features(cursor, sample_id, feature_ids)
            
            connection.commit()
            
            return jsonify({
                "code": 0,
                "message": "样书添加成功",
                "sample_id": sample_id,
                "cover_url": attachment_link
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"添加样书失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/update_sample', methods=['POST'])
def update_sample():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    
    # 获取表单数据
    sample_id = request.form.get('sample_id')
    name = request.form.get('name')
    author = request.form.get('author')
    isbn = request.form.get('isbn')
    price = request.form.get('price')
    awards = request.form.get('awards')
    directory_id = request.form.get('directory_id')
    
    # 获取折扣相关字段
    shipping_discount = request.form.get('shipping_discount')
    settlement_discount = request.form.get('settlement_discount')
    promotion_rate = request.form.get('promotion_rate')
    
    # 获取版别信息
    publisher_name = request.form.get('publisher_name')
    
    # 获取新增的表单数据
    level = request.form.get('level')  # 层次：中职、本科、高职、技校
    book_type = request.form.get('book_type')  # 类型
    
    # 国家规划相关
    national_regulation = request.form.get('national_regulation', '0')
    national_regulation_level_id = request.form.get('national_regulation_level_id')
    
    # 省级规划相关
    provincial_regulation = request.form.get('provincial_regulation', '0')
    provincial_regulation_level_id = request.form.get('provincial_regulation_level_id')
    
    # 样书资源相关
    material_type = request.form.get('material_type', '纸质教材')  # 类型（数字教材、纸质教材）
    
    # 获取多选特色ID列表
    feature_ids = []
    feature_ids_str = request.form.get('feature_ids')
    if feature_ids_str:
        try:
            # 支持逗号分隔的特色ID或JSON数组格式
            if feature_ids_str.startswith('['):
                import json
                feature_ids = json.loads(feature_ids_str)
            else:
                feature_ids = [int(id.strip()) for id in feature_ids_str.split(',') if id.strip()]
        except:
            feature_ids = []
    
    color_system = request.form.get('color_system')  # 色系（彩色、双色、四色）
    courseware = request.form.get('courseware')  # 课件描述
    
    # 资源链接
    sample_download_url = request.form.get('sample_download_url')  # 样章下载
    online_reading_url = request.form.get('online_reading_url')  # 在线试读
    resources = request.form.get('resources')  # 资源描述
    resource_download_url = request.form.get('resource_download_url')  # 资源下载
    courseware_download_url = request.form.get('courseware_download_url')  # 课件下载

    # 出版时间
    publication_date = request.form.get('publication_date')  # 出版时间
    
    # 验证必填字段
    if not sample_id or not name or not author or not isbn or not price or not publication_date or not level:
        return jsonify({"code": 1, "message": "样书ID、名称、作者、ISBN号、价格、出版时间和层次为必填项"})

    # 验证版别
    if not publisher_name:
        return jsonify({"code": 1, "message": "请选择版别"})
    
    # 验证价格
    try:
        price_value = float(price)
        if price_value <= 0:
            return jsonify({"code": 1, "message": "价格必须大于0"})
    except:
        return jsonify({"code": 1, "message": "价格必须是数值"})
    
    # 初始化折扣值为null
    discount_value = None
    
    # 验证发货折扣、结算折扣和推广费率（改为非必填）
    shipping_discount_value = None
    settlement_discount_value = None
    promotion_rate_value = None

    # 发货折扣验证（非必填）
    if shipping_discount and shipping_discount.strip():
        try:
            shipping_discount_value = float(shipping_discount)
            if shipping_discount_value <= 0 or shipping_discount_value > 1:
                return jsonify({"code": 1, "message": "发货折扣必须是0-1之间的数值"})
        except:
            return jsonify({"code": 1, "message": "发货折扣必须是数值"})

    # 结算折扣验证（非必填）
    if settlement_discount and settlement_discount.strip():
        try:
            settlement_discount_value = float(settlement_discount)
            if settlement_discount_value <= 0 or settlement_discount_value > 1:
                return jsonify({"code": 1, "message": "结算折扣必须是0-1之间的数值"})
        except:
            return jsonify({"code": 1, "message": "结算折扣必须是数值"})

    # 验证发货折扣必须大于或等于结算折扣（只在两个都有值时验证）
    if shipping_discount_value is not None and settlement_discount_value is not None:
        if shipping_discount_value < settlement_discount_value:
            return jsonify({"code": 1, "message": "发货折扣必须大于或等于结算折扣"})

    # 如果提供了推广费率，则验证；否则计算默认值
    if promotion_rate and promotion_rate.strip():
        try:
            promotion_rate_value = float(promotion_rate)
            if promotion_rate_value < 0 or promotion_rate_value > 1:
                return jsonify({"code": 1, "message": "推广费率必须是0-1之间的数值"})
        except:
            return jsonify({"code": 1, "message": "推广费率必须是数值"})
    else:
        # 只有在两个折扣都有值时才计算默认推广费率
        if shipping_discount_value is not None and settlement_discount_value is not None:
            promotion_rate_value = shipping_discount_value - settlement_discount_value
        else:
            promotion_rate_value = None
    
    # 验证国家规划和省级规划
    try:
        national_regulation = int(national_regulation)
        if national_regulation not in [0, 1]:
            return jsonify({"code": 1, "message": "国家规划标志必须为0或1"})
    except:
        national_regulation = 0
    
    try:
        provincial_regulation = int(provincial_regulation)
        if provincial_regulation not in [0, 1]:
            return jsonify({"code": 1, "message": "省级规划标志必须为0或1"})
    except:
        provincial_regulation = 0
    
    # 处理规划级别ID，确保是整数或None
    if national_regulation_level_id == '':
        national_regulation_level_id = None
    elif national_regulation_level_id is not None:
        try:
            national_regulation_level_id = int(national_regulation_level_id)
        except:
            national_regulation_level_id = None
    
    if provincial_regulation_level_id == '':
        provincial_regulation_level_id = None
    elif provincial_regulation_level_id is not None:
        try:
            provincial_regulation_level_id = int(provincial_regulation_level_id)
        except:
            provincial_regulation_level_id = None
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查样书是否存在且属于当前出版社
            check_sql = """
                SELECT id FROM sample_books
                WHERE id = %s AND publisher_id = %s
            """
            cursor.execute(check_sql, (sample_id, publisher_id))
            if not cursor.fetchone():
                return jsonify({"code": 1, "message": "样书不存在或不属于当前出版社"})
            
            # 获取当前用户的出版社单位ID
            cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (publisher_id,))
            user_info = cursor.fetchone()
            if not user_info or not user_info['publisher_company_id']:
                return jsonify({"code": 1, "message": "用户未关联出版社单位"})

            company_id = user_info['publisher_company_id']

            # 检查是否修改了ISBN，如果是则检查新ISBN是否在同一出版社单位内已存在
            check_isbn_sql = """
                SELECT sb.id, sb.name FROM sample_books sb
                JOIN users u ON sb.publisher_id = u.user_id
                WHERE sb.id != %s AND u.publisher_company_id = %s AND sb.isbn = %s
            """
            cursor.execute(check_isbn_sql, (sample_id, company_id, isbn))
            existing_book = cursor.fetchone()
            if existing_book:
                return jsonify({"code": 1, "message": f"本单位内已有ISBN为{isbn}的样书：{existing_book['name']}"})

            
            # 更新样书
            sql = """
                UPDATE sample_books
                SET name = %s, author = %s, isbn = %s, price = %s, discount_info = %s,
            award_info = %s, parent_id = %s,
                level = %s, book_type = %s, national_regulation = %s, national_regulation_level_id = %s,
                provincial_regulation = %s, provincial_regulation_level_id = %s, material_type = %s,
                color_system = %s, courseware = %s, sample_download_url = %s, online_reading_url = %s,
            resources = %s, resource_download_url = %s, publisher_name = %s, shipping_discount = %s,
            settlement_discount = %s, promotion_rate = %s, courseware_download_url = %s,
            publication_date = %s
                WHERE id = %s AND publisher_id = %s
            """
            cursor.execute(sql, (
                name, author, isbn, price, discount_value, awards, directory_id,
                level, book_type, national_regulation, national_regulation_level_id,
                provincial_regulation, provincial_regulation_level_id, material_type,
                color_system, courseware, sample_download_url, online_reading_url,
                resources, resource_download_url, publisher_name, shipping_discount_value,
                settlement_discount_value, promotion_rate_value, courseware_download_url,
                publication_date, sample_id, publisher_id
            ))
            
            # 处理封面文件上传
            attachment_link = None
            if 'cover_file' in request.files:
                cover_file = request.files['cover_file']
                if cover_file and cover_file.filename:
                    try:
                        # 先获取旧封面链接用于删除
                        old_cover_sql = "SELECT attachment_link FROM sample_books WHERE id = %s"
                        cursor.execute(old_cover_sql, (sample_id,))
                        old_cover_result = cursor.fetchone()
                        old_cover = old_cover_result['attachment_link'] if old_cover_result else None
                        
                        # 创建上传目录
                        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
                        
                        # 生成唯一文件名
                        filename = secure_filename(cover_file.filename)
                        name_part, ext = os.path.splitext(filename)
                        unique_filename = f"{uuid.uuid4().hex}_{name_part}{ext}"
                        
                        # 保存文件
                        file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
                        cover_file.save(file_path)
                        
                        # 生成访问链接
                        attachment_link = f"/static/upload/sample_covers/{unique_filename}"
                        
                        # 更新数据库中的封面链接
                        update_cover_sql = """
                            UPDATE sample_books 
                            SET attachment_link = %s 
                            WHERE id = %s
                        """
                        cursor.execute(update_cover_sql, (attachment_link, sample_id))
                        
                        # 删除旧封面文件
                        if old_cover and old_cover != attachment_link:
                            old_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), old_cover.lstrip('/'))
                            if os.path.exists(old_file_path):
                                try:
                                    os.remove(old_file_path)
                                except:
                                    pass
                        
                    except Exception as file_error:
                        # 文件上传失败不影响样书更新，只记录错误
                        print(f"封面文件上传失败: {str(file_error)}")
            
            # 更新样书特色关联
            insert_sample_features(cursor, sample_id, feature_ids)
            
            connection.commit()
            
            return jsonify({
                "code": 0,
                "message": "样书更新成功",
                "cover_url": attachment_link
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"更新样书失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/delete_sample', methods=['POST'])
def delete_sample():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    sample_id = request.form.get('sample_id')
    
    if not sample_id:
        return jsonify({"code": 1, "message": "样书ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查样书是否存在且属于该出版社
            check_sql = """
                SELECT id, attachment_link FROM sample_books
                WHERE id = %s AND publisher_id = %s
            """
            cursor.execute(check_sql, (sample_id, publisher_id))
            sample = cursor.fetchone()
            
            if not sample:
                return jsonify({"code": 1, "message": "样书不存在或无权限删除"})
            
            # 删除样书封面
            if sample['attachment_link'] and os.path.exists(sample['attachment_link'].lstrip('/')):
                try:
                    os.remove(sample['attachment_link'].lstrip('/'))
                except:
                    pass
            
            # 删除样书
            sql = """
                DELETE FROM sample_books
                WHERE id = %s AND publisher_id = %s
            """
            cursor.execute(sql, (sample_id, publisher_id))
            
            connection.commit()
            
            return jsonify({
                "code": 0,
                "message": "样书删除成功"
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"删除样书失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/new_export_samples', methods=['GET'])
def new_export_samples():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    directory_id = request.args.get('directory_id')
    search = request.args.get('search', '')
    filter_mode = request.args.get('filter_mode', 'directory')  # 'directory' 或 'advanced'
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件
            where_clause = "sb.publisher_id = %s"
            params = [publisher_id]
            
            if filter_mode == 'advanced':
                # 高级筛选模式
                if search:
                    where_clause += " AND (sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s)"
                    params.extend([f'%{search}%', f'%{search}%', f'%{search}%'])
                
                # 解析筛选条件
                try:
                    levels = json.loads(request.args.get('levels', '[]'))
                    types = json.loads(request.args.get('types', '[]'))
                    ranks = json.loads(request.args.get('ranks', '[]'))
                    national_levels = json.loads(request.args.get('national_levels', '[]'))
                    provincial_levels = json.loads(request.args.get('provincial_levels', '[]'))
                    publishers = json.loads(request.args.get('publishers', '[]'))
                    features = json.loads(request.args.get('features', '[]'))
                    
                    # 应用筛选条件
                    if levels:
                        placeholders = ','.join(['%s'] * len(levels))
                        where_clause += f" AND sb.level IN ({placeholders})"
                        params.extend(levels)
                    
                    if types:
                        placeholders = ','.join(['%s'] * len(types))
                        where_clause += f" AND sb.book_type IN ({placeholders})"
                        params.extend(types)
                    
                    if ranks:
                        rank_conditions = []
                        for rank in ranks:
                            if rank == '国家规划':
                                rank_conditions.append("sb.national_regulation = 1")
                            elif rank == '省级规划':
                                rank_conditions.append("sb.provincial_regulation = 1")
                            elif rank == '普通教材':
                                rank_conditions.append("(sb.national_regulation = 0 AND sb.provincial_regulation = 0)")
                        if rank_conditions:
                            where_clause += f" AND ({' OR '.join(rank_conditions)})"
                    
                    if national_levels:
                        placeholders = ','.join(['%s'] * len(national_levels))
                        where_clause += f" AND nrl.name IN ({placeholders})"
                        params.extend(national_levels)
                    
                    if provincial_levels:
                        placeholders = ','.join(['%s'] * len(provincial_levels))
                        where_clause += f" AND prl.name IN ({placeholders})"
                        params.extend(provincial_levels)
                    
                    if publishers:
                        placeholders = ','.join(['%s'] * len(publishers))
                        where_clause += f" AND sb.publisher_name IN ({placeholders})"
                        params.extend(publishers)
                    
                    # 特色筛选需要特殊处理
                    if features:
                        feature_placeholders = ','.join(['%s'] * len(features))
                        where_clause += f" AND EXISTS (SELECT 1 FROM sample_book_features sbf JOIN book_features bf ON sbf.feature_id = bf.id WHERE sbf.sample_id = sb.id AND bf.name IN ({feature_placeholders}))"
                        params.extend(features)
                        
                except json.JSONDecodeError as e:
                    print(f"解析筛选条件失败: {e}")
                    # 如果解析失败，继续用基本查询
                    pass
            else:
                # 目录模式
                if directory_id:
                    where_clause += " AND sb.parent_id = %s"
                    params.append(directory_id)
                
                if search:
                    where_clause += " AND (sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s)"
                    params.extend([f'%{search}%', f'%{search}%', f'%{search}%'])
            
            # 查询样书完整信息
            sql = f"""
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info, 
                       sb.discount_info, d.name as directory_name, sb.publisher_name,
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       sb.material_type, sb.color_system, sb.courseware, 
                       sb.sample_download_url, sb.online_reading_url,
                       sb.resources, sb.resource_download_url, sb.courseware_download_url,
                       sb.shipping_discount, sb.settlement_discount, sb.promotion_rate
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE {where_clause}
                ORDER BY sb.name
            """
            
            cursor.execute(sql, params)
            samples = cursor.fetchall()
            
            # 获取样书特色信息
            samples_with_features = get_samples_with_features(cursor, sql, params)
            
            # 创建Excel文件
            import pandas as pd
            import tempfile
            from datetime import datetime
            
            # 准备数据
            data = []
            for sample in samples_with_features:
                # 处理多选特色，将特色名称用逗号连接
                feature_names = []
                if sample.get('feature_names'):
                    feature_names = sample['feature_names']
                feature_str = '、'.join(feature_names) if feature_names else ''
                
                # 格式化折扣显示（将小数转换为百分比显示）
                shipping_discount_display = ''
                if sample.get('shipping_discount') is not None:
                    shipping_discount_display = f"{float(sample['shipping_discount']) * 100:.1f}%"
                
                settlement_discount_display = ''
                if sample.get('settlement_discount') is not None:
                    settlement_discount_display = f"{float(sample['settlement_discount']) * 100:.1f}%"
                
                promotion_rate_display = ''
                if sample.get('promotion_rate') is not None:
                    promotion_rate_display = f"{float(sample['promotion_rate']) * 100:.1f}%"
                
                data.append({
                    # '样书ID': sample['id'],
                    '样书名称': sample['name'] or '',
                    '作者': sample['author'] or '',
                    'ISBN': sample['isbn'] or '',
                    '版别': sample['publisher_name'] or '',
                    '价格': sample['price'] or '',
                    '出版时间': sample['publication_date'] or '',
                    '层次': sample['level'] or '',
                    '发货折扣': shipping_discount_display,
                    '结算折扣': settlement_discount_display,
                    '图书类型': sample['book_type'] or '',
                    '教材材质': sample['material_type'] or '',
                    '国家规划': sample['national_regulation_level_name'] or '',
                    '省级规划': sample['provincial_regulation_level_name'] or '',
                    '特色': feature_str,
                    '色系': sample['color_system'] or '',
                    '推广费率': promotion_rate_display,
                    '获奖情况': sample['award_info'] or '',
                    '课件描述': sample['courseware'] or '',
                    '课件下载链接': sample['courseware_download_url'] or '',
                    '资源描述': sample['resources'] or '',
                    '资源下载链接': sample['resource_download_url'] or '',
                    '样书下载链接': sample['sample_download_url'] or '',
                    '在线试读链接': sample['online_reading_url'] or '',
                    '所属目录': sample['directory_name'] or '根目录'
                })
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 创建临时Excel文件
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
            
            # 写入Excel
            with pd.ExcelWriter(temp_file.name, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name='样书列表', index=False)
                
                # 获取xlsxwriter工作簿和工作表对象
                workbook = writer.book
                worksheet = writer.sheets['样书列表']
                
                # 设置标题行格式
                header_format = workbook.add_format({
                    'bold': True,
                    'fg_color': '#4472C4',
                    'font_color': 'white',
                    'border': 1,
                    'align': 'center',
                    'valign': 'vcenter'
                })
                
                # 应用格式到标题行
                for col_num, column_title in enumerate(df.columns):
                    worksheet.write(0, col_num, column_title, header_format)
                
                # 设置列宽和格式
                for i, col in enumerate(df.columns):
                    # 根据列内容类型设置适当的列宽
                    if col in ['样书名称', '作者', '课件描述', '资源描述', '获奖情况']:
                        width = 25
                    elif col in ['课件下载链接', '资源下载链接', '样书下载链接', '在线试读链接']:
                        width = 20
                    elif col in ['ISBN', '版别', '所属目录']:
                        width = 15
                    else:
                        width = 12
                    
                    worksheet.set_column(i, i, width)
                
                # 设置表格总体格式
                worksheet.set_row(0, 25)  # 设置标题行高度
                worksheet.freeze_panes(1, 0)  # 冻结标题行
            
            # 生成文件名
            current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            if filter_mode == 'advanced':
                filename = f"样书列表_筛选结果_{current_time}.xlsx"
            else:
                directory_name = request.args.get('directory_name', '')
                directory_suffix = f"_{directory_name.replace('/', '_')}" if directory_id and directory_name else ""
                filename = f"样书列表{directory_suffix}_{current_time}.xlsx"
            
            return send_file(
                temp_file.name,
                as_attachment=True,
                download_name=filename,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
    except Exception as e:
        print(f"导出样书失败: {str(e)}")
        return jsonify({"code": 1, "message": f"导出失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/download_sample_template', methods=['GET'])
def download_sample_template():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    try:
        import os
        from flask import current_app
        
        # 静态模板文件路径
        template_path = os.path.join(current_app.static_folder, '样书批量导入模板.xlsx')
        
        # 检查文件是否存在
        if not os.path.exists(template_path):
            return jsonify({"code": 1, "message": "模板文件不存在，请联系管理员"})
        
        # 返回静态文件
        return send_file(
            template_path,
            as_attachment=True,
            download_name='样书批量导入模板.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        return jsonify({"code": 1, "message": f"下载模板失败: {str(e)}"})

@publisher_bp.route('/batch_upload_samples', methods=['POST'])
def batch_upload_samples():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    print(f"开始批量导入样书, 用户ID: {publisher_id}")
    
    if 'file' not in request.files:
        return jsonify({"code": 1, "message": "未上传文件"})
    
    file = request.files['file']
    
    if file.filename == '':
        return jsonify({"code": 1, "message": "未选择文件"})
    
    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({"code": 1, "message": "请上传Excel文件(.xlsx或.xls)"})
    
    # 获取处理模式（默认为询问）
    duplicate_mode = request.form.get('duplicate_mode', 'ask')  # 可选值：'ask', 'replace', 'skip'

    # 获取目录ID（如果前端传递了的话）
    target_directory_id = request.form.get('directory_id')
    
    try:
        import pandas as pd
        import numpy as np
        
        # 读取Excel文件
        df = pd.read_excel(file)
        
        # 清理列名，移除可能的"（必填）"后缀和空格
        def clean_column_name(col_name):
            if pd.isna(col_name):
                return str(col_name)
            col_name = str(col_name).strip()
            # 移除"（必填）"和"(必填)"后缀
            col_name = col_name.replace('（必填）', '').replace('(必填)', '').strip()
            return col_name
        
        # 清理所有列名
        df.columns = [clean_column_name(col) for col in df.columns]
        
        # 调试信息：输出清理后的列名
        print(f"清理后的Excel列名: {list(df.columns)}")
        # print(f"期望的必填列名: {required_fields}")
        
        # 定义字段映射和必填字段
        field_mapping = {
            '样书名称': 'name',
            '作者': 'author',
            'ISBN号': 'isbn',
            '版别': 'publisher_name',  # 添加版别字段映射
            '价格': 'price',
            '出版时间': 'publication_date',  # 新增出版时间字段映射
            '层次': 'level',
            '发货折扣': 'shipping_discount',
            '结算折扣': 'settlement_discount',
            '推广费率': 'promotion_rate',
            '图书类型': 'book_type',
            '教材材质': 'material_type',
            '色系': 'color_system',
            '国家规划': 'national_regulation',
            '省级规划': 'provincial_regulation',
            '获奖信息': 'award_info',
            '特色': 'features',
            '课件说明': 'courseware',
            '课件下载链接': 'courseware_download_url',  # 确保这个映射正确
            '资源说明': 'resources',
            '资源下载链接': 'resource_download_url',
            '样章下载链接': 'sample_download_url',
            '在线试读链接': 'online_reading_url'
        }
        
        # 必填字段（移除发货折扣、结算折扣、教材材质、图书类型）
        required_fields = ['样书名称', '作者', 'ISBN号', '版别', '价格', '出版时间', '层次']
        
        # 检查必要的列是否存在
        missing_columns = []
        for field in required_fields:
            if field not in df.columns:
                missing_columns.append(field)
        
        if missing_columns:
            return jsonify({"code": 1, "message": f"Excel文件缺少必要的列: {', '.join(missing_columns)}"})
        
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 确定目标目录ID
                if target_directory_id:
                    # 验证目录是否属于当前用户
                    cursor.execute("SELECT id FROM directories WHERE id = %s AND publisher_id = %s",
                                 (target_directory_id, publisher_id))
                    if cursor.fetchone():
                        directory_id = target_directory_id
                    else:
                        target_directory_id = None

                if not target_directory_id:
                    # 获取用户的默认目录
                    cursor.execute("SELECT id FROM directories WHERE publisher_id = %s AND parent_id IS NULL LIMIT 1", (publisher_id,))
                    default_directory = cursor.fetchone()
                    if not default_directory:
                        # 创建默认目录
                        cursor.execute("INSERT INTO directories (name, publisher_id, parent_id) VALUES (%s, %s, %s)",
                                     ("批量导入", publisher_id, None))
                        directory_id = cursor.lastrowid
                    else:
                        directory_id = default_directory['id']
                
                # 获取版别信息
                sql = "SELECT pc.name, pc.is_publisher FROM users u LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id WHERE u.user_id = %s"
                print(f"出版社信息查询SQL: {sql}")
                cursor.execute(sql, (publisher_id,))
                publisher_info = cursor.fetchone()
                print(f"出版社信息查询结果: {publisher_info}")
                
                publisher_name = publisher_info['name'] if publisher_info and publisher_info['name'] else '未知版别'
                is_publisher_only = publisher_info['is_publisher'] == 1 if publisher_info and 'is_publisher' in publisher_info else False
                
                # 调试信息
                print(f"出版社名称: {publisher_name}")
                print(f"是否纯出版社: {is_publisher_only}")
                
                # 预加载特色数据
                cursor.execute("SELECT id, name FROM book_features")
                features_dict = {row['name'].strip(): row['id'] for row in cursor.fetchall()}
                
                # 预加载国家规划级别数据
                cursor.execute("SELECT id, name FROM national_regulation_levels")
                national_levels_dict = {row['name'].strip(): row['id'] for row in cursor.fetchall()}
                
                # 预加载省级规划级别数据
                cursor.execute("SELECT id, name FROM provincial_regulation_levels")
                provincial_levels_dict = {row['name'].strip(): row['id'] for row in cursor.fetchall()}
                
                # 获取当前用户的出版社单位ID
                cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (publisher_id,))
                user_info = cursor.fetchone()
                if not user_info or not user_info['publisher_company_id']:
                    return jsonify({"code": 1, "message": "用户未关联出版社单位"})

                company_id = user_info['publisher_company_id']

                # 预先检查所有ISBN是否在同一出版社单位内已存在
                isbn_list = []
                for index, row in df.iterrows():
                    isbn_value = row.get('ISBN号', '')
                    if not pd.isna(isbn_value) and str(isbn_value).strip() != '' and str(isbn_value).strip() != 'nan':
                        isbn_list.append(str(isbn_value).strip())

                existing_isbns = {}
                if isbn_list:
                    placeholders = ', '.join(['%s'] * len(isbn_list))
                    query = f"""
                        SELECT sb.id, sb.isbn, sb.name FROM sample_books sb
                        JOIN users u ON sb.publisher_id = u.user_id
                        WHERE u.publisher_company_id = %s AND sb.isbn IN ({placeholders})
                    """
                    params = [company_id] + isbn_list
                    cursor.execute(query, params)
                    for row in cursor.fetchall():
                        existing_isbns[row['isbn']] = {'id': row['id'], 'name': row['name']}
                
                # 处理数据
                success_count = 0
                updated_count = 0
                skipped_count = 0
                error_count = 0
                error_details = []
                duplicate_records = []
                
                for index, row in df.iterrows():
                    row_num = index + 2  # Excel行号（从第2行开始）
                    errors = []
                    
                    try:
                        # 数据收集和验证
                        sample_data = {}
                        
                        # 验证必填字段
                        for field in required_fields:
                            value = row.get(field, '')
                            if pd.isna(value) or str(value).strip() == '' or str(value).strip() == 'nan':
                                errors.append(f"{field}不能为空")
                                continue
                            
                            # 特殊处理不同字段类型
                            if field == '价格':
                                try:
                                    price = float(str(value).strip())
                                    if price <= 0:
                                        errors.append("价格必须大于0")
                                        continue
                                    sample_data['price'] = price
                                except:
                                    errors.append("价格必须是有效数字")
                                    continue
                            elif field == '发货折扣':
                                try:
                                    discount = float(str(value).strip())
                                    if discount <= 0 or discount > 1:
                                        errors.append("发货折扣必须在0到1之间")
                                        continue
                                    sample_data['shipping_discount'] = discount
                                except:
                                    errors.append("发货折扣必须是有效数字")
                                    continue
                            elif field == '结算折扣':
                                try:
                                    discount = float(str(value).strip())
                                    if discount <= 0 or discount > 1:
                                        errors.append("结算折扣必须在0到1之间")
                                        continue
                                    sample_data['settlement_discount'] = discount
                                except:
                                    errors.append("结算折扣必须是有效数字")
                                    continue
                            elif field == '层次':
                                # 验证层次是否在允许的值范围内
                                allowed_levels = ['技校', '中职', '高职', '本科']
                                level_value = str(value).strip()
                                if level_value not in allowed_levels:
                                    errors.append(f"层次必须是以下之一: {', '.join(allowed_levels)}")
                                    continue
                                sample_data['level'] = level_value
                            elif field == '图书类型':
                                # 验证图书类型
                                allowed_types = ['教材', '教辅', '参考书', '工具书', '其他']
                                type_value = str(value).strip()
                                if type_value not in allowed_types:
                                    errors.append(f"图书类型必须是以下之一: {', '.join(allowed_types)}")
                                    continue
                                sample_data['book_type'] = type_value
                            elif field == '教材材质':
                                # 验证教材材质
                                allowed_materials = ['纸质教材', '数字教材']
                                material_value = str(value).strip()
                                if material_value not in allowed_materials:
                                    errors.append(f"教材材质必须是以下之一: {', '.join(allowed_materials)}")
                                    continue
                                sample_data['material_type'] = material_value
                            elif field == '版别':
                                # 如果是纯出版社用户，验证填写的版别与所属版别是否一致
                                publisher_value = str(value).strip()
                                if is_publisher_only and publisher_name != '未知版别':
                                    if publisher_value != publisher_name:
                                        errors.append(f"您是出版社用户，版别必须填写为：{publisher_name}")
                                        continue
                                sample_data['publisher_name'] = publisher_value
                            elif field == 'ISBN号':
                                # 将ISBN保存为标准格式，去除空格
                                isbn_value = str(value).strip()
                                sample_data['isbn'] = isbn_value
                            elif field == '出版时间':
                                # 处理出版时间字段，支持多种日期格式
                                try:
                                    from datetime import datetime
                                    import pandas as pd

                                    # 处理空值
                                    if pd.isna(value):
                                        errors.append("出版时间不能为空")
                                        continue

                                    publication_date = None

                                    # 处理不同的数据类型
                                    if isinstance(value, pd.Timestamp):
                                        # pandas的Timestamp类型
                                        publication_date = value.date()
                                    elif isinstance(value, datetime):
                                        # Python的datetime类型
                                        publication_date = value.date()
                                    else:
                                        # 处理字符串格式
                                        value_str = str(value).strip()

                                        # 支持多种日期格式
                                        date_formats = ['%Y-%m-%d', '%Y/%m/%d', '%Y年%m月%d日', '%Y-%m', '%Y/%m']

                                        for fmt in date_formats:
                                            try:
                                                publication_date = datetime.strptime(value_str, fmt).date()
                                                break
                                            except ValueError:
                                                continue

                                    if publication_date:
                                        sample_data['publication_date'] = publication_date
                                    else:
                                        errors.append(f"出版时间格式不正确，实际值：'{value}'，支持格式：YYYY-MM-DD、YYYY/MM/DD、YYYY-MM等")
                                        continue

                                except Exception as e:
                                    errors.append(f"出版时间处理错误: {str(e)}")
                                    continue
                            else:
                                sample_data[field_mapping[field]] = str(value).strip()
                        
                        # 验证折扣关系
                        if 'shipping_discount' in sample_data and 'settlement_discount' in sample_data:
                            if sample_data['shipping_discount'] < sample_data['settlement_discount']:
                                errors.append("发货折扣不能小于结算折扣")
                        
                        # 处理可选字段（添加发货折扣、结算折扣、教材材质、色系、图书类型）
                        optional_fields = ['发货折扣', '结算折扣', '教材材质', '图书类型', '推广费率', '色系', '国家规划', '省级规划', '获奖信息', '特色',
                                         '课件说明', '课件下载链接', '资源说明', '资源下载链接',
                                         '样章下载链接', '在线试读链接']
                        
                        for field in optional_fields:
                            if field in df.columns:
                                value = row.get(field, '')
                                # 调试: 打印字段名和原始值
                                if field == '课件下载链接':
                                    print(f"原始课件下载链接值: '{value}'")
                                    
                                if not pd.isna(value) and str(value).strip() != '' and str(value).strip() != 'nan':
                                    value_str = str(value).strip()

                                    if field == '发货折扣':
                                        try:
                                            discount = float(value_str)
                                            if discount <= 0 or discount > 1:
                                                errors.append("发货折扣必须在0到1之间")
                                                continue
                                            sample_data['shipping_discount'] = discount
                                        except:
                                            errors.append("发货折扣必须是有效数字")
                                            continue
                                    elif field == '结算折扣':
                                        try:
                                            discount = float(value_str)
                                            if discount <= 0 or discount > 1:
                                                errors.append("结算折扣必须在0到1之间")
                                                continue
                                            sample_data['settlement_discount'] = discount
                                        except:
                                            errors.append("结算折扣必须是有效数字")
                                            continue
                                    elif field == '教材材质':
                                        allowed_materials = ['纸质教材', '数字教材']
                                        if value_str not in allowed_materials:
                                            errors.append(f"教材材质必须是以下之一: {', '.join(allowed_materials)}")
                                            continue
                                        sample_data['material_type'] = value_str
                                    elif field == '图书类型':
                                        allowed_types = ['教材', '教辅', '参考书', '工具书', '其他']
                                        if value_str not in allowed_types:
                                            errors.append(f"图书类型必须是以下之一: {', '.join(allowed_types)}")
                                            continue
                                        sample_data['book_type'] = value_str
                                    elif field == '推广费率':
                                        try:
                                            rate = float(value_str)
                                            if rate < 0 or rate > 1:
                                                errors.append("推广费率必须在0到1之间")
                                                continue
                                            sample_data['promotion_rate'] = rate
                                        except:
                                            errors.append("推广费率必须是有效数字")
                                            continue
                                    elif field == '色系':
                                        allowed_colors = ['彩色', '双色', '四色']
                                        if value_str not in allowed_colors:
                                            errors.append(f"色系必须是以下之一: {', '.join(allowed_colors)}")
                                            continue
                                        sample_data['color_system'] = value_str
                                    elif field == '国家规划':
                                        # 查找国家规划级别ID
                                        if value_str in national_levels_dict:
                                            sample_data['national_regulation'] = 1
                                            sample_data['national_regulation_level_id'] = national_levels_dict[value_str]
                                        else:
                                            errors.append(f"国家规划级别'{value_str}'不存在")
                                            continue
                                    elif field == '省级规划':
                                        # 查找省级规划级别ID
                                        if value_str in provincial_levels_dict:
                                            sample_data['provincial_regulation'] = 1
                                            sample_data['provincial_regulation_level_id'] = provincial_levels_dict[value_str]
                                        else:
                                            errors.append(f"省级规划级别'{value_str}'不存在")
                                            continue
                                    elif field == '特色':
                                        # 处理特色字段（支持逗号分隔的多个特色名称，支持中英文逗号）
                                        # 先替换中文逗号为英文逗号，然后分割
                                        feature_str = value_str.replace('，', ',')
                                        feature_names = [name.strip() for name in feature_str.split(',') if name.strip()]
                                        feature_ids = []
                                        not_found_features = []
                                        
                                        for feature_name in feature_names:
                                            feature_name = feature_name.strip()
                                            if feature_name in features_dict:
                                                feature_ids.append(features_dict[feature_name])
                                            else:
                                                not_found_features.append(feature_name)
                                        
                                        if not_found_features:
                                            errors.append(f"特色'{', '.join(not_found_features)}'不存在")
                                        elif feature_ids:
                                            sample_data['feature_ids'] = feature_ids

                                    else:
                                        # 确保所有其他字段都正确映射
                                        field_key = field_mapping.get(field)
                                        if field_key:
                                            sample_data[field_key] = value_str
                        
                        # 如果有错误，记录并跳过
                        if errors:
                            error_count += 1
                            error_details.append({
                                'row': row_num,
                                'errors': errors
                            })
                            continue
                        
                        # 检查ISBN是否已存在
                        isbn = sample_data.get('isbn')
                        if isbn in existing_isbns:
                            if duplicate_mode == 'ask':
                                # 保存到待处理列表，等待用户确认
                                duplicate_records.append({
                                    'row': row_num,
                                    'new_data': sample_data,
                                    'existing_id': existing_isbns[isbn]['id'],
                                    'existing_name': existing_isbns[isbn]['name']
                                })
                                continue
                            elif duplicate_mode == 'skip':
                                # 跳过重复的记录
                                skipped_count += 1
                                continue
                            elif duplicate_mode == 'replace':
                                # 更新现有记录
                                existing_id = existing_isbns[isbn]['id']
                                
                                # 更新样书数据
                                update_fields = []
                                update_values = []
                                
                                for key, value in sample_data.items():
                                    if key != 'isbn' and key != 'feature_ids':  # 排除ISBN和特色IDs
                                        update_fields.append(f"{key} = %s")
                                        update_values.append(value)
                                
                                update_sql = f"""
                                    UPDATE sample_books SET 
                                    {', '.join(update_fields)}
                                    WHERE id = %s AND publisher_id = %s
                                """
                                
                                update_values.append(existing_id)
                                update_values.append(publisher_id)
                                
                                cursor.execute(update_sql, update_values)
                                
                                # 处理特色关联
                                if 'feature_ids' in sample_data and sample_data['feature_ids']:
                                    # 先删除现有关联
                                    cursor.execute("DELETE FROM sample_book_features WHERE sample_id = %s", (existing_id,))
                                    
                                    # 再添加新关联
                                    for feature_id in sample_data['feature_ids']:
                                        cursor.execute(
                                            "INSERT INTO sample_book_features (sample_id, feature_id) VALUES (%s, %s)",
                                            (existing_id, feature_id)
                                        )
                                
                                updated_count += 1
                                continue
                        
                        # 插入新样书数据
                        insert_sql = """
                            INSERT INTO sample_books (
                                name, author, isbn, price, level, book_type, material_type, color_system,
                                shipping_discount, settlement_discount, promotion_rate,
                                national_regulation, national_regulation_level_id,
                                provincial_regulation, provincial_regulation_level_id,
                                award_info, courseware, courseware_download_url, resources, resource_download_url,
                                sample_download_url, online_reading_url,
                                publisher_id, publisher_name, parent_id, publication_date
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                        """
                        
                        cursor.execute(insert_sql, (
                            sample_data.get('name'),
                            sample_data.get('author'),
                            sample_data.get('isbn'),
                            sample_data.get('price'),
                            sample_data.get('level'),
                            sample_data.get('book_type'),
                            sample_data.get('material_type'),
                            sample_data.get('color_system'),
                            sample_data.get('shipping_discount'),
                            sample_data.get('settlement_discount'),
                            sample_data.get('promotion_rate',sample_data.get('shipping_discount') - sample_data.get('settlement_discount')),
                            sample_data.get('national_regulation', 0),
                            sample_data.get('national_regulation_level_id'),
                            sample_data.get('provincial_regulation', 0),
                            sample_data.get('provincial_regulation_level_id'),
                            sample_data.get('award_info'),
                            sample_data.get('courseware'),
                            sample_data.get('courseware_download_url'),
                            sample_data.get('resources'),
                            sample_data.get('resource_download_url'),
                            sample_data.get('sample_download_url'),
                            sample_data.get('online_reading_url'),
                            publisher_id,
                            sample_data.get('publisher_name', publisher_name),
                            directory_id,
                            sample_data.get('publication_date')
                        ))
                        
                        sample_id = cursor.lastrowid
                        
                        # 插入特色关联
                        if 'feature_ids' in sample_data and sample_data['feature_ids']:
                            for feature_id in sample_data['feature_ids']:
                                cursor.execute(
                                    "INSERT INTO sample_book_features (sample_id, feature_id) VALUES (%s, %s)",
                                    (sample_id, feature_id)
                                )
                        
                        success_count += 1
                        
                    except Exception as e:
                        error_count += 1
                        error_details.append({
                            'row': row_num,
                            'errors': [f"数据库错误: {str(e)}"]
                        })
                        continue
                
                # 如果有重复记录且模式为询问，返回重复记录列表给前端处理
                if duplicate_mode == 'ask' and duplicate_records:
                    return jsonify({
                        "code": 2,  # 特殊状态码，表示需要处理重复记录
                        "message": "发现本单位内重复的ISBN，请选择处理方式",
                        "data": {
                            "total_count": len(df),
                            "success_count": success_count,
                            "duplicate_count": len(duplicate_records),
                            "error_count": error_count,
                            "duplicate_records": duplicate_records,
                            "error_details": error_details
                        }
                    })
                
                # 提交事务
                connection.commit()
                
                return jsonify({
                    "code": 0,
                    "message": "批量导入完成",
                    "data": {
                        "total_count": len(df),
                        "success_count": success_count,
                        "updated_count": updated_count,
                        "skipped_count": skipped_count,
                        "error_count": error_count,
                        "error_details": error_details
                    }
                })
        
        except Exception as e:
            connection.rollback()
            return jsonify({"code": 1, "message": f"导入过程中发生错误: {str(e)}"})
        finally:
            connection.close()
    
    except Exception as e:
        return jsonify({"code": 1, "message": f"文件处理失败: {str(e)}"})

@publisher_bp.route('/get_sample_details', methods=['GET'])
def get_sample_details():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    sample_id = request.args.get('sample_id')
    
    if not sample_id:
        return jsonify({"code": 1, "message": "样书ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询样书详情（移除feature_id相关查询）
            sql = """
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.discount_info,
                       sb.award_info, sb.attachment_link, sb.parent_id, d.name as directory_name,
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.color_system, sb.courseware,sb.courseware_download_url,
                       sb.sample_download_url, sb.online_reading_url,
                       sb.resources, sb.resource_download_url, sb.publisher_name,
                       sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                       sb.publication_date,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE sb.id = %s AND sb.publisher_id = %s
            """
            cursor.execute(sql, (sample_id, publisher_id))
            sample = cursor.fetchone()
            
            if not sample:
                return jsonify({"code": 1, "message": "样书不存在或无权限查看"})

            # 获取样书特色
            features = get_sample_features(cursor, sample_id)
            sample['features'] = features

            # 格式化日期字段
            if sample.get('publication_date'):
                sample['publication_date'] = sample['publication_date'].strftime('%Y-%m-%d')

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": sample
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书详情失败: {str(e)}"})
    finally:
        connection.close()

# 获取待处理的报备申请
@publisher_bp.route('/get_pending_reports', methods=['GET'])
def get_pending_reports():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    publisher_id = session['user_id']
    search = request.args.get('search', '')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    offset = (page - 1) * limit
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件
            search_condition = ""
            if search:
                search_condition = "AND (sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s OR pr.school_name LIKE %s)"
            
            # 查询待处理报备（不包含经销商个人信息）
            sql = f"""
                SELECT pr.id, pr.dealer_id, pr.sample_book_id, pr.school_name,
                       pr.status, pr.created_at, pr.updated_at, pr.conflict_reason,
                       pr.attachment, pr.reason,
                       sb.name as sample_name, sb.isbn, sb.author, sb.price,
                       dc.name as dealer_company_name
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                JOIN users u ON pr.dealer_id = u.user_id
                LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                WHERE sb.publisher_id = %s AND pr.status = 'pending'
                {search_condition}
                ORDER BY pr.created_at DESC
                LIMIT %s OFFSET %s
            """
            
            params = [publisher_id]
            if search:
                params.extend([f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'])
            params.extend([limit, offset])
            
            cursor.execute(sql, params)
            reports = cursor.fetchall()
            
            # 获取总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                JOIN users u ON pr.dealer_id = u.user_id
                WHERE sb.publisher_id = %s AND pr.status = 'pending'
                {search_condition}
            """
            
            count_params = [publisher_id]
            if search:
                count_params.extend([f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'])
            
            cursor.execute(count_sql, count_params)
            total = cursor.fetchone()['total']
            
            # 格式化日期
            for report in reports:
                if report['created_at']:
                    report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                if report['updated_at']:
                    report['updated_at'] = report['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
            
            return jsonify({"code": 0, "data": reports, "count": total})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取待处理报备失败: {str(e)}"})
    finally:
        connection.close()

# 获取已处理的报备申请
@publisher_bp.route('/get_processed_reports', methods=['GET'])
def get_processed_reports():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    publisher_id = session['user_id']
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    offset = (page - 1) * limit
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件
            search_condition = ""
            if search:
                search_condition = "AND (sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s OR pr.school_name LIKE %s)"
            
            status_condition = ""
            if status == 'approved':
                # 已通过
                status_condition = "AND pr.status = 'approved'"
            # elif status == 'shipped':
            #     # 已批准且有物流单号
            #     status_condition = "AND pr.status = 'approved' AND pr.tracking_number IS NOT NULL"
            # elif status == 'no_shipment_needed':
            #     # 已批准但无地址信息（无需邮寄）
            #     status_condition = "AND pr.status = 'approved' AND pr.address_id IS NULL"
            elif status == 'rejected':
                status_condition = "AND pr.status = 'rejected'"
            elif status != 'all':
                status_condition = f"AND pr.status = '{status}'"
            else:
                status_condition = "AND pr.status != 'pending'"
            
            # 查询已处理报备（不包含经销商个人信息）
            sql = f"""
                SELECT pr.id, pr.dealer_id, pr.sample_book_id, pr.school_name,
                       pr.status display_status, pr.created_at, pr.updated_at, pr.conflict_reason,
                       pr.attachment, pr.reason,
                       sb.name as sample_name, sb.isbn, sb.author, sb.price,
                       dc.name as dealer_company_name
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                JOIN users u ON pr.dealer_id = u.user_id
                LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                WHERE sb.publisher_id = %s {status_condition} {search_condition}
                ORDER BY pr.updated_at DESC
                LIMIT %s OFFSET %s
            """
            
            params = [publisher_id]
            # 修复参数错误：已拒绝状态不需要额外添加参数
            if search:
                params.extend([f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'])
            params.extend([limit, offset])
            
            cursor.execute(sql, params)
            reports = cursor.fetchall()
            
            # 获取总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                JOIN users u ON pr.dealer_id = u.user_id
                WHERE sb.publisher_id = %s {status_condition} {search_condition}
            """
            
            count_params = [publisher_id]
            # 修复参数错误：已拒绝状态不需要额外添加参数
            if search:
                count_params.extend([f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'])
            
            cursor.execute(count_sql, count_params)
            total = cursor.fetchone()['total']
            
            # 格式化日期
            for report in reports:
                if report['created_at']:
                    report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                if report['updated_at']:
                    report['updated_at'] = report['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                
                # 为前端传递正确的状态字段
                report['status'] = report['display_status']
            
            return jsonify({"code": 0, "data": reports, "count": total})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取已处理报备失败: {str(e)}"})
    finally:
        connection.close()

# 获取报备申请详情
@publisher_bp.route('/get_report_detail', methods=['GET'])
def get_report_detail():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    publisher_id = session['user_id']
    report_id = request.args.get('id')
    
    if not report_id:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取基本信息（不包含经销商个人信息）
            sql = """
                SELECT pr.id, pr.dealer_id, pr.sample_book_id, pr.school_name,
                       pr.status, pr.created_at, pr.updated_at, pr.conflict_reason,
                       pr.attachment, pr.reason, pr.expiry_date, pr.promotion_status,
                       sb.name as sample_name, sb.isbn, sb.author, sb.price,
                       dc.name as dealer_company_name
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                JOIN users u ON pr.dealer_id = u.user_id
                LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                WHERE pr.id = %s AND sb.publisher_id = %s
            """
            cursor.execute(sql, (report_id, publisher_id))
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"code": 1, "message": "报备申请不存在或无权查看"})
            
            # 如果存在冲突理由，查询所有可能冲突的报备
            if report.get('conflict_reason'):
                # 通过样书和学校查询可能冲突的报备（不包含经销商个人信息）
                conflict_search_sql = """
                    SELECT pr.id, pr.school_name, pr.created_at, pr.status,
                           sb.name as sample_name,
                           dc.name as dealer_company_name
                    FROM promotion_reports pr
                    JOIN sample_books sb ON pr.sample_book_id = sb.id
                    JOIN users u ON pr.dealer_id = u.user_id
                    LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                    WHERE pr.id != %s
                          AND pr.sample_book_id = %s
                          AND pr.school_name = %s
                          AND (pr.status = 'approved' OR pr.status = 'pending')
                """
                cursor.execute(conflict_search_sql, (report_id, report['sample_book_id'], report['school_name']))
                conflict_reports = cursor.fetchall()
                
                if conflict_reports:
                    # 格式化冲突报备的日期
                    for conflict_report in conflict_reports:
                        if conflict_report['created_at']:
                            conflict_report['created_at'] = conflict_report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    report['conflict_reports'] = conflict_reports
            
            # 格式化日期
            if report['created_at']:
                report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if report['updated_at']:
                report['updated_at'] = report['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
            
            # 添加计算的状态信息
            # if report['status'] == 'approved':
            #     if report['address_id'] is None:
            #         report['shipping_status'] = 'no_shipment_needed'
            #     elif report['tracking_number'] is not None:
            #         report['shipping_status'] = 'shipped'
            #     else:
            #         report['shipping_status'] = 'pending_shipment'
            # else:
            #     report['shipping_status'] = None
            
            return jsonify({"code": 0, "data": report})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报备详情失败: {str(e)}"})
    finally:
        connection.close()

# 审批订单
@publisher_bp.route('/approve_report', methods=['POST'])
def approve_report():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    publisher_id = session['user_id']
    report_id = request.form.get('id')
    
    if not report_id:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查报备是否存在且属于该出版社
            check_sql = """
                SELECT pr.id, pr.status, pr.sample_book_id, pr.school_name, sb.publisher_id
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                WHERE pr.id = %s
            """
            cursor.execute(check_sql, (report_id,))
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"code": 1, "message": "报备申请不存在"})
            
            if report['publisher_id'] != publisher_id:
                return jsonify({"code": 1, "message": "无权操作此报备申请"})
            
            if report['status'] != 'pending':
                return jsonify({"code": 1, "message": "该报备申请已处理"})
            
            # 更新报备状态为已批准
            update_sql = """
                UPDATE promotion_reports
                SET status = 'approved', 
                    updated_at = NOW()
                WHERE id = %s
            """
            cursor.execute(update_sql, (report_id,))
            
            # 查找并拒绝冲突的报备申请
            find_conflicts_sql = """
                SELECT id FROM promotion_reports 
                WHERE id != %s 
                AND sample_book_id = %s 
                AND school_name = %s 
                AND (status = 'pending' OR status = 'approved')
            """
            cursor.execute(find_conflicts_sql, (report_id, report['sample_book_id'], report['school_name']))
            conflict_reports = cursor.fetchall()
            
            if conflict_reports and len(conflict_reports) > 0:
                # 构建冲突报备ID列表
                conflict_ids = [r['id'] for r in conflict_reports]
                
                # 拒绝所有冲突的报备
                reject_conflicts_sql = """
                    UPDATE promotion_reports
                    SET status = 'rejected', 
                        reason = %s, 
                        updated_at = NOW()
                    WHERE id IN ({})
                """.format(','.join(['%s'] * len(conflict_ids)))
                
                reject_reason = "由于其他同样书同学校的报备申请已获批准，此申请被自动拒绝"
                cursor.execute(reject_conflicts_sql, [reject_reason] + conflict_ids)
            
            connection.commit()
            
            return jsonify({"code": 0, "message": "报备申请已批准"})
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"批准报备失败: {str(e)}"})
    finally:
        connection.close()

# 拒绝报备申请
@publisher_bp.route('/reject_report', methods=['POST'])
def reject_report():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    publisher_id = session['user_id']
    report_id = request.form.get('id')
    reject_reason = request.form.get('reason', '')
    
    if not report_id:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查报备是否存在且属于该出版社
            check_sql = """
                SELECT pr.id, pr.status, sb.publisher_id
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                WHERE pr.id = %s
            """
            cursor.execute(check_sql, (report_id,))
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"code": 1, "message": "报备申请不存在"})
            
            if report['publisher_id'] != publisher_id:
                return jsonify({"code": 1, "message": "无权操作此报备申请"})
            
            if report['status'] != 'pending':
                return jsonify({"code": 1, "message": "该报备申请已处理"})
            
            # 更新报备状态为已拒绝
            update_sql = """
                UPDATE promotion_reports
                SET status = 'rejected', reason = %s, updated_at = NOW()
                WHERE id = %s
            """
            cursor.execute(update_sql, (reject_reason, report_id))
            connection.commit()
            
            return jsonify({"code": 0, "message": "报备申请已拒绝"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"拒绝报备失败: {str(e)}"})
    finally:
        connection.close()

# 撤销报备处理
@publisher_bp.route('/revoke_report', methods=['POST'])
def revoke_report():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    publisher_id = session['user_id']
    report_id = request.form.get('id')
    
    if not report_id:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证报备是否存在且属于当前出版社
            cursor.execute("""
                SELECT pr.id, pr.status, sb.publisher_id 
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                WHERE pr.id = %s
            """, (report_id,))
            
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"code": 1, "message": "报备申请不存在"})
            
            if report['publisher_id'] != publisher_id:
                return jsonify({"code": 1, "message": "无权操作此报备申请"})
            
            if report['status'] == 'pending':
                return jsonify({"code": 1, "message": "该报备申请尚未处理"})
            
            # 更新状态为待处理
            cursor.execute("""
                UPDATE promotion_reports
                SET status = 'pending', 
                    updated_at = NOW()
                WHERE id = %s
            """, (report_id,))
            
            connection.commit()
            
            return jsonify({"code": 0, "message": "操作成功"})
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"撤销处理失败: {str(e)}"})
    finally:
        connection.close()

# 更新报备物流信息（发货）
@publisher_bp.route('/update_report_shipment', methods=['POST'])
def update_report_shipment():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    publisher_id = session['user_id']
    report_id = request.form.get('id')
    shipping_company = request.form.get('shipping_company')
    tracking_number = request.form.get('tracking_number')
    
    if not report_id or not shipping_company or not tracking_number:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证报备是否存在且属于当前出版社
            cursor.execute("""
                SELECT pr.id, pr.status, pr.address_id, sb.publisher_id 
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                WHERE pr.id = %s
            """, (report_id,))
            
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"code": 1, "message": "报备申请不存在"})
            
            if report['publisher_id'] != publisher_id:
                return jsonify({"code": 1, "message": "无权操作此报备申请"})
            
            if report['status'] != 'approved':
                return jsonify({"code": 1, "message": "只能对已批准的报备申请进行发货操作"})
                
            if report['address_id'] is None:
                return jsonify({"code": 1, "message": "该报备申请无收货地址，不需要发货"})
            
            # 更新物流信息
            cursor.execute("""
                UPDATE promotion_reports
                SET tracking_number = %s,
                    shipping_company = %s,
                    updated_at = NOW()
                WHERE id = %s
            """, (tracking_number, shipping_company, report_id))
            
            connection.commit()
            
            return jsonify({"code": 0, "message": "物流信息已更新"})
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"更新物流信息失败: {str(e)}"})
    finally:
        connection.close()

# 更新已发货报备的物流信息
@publisher_bp.route('/update_report_tracking', methods=['POST'])
def update_report_tracking():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    publisher_id = session['user_id']
    report_id = request.form.get('id')
    shipping_company = request.form.get('shipping_company')
    tracking_number = request.form.get('tracking_number')
    
    if not report_id or not shipping_company or not tracking_number:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证报备是否存在且属于当前出版社
            cursor.execute("""
                SELECT pr.id, pr.status, pr.tracking_number, pr.address_id, sb.publisher_id 
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                WHERE pr.id = %s
            """, (report_id,))
            
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"code": 1, "message": "报备申请不存在"})
            
            if report['publisher_id'] != publisher_id:
                return jsonify({"code": 1, "message": "无权操作此报备申请"})
            
            if report['status'] != 'approved' or report['tracking_number'] is None:
                return jsonify({"code": 1, "message": "只能对已发货的报备申请更新物流信息"})
            
            # 更新物流信息
            cursor.execute("""
                UPDATE promotion_reports
                SET tracking_number = %s,
                    shipping_company = %s,
                    updated_at = NOW()
                WHERE id = %s
            """, (tracking_number, shipping_company, report_id))
            
            connection.commit()
            
            return jsonify({"code": 0, "message": "物流信息更新成功"})
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"更新物流信息失败: {str(e)}"})
    finally:
        connection.close()

# 标记报备为无需邮寄 - 已删除，由经销商在提交时决定是否需要邮寄

# 按订单编号批量通过样书申请
@publisher_bp.route('/approve_order', methods=['POST'])
def approve_order():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    order_id = request.form.get('id')
    order_number = request.form.get('order_number')
    
    if not order_id and not order_number:
        return jsonify({"code": 1, "message": "订单ID或订单编号不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 根据参数选择查询条件
            if order_id:
                # 获取订单编号
                sql = """
                    SELECT order_number
                    FROM sample_requests
                    WHERE request_id = %s
                """
                cursor.execute(sql, (order_id,))
                result = cursor.fetchone()
                if not result:
                    return jsonify({"code": 1, "message": "未找到申请信息"})
                order_number = result['order_number']
            
            # 验证申请是否属于该出版社
            check_sql = """
                SELECT COUNT(*) as count
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                WHERE sr.order_number = %s 
                AND sb.publisher_id = %s 
                AND sr.status = 'pending'
            """
            cursor.execute(check_sql, (order_number, publisher_id))
            result = cursor.fetchone()
            
            if not result or result['count'] == 0:
                return jsonify({"code": 1, "message": "未找到订单、无权操作或订单已处理"})
            
            # 更新该订单下所有申请状态为已批准
            update_sql = """
                UPDATE sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                SET sr.status = 'approved', 
                    sr.approval_date = NOW()
                WHERE sr.order_number = %s 
                AND sb.publisher_id = %s 
                AND sr.status = 'pending'
            """
            cursor.execute(update_sql, (order_number, publisher_id))
            affected_rows = cursor.rowcount
            
            connection.commit()
            
            # 获取批准的申请ID列表
            if affected_rows > 0:
                request_ids_sql = """
                    SELECT request_id
                    FROM sample_requests
                    WHERE order_number = %s AND status = 'approved'
                """
                cursor.execute(request_ids_sql, (order_number,))
                results = cursor.fetchall()
                approved_request_ids = [row['request_id'] for row in results]
                
                # 发送邮件通知
                try:
                    result = notify_teacher_request_result(approved_request_ids, action='通过')
                    current_app.logger.info(f"样书申请通过邮件通知发送成功: {result}")
                except Exception as e:
                    # 记录错误但不影响主流程
                    current_app.logger.error(f"发送邮件通知失败: {str(e)}")
            
            return jsonify({
                "code": 0,
                "message": f"已批准 {affected_rows} 条申请"
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"操作失败: {str(e)}"})
    finally:
        connection.close()

# 按订单编号批量拒绝样书申请
@publisher_bp.route('/reject_order', methods=['POST'])
def reject_order():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    
    # 同时支持id和order_number参数
    order_id = request.form.get('id')
    order_number = request.form.get('order_number')
    
    # 至少需要提供一个参数
    if not order_id and not order_number:
        return jsonify({"code": 1, "message": "订单ID或订单编号不能为空"})
    
    reason = request.form.get('reason', '')  # 设置默认值为空字符串
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 根据参数类型构建不同的SQL查询
            if order_id:
                # 获取订单编号
                sql = """
                    SELECT order_number
                    FROM sample_requests
                    WHERE request_id = %s
                """
                cursor.execute(sql, (order_id,))
                result = cursor.fetchone()
                
                if not result:
                    return jsonify({"code": 1, "message": "未找到申请信息"})
                
                order_number = result['order_number']
            
            # 验证申请是否属于该出版社
            check_sql = """
                SELECT COUNT(*) as count
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                WHERE sr.order_number = %s 
                AND sb.publisher_id = %s
                AND sr.status = 'pending'
            """
            cursor.execute(check_sql, (order_number, publisher_id))
            result = cursor.fetchone()
            
            if not result or result['count'] == 0:
                return jsonify({"code": 1, "message": "未找到申请信息、无权操作或申请已处理"})
            
            # 更新该订单下所有申请状态为已拒绝
            update_sql = """
                UPDATE sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                SET sr.status = 'rejected', 
                    sr.approval_date = NOW(),
                    sr.reject_reason = %s
                WHERE sr.order_number = %s 
                AND sb.publisher_id = %s 
                AND sr.status = 'pending'
            """
            
            cursor.execute(update_sql, (reason, order_number, publisher_id))
            
            affected_rows = cursor.rowcount
            connection.commit()
            
            # 获取拒绝的申请ID列表
            if affected_rows > 0:
                request_ids_sql = """
                    SELECT request_id
                    FROM sample_requests
                    WHERE order_number = %s AND status = 'rejected'
                """
                cursor.execute(request_ids_sql, (order_number,))
                results = cursor.fetchall()
                rejected_request_ids = [row['request_id'] for row in results]
                
                # 发送邮件通知
                try:
                    result = notify_teacher_request_result(rejected_request_ids, action='拒绝')
                    current_app.logger.info(f"样书申请拒绝邮件通知发送成功: {result}")
                except Exception as e:
                    # 记录错误但不影响主流程
                    current_app.logger.error(f"发送邮件通知失败: {str(e)}")
            
            return jsonify({
                "code": 0,
                "message": f"已批量拒绝 {affected_rows} 条申请"
            })
    except Exception as e:
        if connection:
            connection.rollback()
        return jsonify({"code": 1, "message": f"拒绝订单失败: {str(e)}"})
    finally:
        if connection:
            connection.close()

# 批量填写快递信息
@publisher_bp.route('/update_order_tracking', methods=['POST'])
def update_order_tracking():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    order_number = request.form.get('order_number')
    tracking_number = request.form.get('tracking_number')
    courier_company = request.form.get('courier_company')
    
    if not order_number or not tracking_number or not courier_company:
        return jsonify({"code": 1, "message": "订单编号、快递单号和快递公司不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证申请是否属于该出版社
            sql = """
                SELECT COUNT(*) as count
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                WHERE sr.order_number = %s AND sb.publisher_id = %s AND sr.status = 'approved'
            """
            
            cursor.execute(sql, (order_number, publisher_id))
            result = cursor.fetchone()
            
            if not result or result['count'] == 0:
                return jsonify({"code": 1, "message": "未找到申请信息、无权操作或申请未被批准"})
            
            # 更新该订单下所有申请的快递信息
            update_sql = """
                UPDATE sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                SET sr.tracking_number = %s, sr.shipping_company = %s, sr.shipping_date = NOW()
                WHERE sr.order_number = %s 
                AND sb.publisher_id = %s 
                AND sr.status = 'approved'
            """
            
            cursor.execute(update_sql, (tracking_number, courier_company, order_number, publisher_id))
            
            affected_rows = cursor.rowcount
            connection.commit()
            
            # 获取更新物流信息的申请ID列表
            if affected_rows > 0:
                request_ids_sql = """
                    SELECT request_id
                    FROM sample_requests
                    WHERE order_number = %s AND tracking_number = %s
                """
                cursor.execute(request_ids_sql, (order_number, tracking_number))
                results = cursor.fetchall()
                updated_request_ids = [row['request_id'] for row in results]
                
                # 发送邮件通知
                try:
                    tracking_info = {
                        'tracking_number': tracking_number,
                        'shipping_company': courier_company
                    }
                    result = notify_teacher_tracking_update(updated_request_ids, tracking_info)
                    current_app.logger.info(f"样书物流信息更新邮件通知发送成功: {result}")
                except Exception as e:
                    # 记录错误但不影响主流程
                    current_app.logger.error(f"发送邮件通知失败: {str(e)}")
            
            return jsonify({
                "code": 0,
                "message": f"已为 {affected_rows} 条申请更新快递信息"
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"操作失败: {str(e)}"})
    finally:
        connection.close()

# 根据地址ID获取收货地址
@publisher_bp.route('/get_shipping_address', methods=['GET'])
def get_shipping_address():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    address_id = request.args.get('address_id')
    
    if not address_id:
        return jsonify({"code": 1, "message": "地址ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证地址是否存在
            sql = """
                SELECT sa.* 
                FROM shipping_addresses sa
                WHERE sa.address_id = %s
            """
            
            cursor.execute(sql, (address_id,))
            address = cursor.fetchone()
            
            if not address:
                return jsonify({"code": 1, "message": "未找到地址信息"})
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": address
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取地址失败: {str(e)}"})
    finally:
        connection.close()

# 按订单编号批量撤销处理
@publisher_bp.route('/revoke_order_processing', methods=['POST'])
def revoke_order_processing():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    order_number = request.form.get('order_number')
    
    if not order_number:
        return jsonify({"code": 1, "message": "订单编号不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查订单是否存在且属于该出版社
            check_sql = """
                SELECT COUNT(*) as count
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                WHERE sr.order_number = %s 
                AND sb.publisher_id = %s
                AND sr.status IN ('approved', 'rejected')
            """
            cursor.execute(check_sql, (order_number, publisher_id))
            result = cursor.fetchone()
            
            if not result or result['count'] == 0:
                return jsonify({"code": 1, "message": "未找到可撤销的订单或订单不属于您"})
            
            # 撤销订单处理，将状态重置为pending
            update_sql = """
                UPDATE sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                SET sr.status = 'pending', 
                    sr.approval_date = NULL,
                    sr.tracking_number = NULL,
                    sr.shipping_company = NULL,
                    sr.shipping_date = NULL,
                    sr.reject_reason = NULL
                WHERE sr.order_number = %s 
                AND sb.publisher_id = %s 
                AND sr.status IN ('approved', 'rejected')
            """
            # 先获取要撤销的申请ID列表，用于后续发送邮件通知
            select_requests_sql = """
                SELECT request_id
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                WHERE sr.order_number = %s 
                AND sb.publisher_id = %s
                AND sr.status IN ('approved', 'rejected')
            """
            cursor.execute(select_requests_sql, (order_number, publisher_id))
            results = cursor.fetchall()
            revoke_request_ids = [row['request_id'] for row in results]
            
            # 执行撤销操作
            cursor.execute(update_sql, (order_number, publisher_id))
            connection.commit()
            
            # 获取更新的记录数
            affected_rows = cursor.rowcount
            
            # 发送邮件通知
            if affected_rows > 0 and revoke_request_ids:
                try:
                    result = notify_teacher_request_result(revoke_request_ids, action='撤销')
                    current_app.logger.info(f"样书申请撤销处理邮件通知发送成功: {result}")
                except Exception as e:
                    # 记录错误但不影响主流程
                    current_app.logger.error(f"发送邮件通知失败: {str(e)}")
            
            return jsonify({
                "code": 0, 
                "message": f"撤销成功，已将{affected_rows}条申请记录重置为待处理状态"
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"撤销处理失败: {str(e)}"})
    finally:
        connection.close()

# 更新订单物流信息
@publisher_bp.route('/update_order_logistics', methods=['POST'])
def update_order_logistics():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    order_number = request.form.get('order_number')
    tracking_number = request.form.get('tracking_number')
    shipping_company = request.form.get('shipping_company')
    
    if not order_number:
        return jsonify({"code": 1, "message": "订单编号不能为空"})
    
    if not tracking_number:
        return jsonify({"code": 1, "message": "运单号不能为空"})
    
    if not shipping_company:
        return jsonify({"code": 1, "message": "快递公司不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查订单是否存在且属于该出版社
            check_sql = """
                SELECT COUNT(*) as count 
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                WHERE sr.order_number = %s 
                AND sb.publisher_id = %s
                AND sr.status = 'approved'
            """
            cursor.execute(check_sql, (order_number, publisher_id))
            result = cursor.fetchone()
            
            if not result or result['count'] == 0:
                return jsonify({"code": 1, "message": "未找到可更新的订单或订单不属于您"})
            
            # 更新订单的物流信息
            update_sql = """
                UPDATE sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                SET sr.tracking_number = %s, 
                    sr.shipping_company = %s,
                    sr.shipping_date = NOW()
                WHERE sr.order_number = %s 
                AND sb.publisher_id = %s
                AND sr.status = 'approved'
            """
            cursor.execute(update_sql, (tracking_number, shipping_company, order_number, publisher_id))
            connection.commit()
            
            # 获取更新的记录数
            affected_rows = cursor.rowcount
            
            return jsonify({
                "code": 0,
                "message": f"更新成功，已为{affected_rows}条申请记录添加物流信息"
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新物流信息失败: {str(e)}"})
    finally:
        connection.close()

# 获取国家规划级别
@publisher_bp.route('/get_national_regulation_levels', methods=['GET'])
def get_national_regulation_levels():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询国家规划级别
            sql = """
                SELECT id, name, description, created_at
                FROM national_regulation_levels
                ORDER BY id
            """
            cursor.execute(sql)
            levels = cursor.fetchall()
            
            return jsonify({"code": 0, "data": levels})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取国家规划级别失败: {str(e)}"})
    finally:
        connection.close()

# 获取省级规划级别
@publisher_bp.route('/get_provincial_regulation_levels', methods=['GET'])
def get_provincial_regulation_levels():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询省级规划级别
            sql = """
                SELECT id, name, province, description, created_at
                FROM provincial_regulation_levels
                ORDER BY id
            """
            cursor.execute(sql)
            levels = cursor.fetchall()
            
            return jsonify({"code": 0, "data": levels})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取省级规划级别失败: {str(e)}"})
    finally:
        connection.close()

# 获取所有特色选项
@publisher_bp.route('/get_book_features', methods=['GET'])
def get_book_features():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询特色选项
            sql = """
                SELECT id, name, description, created_at
                FROM book_features
                ORDER BY id
            """
            cursor.execute(sql)
            features = cursor.fetchall()
            
            return jsonify({"code": 0, "data": features})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取特色选项失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/get_publisher_companies', methods=['GET'])
def get_publisher_companies():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    search = request.args.get('search', '')
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取用户所属的出版社公司信息
            user_sql = """
                SELECT pc.id, pc.name, pc.is_publisher
                FROM users u
                JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                WHERE u.user_id = %s
            """
            cursor.execute(user_sql, (session['user_id'],))
            user_company = cursor.fetchone()
            
            # 如果用户所属公司是仅为出版社，则不需要获取其他出版社
            if user_company and user_company['is_publisher'] == 1:
                return jsonify({
                    "code": 0,
                    "message": "获取成功",
                    "data": {
                        "user_company": user_company,
                        "publisher_companies": [],
                        "is_publisher_only": True
                    }
                })
            
            # 否则获取所有仅为出版社的公司列表
            search_condition = ""
            search_params = []
            if search:
                search_condition = "AND name LIKE %s"
                search_params.append(f'%{search}%')
            
            company_sql = f"""
                SELECT id, name
                FROM publisher_companies
                WHERE is_publisher = 1 {search_condition}
                ORDER BY name
            """
            cursor.execute(company_sql, search_params)
            publisher_companies = cursor.fetchall()
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "user_company": user_company,
                    "publisher_companies": publisher_companies,
                    "is_publisher_only": False
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取出版社列表失败: {str(e)}"})
    finally:
        connection.close()

# 处理单个报备请求（通过或拒绝）
@publisher_bp.route('/process_report', methods=['POST'])
def process_report():
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"})
    
    publisher_id = session['user_id']
    
    # 获取请求数据
    data = request.json
    report_id = data.get('report_id')
    action = data.get('action')  # 'approve' 或 'reject'
    
    # 验证参数
    if not report_id or not action or action not in ['approve', 'reject']:
        return jsonify({"success": False, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查报备是否存在且属于该出版社
            check_sql = """
                SELECT pr.id, pr.status, sb.publisher_id
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                WHERE pr.id = %s
            """
            cursor.execute(check_sql, (report_id,))
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"success": False, "message": "报备申请不存在"})
            
            if report['publisher_id'] != publisher_id:
                return jsonify({"success": False, "message": "无权操作此报备申请"})
            
            if report['status'] != 'pending':
                return jsonify({"success": False, "message": "该报备申请已处理"})
            
            # 根据操作类型处理报备
            if action == 'approve':
                # 批准报备
                update_sql = """
                    UPDATE promotion_reports
                    SET status = 'approved', 
                        updated_at = NOW(),
                        processed_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_sql, (report_id,))
            else:
                # 拒绝报备，这里可以传入拒绝原因，简化版我们不传
                update_sql = """
                    UPDATE promotion_reports
                    SET status = 'rejected', 
                        updated_at = NOW(),
                        processed_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_sql, (report_id,))
            
            connection.commit()
            
            return jsonify({
                "success": True, 
                "message": f"报备申请已{'批准' if action == 'approve' else '拒绝'}"
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"success": False, "message": f"处理报备失败: {str(e)}"})
    finally:
        connection.close()

# 批量处理报备
@publisher_bp.route('/batch_process_reports', methods=['POST'])
def batch_process_reports():
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"})
    
    publisher_id = session['user_id']
    
    # 获取请求数据
    data = request.json
    report_ids = data.get('report_ids', [])
    action = data.get('action')  # 'approve' 或 'reject'
    reason = data.get('reason')  # 拒绝原因，仅当action为reject时使用
    
    # 验证参数
    if not report_ids or not action or action not in ['approve', 'reject']:
        return jsonify({"success": False, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 先筛选出有效的报备
            valid_report_ids = []
            
            for report_id in report_ids:
                # 检查报备是否存在且属于该出版社
                check_sql = """
                    SELECT pr.id, pr.status, sb.publisher_id
                    FROM promotion_reports pr
                    JOIN sample_books sb ON pr.sample_book_id = sb.id
                    WHERE pr.id = %s AND pr.status = 'pending'
                """
                cursor.execute(check_sql, (report_id,))
                report = cursor.fetchone()
                
                if report and report['publisher_id'] == publisher_id:
                    valid_report_ids.append(report_id)
            
            if not valid_report_ids:
                return jsonify({"success": False, "message": "没有可处理的有效报备"})
            
            # 将ID列表转为字符串
            id_list = ','.join(str(id) for id in valid_report_ids)
            
            if action == 'approve':
                # 批量审批
                update_sql = f"""
                        UPDATE promotion_reports
                        SET status = 'approved', updated_at = NOW()
                    WHERE id IN ({id_list})
                    """
                cursor.execute(update_sql)
            else:
                # 批量拒绝
                update_sql = f"""
                        UPDATE promotion_reports
                        SET status = 'rejected', reason = %s, updated_at = NOW()
                    WHERE id IN ({id_list})
                    """
                cursor.execute(update_sql, (reason or "批量拒绝",))
            
            connection.commit()
            
            return jsonify({
                "success": True, 
                "message": f"成功{'批准' if action == 'approve' else '拒绝'}{len(valid_report_ids)}个报备"
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"success": False, "message": f"批量处理报备失败: {str(e)}"})
    finally:
        connection.close()

# 获取冲突报备详情
@publisher_bp.route('/conflict_detail/<int:report_id>', methods=['GET'])
def get_conflict_detail(report_id):
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "请先登录"})
    
    publisher_id = session['user_id']
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取主报备详情
            cursor.execute("""
                SELECT pr.*, sb.name as sample_name, sb.publisher_id,
                       u.name as dealer_name, u.phone_number as dealer_phone, d.company_id,
                       dc.name as dealer_company
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                JOIN users u ON pr.dealer_id = u.user_id
                LEFT JOIN dealers d ON u.user_id = d.user_id
                LEFT JOIN dealer_companies dc ON d.company_id = dc.id
                WHERE pr.id = %s
            """, (report_id,))
            
            report = cursor.fetchone()
            
            if not report:
                return jsonify({"success": False, "message": "报备不存在"})
            
            if report['publisher_id'] != publisher_id:
                return jsonify({"success": False, "message": "无权查看此报备"})
            
            # 获取冲突报备列表
            cursor.execute("""
                SELECT pr.*, u.name as dealer_name, u.phone_number as dealer_phone,
                       d.company_id, dc.name as dealer_company,
                       cr.conflict_reason
                FROM promotion_reports pr
                JOIN conflict_reports cr ON pr.id = cr.conflict_report_id
                JOIN users u ON pr.dealer_id = u.user_id
                LEFT JOIN dealers d ON u.user_id = d.user_id
                LEFT JOIN dealer_companies dc ON d.company_id = dc.id
                WHERE cr.report_id = %s
            """, (report_id,))
            
            conflict_reports = cursor.fetchall()
            
            return jsonify({
                "success": True, 
                "report": report,
                "conflict_reports": conflict_reports
            })
    except Exception as e:
        return jsonify({"success": False, "message": f"获取冲突详情失败: {str(e)}"})
    finally:
        connection.close()

# 订单管理相关API

# 获取订单列表
@publisher_bp.route('/get_orders', methods=['GET'])
def get_orders():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    search = request.args.get('search', '')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    reconciliation_status = request.args.get('reconciliation_status', '')  # 'pre_settlement', 'pending_payment', 'settled'
    payment_status = request.args.get('payment_status', '')  # 0未支付，1已支付
    
    offset = (page - 1) * limit
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件 - 只查询有效订单（删除审核相关逻辑）
            where_clause = """
                WHERE sb.publisher_id = %s AND oi.effective = 1 AND oi.from_dealer = 0
            """
            params = [publisher_id]
                
            # 根据对账状态筛选
            if reconciliation_status:
                where_clause += " AND oi.reconciliation_status = %s"
                params.append(reconciliation_status)
                
            # 根据支付状态筛选
            if payment_status:
                where_clause += " AND oi.payment_status = %s"
                params.append(int(payment_status))
            
            # 添加搜索条件
            if search:
                where_clause += """ AND (
                    sb.name LIKE %s OR 
                    sb.isbn LIKE %s OR
                    oi.school_name LIKE %s
                )"""
                params.extend([f'%{search}%', f'%{search}%', f'%{search}%'])
            
            # 查询订单
            sql = f"""
                SELECT 
                    oi.id, 
                    oi.book_id, 
                    oi.school_name, 
                    oi.shipped_quantity, 
                    oi.returned_quantity, 
                    (oi.shipped_quantity - oi.returned_quantity) as effective_quantity,
                    oi.unit_price, 
                    (oi.shipped_quantity - oi.returned_quantity) * oi.unit_price as total_price,
                    oi.promotion_report_id, 
                    oi.created_at, 
                    oi.from_dealer, 
                    oi.effective,
                    oi.order_number,
                    oi.remark,
                    sb.name as sample_name, 
                    sb.author, 
                    sb.isbn,
                    pr.id as report_id, 
                    pr.status as report_status,
                    pr.promotion_status,
                    pr.expiry_date, 
                    pr.dealer_id,
                    u.name as dealer_name,
                    dc.name as dealer_company,
                    oi.reconciliation_status,
                    oi.payment_status,
                    oi.publisher_quantity,
                    oi.dealer_quantity,
                    oi.publisher_confirm_status,
                    oi.dealer_confirm_status,
                    oi.matched_order_id,
                    oi.last_modified_by
                FROM order_items oi
                JOIN sample_books sb ON oi.book_id = sb.id
                LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                LEFT JOIN users u ON pr.dealer_id = u.user_id
                LEFT JOIN dealers d ON u.user_id = d.user_id
                LEFT JOIN dealer_companies dc ON d.company_id = dc.id
                {where_clause}
                ORDER BY oi.created_at DESC
                LIMIT %s OFFSET %s
            """
            params.extend([limit, offset])
            
            cursor.execute(sql, params)
            orders = cursor.fetchall()
            
            # 获取总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM order_items oi
                JOIN sample_books sb ON oi.book_id = sb.id
                {where_clause}
            """
            
            cursor.execute(count_sql, params[:-2])  # 去掉limit和offset参数
            result = cursor.fetchone()
            total = result['total']
            
            # 格式化日期
            for order in orders:
                if order['created_at']:
                    order['created_at'] = order['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                if order.get('expiry_date'):
                    order['expiry_date'] = order['expiry_date'].strftime('%Y-%m-%d')
            
            return jsonify({
                "code": 0,
                "data": {
                    "orders": orders,
                    "pagination": {
                        "total": total,
                        "page": page,
                        "page_size": limit,
                        "total_pages": (total + limit - 1) // limit
                    }
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取订单列表失败: {str(e)}"})
    finally:
        connection.close()

# 上传订单（出版社自行上传）
@publisher_bp.route('/upload_order', methods=['POST'])
def upload_order():
    """
    上传订单
    请求数据:
        book_id: 样书ID
        school_name: 学校名称
        shipped_quantity: 发货数量
        returned_quantity: 退货数量
        unit_price: 单价
        remark: 备注
    返回:
        处理结果
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    user_id = session.get('user_id')
    
    try:
        data = request.json
        book_id = data.get('book_id')
        school_name = data.get('school_name')
        shipped_quantity = data.get('shipped_quantity', 0)
        returned_quantity = data.get('returned_quantity', 0)
        unit_price = data.get('unit_price')
        remark = data.get('remark', '')
        
        if not book_id or not school_name:
            return jsonify({"code": 1, "message": "样书ID和学校名称为必填项"})
        
        if shipped_quantity <= 0:
            return jsonify({"code": 1, "message": "发货数量必须大于0"})
        
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 验证样书是否存在
                sql = """
                    SELECT id, name, publisher_id FROM sample_books WHERE id = %s
                """
                cursor.execute(sql, (book_id,))
                sample = cursor.fetchone()
                
                if not sample:
                    return jsonify({"code": 1, "message": "样书不存在"})
                
                # 验证当前用户是否是该样书的出版社
                if sample['publisher_id'] != user_id:
                    return jsonify({"code": 1, "message": "您不是该样书的出版社，无法上传订单"})
                
                # 生成订单编号
                import random
                current_time = datetime.now()
                timestamp = current_time.strftime('%Y%m%d%H%M%S')
                random_num = ''.join([str(random.randint(0, 9)) for _ in range(4)])
                order_number = f"PO{timestamp}{random_num}"
                duplicate_check_sql = """
                    SELECT id, shipped_quantity, returned_quantity, unit_price, order_number, 
                           created_at, matched_order_id, reconciliation_status
                    FROM order_items
                    WHERE book_id = %s AND school_name = %s AND from_dealer = 0 
                    AND effective = 1 AND created_at > DATE_SUB(NOW(), INTERVAL 6 MONTH)
                    ORDER BY created_at DESC
                    LIMIT 1
                """
                cursor.execute(duplicate_check_sql, (book_id, school_name))
                existing_order = cursor.fetchone()
                
                # 检查是否需要用户确认累加
                force_accumulate = request.form.get('force_accumulate', 'false').lower() == 'true'
                
                if existing_order and not force_accumulate:
                    # 存在重复订单，返回确认信息
                    return jsonify({
                        "code": 2,  # 特殊代码表示需要用户确认
                        "message": "检测到相同样书和学校的订单",
                        "data": {
                            "duplicate_order": {
                                "id": existing_order['id'],
                                "order_number": existing_order['order_number'],
                                "shipped_quantity": existing_order['shipped_quantity'],
                                "returned_quantity": existing_order['returned_quantity'],
                                "unit_price": existing_order['unit_price'],
                                "created_at": existing_order['created_at'].strftime('%Y-%m-%d %H:%M:%S'),
                                "reconciliation_status": existing_order['reconciliation_status']
                            },
                            "new_order": {
                                "shipped_quantity": shipped_quantity,
                                "returned_quantity": returned_quantity,
                                "unit_price": unit_price
                            },
                            "sample_name": sample['name'],
                            "school_name": school_name
                        }
                    })
                
                # 如果用户确认累加或不存在重复订单，则创建或更新订单
                if existing_order and force_accumulate:
                    # 更新现有订单，累加数量
                    new_shipped_quantity = existing_order['shipped_quantity'] + shipped_quantity
                    new_returned_quantity = existing_order['returned_quantity'] + returned_quantity
                    
                    update_sql = """
                        UPDATE order_items 
                        SET shipped_quantity = %s, returned_quantity = %s, unit_price = %s, 
                            publisher_quantity = %s,
                            updated_at = %s
                        WHERE id = %s
                    """
                    
                    current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')
                    
                    cursor.execute(update_sql, (
                        new_shipped_quantity,
                        new_returned_quantity,
                        unit_price,  # 使用最新的单价
                        new_shipped_quantity,  # 更新publisher_quantity
                        current_time_str,
                        existing_order['id']
                    ))
                    
                    publisher_order_id = existing_order['id']
                    order_number = existing_order['order_number']
                    
                    # 添加累加历史记录
                    history_sql = """
                        INSERT INTO order_reconciliation_history
                        (order_id, user_id, user_role, action_type, new_quantity, old_quantity, remark)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(history_sql, (
                        publisher_order_id,
                        user_id,
                        'publisher',
                        'accumulate',
                        new_shipped_quantity,
                        existing_order['shipped_quantity'],
                        f'订单数量累加'
                    ))
                    
                    # 如果原订单已经匹配了，需要重新评估匹配状态
                    if existing_order['matched_order_id']:
                        # 将对账状态重置为预结算，需要重新确认
                        reset_status_sql = """
                            UPDATE order_items 
                            SET reconciliation_status = 'pre_settlement', 
                                dealer_confirm_status = 'unconfirmed'
                            WHERE id = %s
                        """
                        cursor.execute(reset_status_sql, (publisher_order_id,))
                        
                        # 同时更新匹配的经销商订单状态
                        update_dealer_sql = """
                            UPDATE order_items 
                            SET reconciliation_status = 'pre_settlement',
                                publisher_quantity = %s
                            WHERE id = %s
                        """
                        cursor.execute(update_dealer_sql, (new_shipped_quantity, existing_order['matched_order_id']))
                        
                        # 更新匹配记录状态
                        update_match_sql = """
                            UPDATE order_matches
                            SET reconciliation_status = 'pre_settlement'
                            WHERE publisher_order_id = %s
                        """
                        cursor.execute(update_match_sql, (publisher_order_id,))
                        
                else:
                    # 创建新订单
                    sql = """
                        INSERT INTO order_items 
                        (book_id, school_name, shipped_quantity, returned_quantity, unit_price, 
                         from_dealer, effective, order_number, remark, created_at, 
                         publisher_quantity, publisher_confirm_status, reconciliation_status, payment_status) 
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')
                    
                    # 默认出版社订单确认自己的数量
                    publisher_quantity = shipped_quantity
                    publisher_confirm_status = 'confirmed'
                    
                    cursor.execute(sql, (
                        book_id, 
                        school_name, 
                        shipped_quantity, 
                        returned_quantity, 
                        unit_price, 
                        0,  # from_dealer=0表示来自出版社的订单
                        1,  # effective=1表示有效状态
                        order_number, 
                        remark, 
                        current_time_str,
                        publisher_quantity,
                        publisher_confirm_status,
                        'pre_settlement',  # 默认为预结算状态
                        0  # 默认为未支付状态
                    ))
                    
                    # 获取刚插入的订单ID
                    publisher_order_id = cursor.lastrowid
                
                # 添加对账历史记录（仅对新订单）
                if not (existing_order and force_accumulate):
                    history_sql = """
                        INSERT INTO order_reconciliation_history
                        (order_id, user_id, user_role, action_type, new_quantity, remark)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(history_sql, (
                        publisher_order_id,
                        user_id,
                        'publisher',
                        'upload',
                        shipped_quantity,
                        '出版社上传订单'
                    ))
                
                # 首先检查是否有对应的报备需要更新为推广成功状态
                report_update_sql = """
                    SELECT id FROM promotion_reports
                    WHERE sample_book_id = %s AND school_name = %s
                    AND status = 'approved' AND created_at < %s
                    ORDER BY created_at DESC LIMIT 1
                """
                cursor.execute(report_update_sql, (book_id, school_name, current_time_str))
                corresponding_report = cursor.fetchone()

                if corresponding_report:
                    # 更新报备状态为推广成功
                    update_report_sql = """
                        UPDATE promotion_reports
                        SET promotion_status = 'successful'
                        WHERE id = %s
                    """
                    cursor.execute(update_report_sql, (corresponding_report['id'],))

                # 查找匹配的经销商订单进行对账
                match_sql = """
                    SELECT oi.id, oi.shipped_quantity, oi.dealer_quantity, oi.dealer_confirm_status,
                           oi.promotion_report_id, oi.school_name, sb.name as book_name,
                           u.user_id as dealer_id, u.name as dealer_name,
                           pr.status as report_status, pr.promotion_status
                    FROM order_items oi
                    INNER JOIN sample_books sb ON oi.book_id = sb.id
                    LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                    LEFT JOIN users u ON pr.dealer_id = u.user_id
                    WHERE oi.book_id = %s AND oi.school_name = %s AND oi.from_dealer = 1
                    AND oi.effective = 1 AND oi.matched_order_id IS NULL
                    AND oi.promotion_report_id IS NOT NULL
                    AND pr.status IN ('approved', 'successful')
                    ORDER BY oi.created_at DESC
                    LIMIT 1
                """
                cursor.execute(match_sql, (book_id, school_name))
                matching_order = cursor.fetchone()
                
                matched_dealer_order = None
                if matching_order:
                    dealer_order_id = matching_order['id']
                    dealer_quantity = matching_order['shipped_quantity']

                    # 判断实销数量是否一致
                    if shipped_quantity == dealer_quantity:
                        reconciliation_status = 'pending_payment'
                        message_suffix = '，数量一致，已进入待支付状态'
                        need_reconciliation = False
                    else:
                        reconciliation_status = 'pre_settlement'
                        message_suffix = '，数量不一致，需要对账'
                        need_reconciliation = True

                    # 创建订单匹配记录
                    match_insert_sql = """
                        INSERT INTO order_matches
                        (publisher_order_id, dealer_order_id, reconciliation_status)
                        VALUES (%s, %s, %s)
                    """
                    cursor.execute(match_insert_sql, (publisher_order_id, dealer_order_id, reconciliation_status))

                    # 更新双方订单的匹配ID和对账状态
                    update_publisher_sql = """
                        UPDATE order_items SET
                        matched_order_id = %s,
                        dealer_quantity = %s,
                        reconciliation_status = %s
                        WHERE id = %s
                    """
                    cursor.execute(update_publisher_sql, (
                        dealer_order_id,
                        dealer_quantity,
                        reconciliation_status,
                        publisher_order_id
                    ))

                    update_dealer_sql = """
                        UPDATE order_items SET
                        matched_order_id = %s,
                        publisher_quantity = %s,
                        publisher_confirm_status = %s,
                        reconciliation_status = %s
                        WHERE id = %s
                    """
                    cursor.execute(update_dealer_sql, (
                        publisher_order_id,
                        shipped_quantity,
                        'confirmed',  # 出版社确认
                        reconciliation_status,
                        dealer_order_id
                    ))

                    # 更新订单匹配记录状态
                    update_match_sql = """
                        UPDATE order_matches
                        SET reconciliation_status = %s
                        WHERE publisher_order_id = %s AND dealer_order_id = %s
                    """
                    cursor.execute(update_match_sql, (reconciliation_status, publisher_order_id, dealer_order_id))

                    # 添加自动匹配的对账历史记录
                    match_history_sql = """
                        INSERT INTO order_reconciliation_history
                        (order_id, user_id, user_role, action_type, new_quantity, remark)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(match_history_sql, (
                        publisher_order_id,
                        user_id,
                        'publisher',
                        'match',
                        shipped_quantity,
                        f'自动匹配经销商订单{message_suffix}'
                    ))

                    # 保存匹配的经销商订单信息
                    matched_dealer_order = {
                        'order_id': dealer_order_id,
                        'dealer_name': matching_order['dealer_name'],
                        'dealer_quantity': dealer_quantity,
                        'publisher_quantity': shipped_quantity,
                        'need_reconciliation': need_reconciliation,
                        'reconciliation_status': reconciliation_status
                    }
                
                # 检查是否有推荐成功
                process_recommendation_success(connection, order_number)
                
                connection.commit()
                
                # 根据匹配情况和对账状态返回不同消息
                if matched_dealer_order:
                    if matched_dealer_order['reconciliation_status'] == 'pending_payment':
                        return jsonify({
                            "code": 0,
                            "message": "订单上传成功，已匹配到经销商订单，数量一致，已进入待支付状态",
                            "order_number": order_number,
                            "need_reconciliation": False,
                            "order_id": publisher_order_id,
                            "matched_order": matched_dealer_order
                        })
                    else:
                        return jsonify({
                            "code": 0,
                            "message": "订单上传成功，已匹配到经销商订单，数量不一致，需要对账",
                            "order_number": order_number,
                            "need_reconciliation": True,
                            "order_id": publisher_order_id,
                            "matched_order": matched_dealer_order
                        })
                else:
                    if corresponding_report:
                        return jsonify({"code": 0, "message": "订单上传成功，对应报备已更新为推广成功状态", "order_number": order_number})
                    else:
                        return jsonify({"code": 0, "message": "订单上传成功", "order_number": order_number})
        except Exception as e:
            connection.rollback()
            return jsonify({"code": 1, "message": f"上传订单失败: {str(e)}"})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"上传订单失败: {str(e)}"})

# 添加检测推荐成功的函数
def process_recommendation_success(connection, order_number):
    with connection.cursor(pymysql.cursors.DictCursor) as cursor:
        # 获取订单项
        sql = """
            SELECT oi.* 
            FROM order_items oi
            WHERE oi.order_number = %s AND oi.effective = 1
        """
        cursor.execute(sql, (order_number,))
        order_items = cursor.fetchall()
        
        if not order_items:
            return
        
        # 检查每个订单项是否匹配推荐
        for item in order_items:
            book_id = item['book_id']
            school_name = item['school_name']
            
            if not school_name:
                continue
            
            # 查找匹配的学校
            cursor.execute("SELECT id FROM schools WHERE name = %s", (school_name,))
            school = cursor.fetchone()
            
            if not school:
                continue
            
            school_id = school['id']
            
            # 查找进行中的推荐
            sql = """
                SELECT DISTINCT br.id 
                FROM book_recommendations br
                JOIN recommendation_results rr ON br.id = rr.recommendation_id
                WHERE br.school_id = %s AND br.status = 'in_progress' 
                AND rr.recommended_book_id = %s AND rr.status = 'pending'
            """
            cursor.execute(sql, (school_id, book_id))
            recommendations = cursor.fetchall()
            
            for rec in recommendations:
                # 更新推荐结果状态
                sql = """
                    UPDATE recommendation_results 
                    SET status = 'successful', updated_at = NOW() 
                    WHERE recommendation_id = %s AND recommended_book_id = %s
                """
                cursor.execute(sql, (rec['id'], book_id))
                
                # 检查是否所有推荐结果都已成功
                sql = """
                    SELECT COUNT(*) as total, SUM(CASE WHEN status = 'successful' THEN 1 ELSE 0 END) as successful
                    FROM recommendation_results
                    WHERE recommendation_id = %s
                """
                cursor.execute(sql, (rec['id'],))
                stats = cursor.fetchone()
                
                # 如果至少有一个推荐成功，则标记推荐为完成
                if stats['successful'] > 0:
                    sql = "UPDATE book_recommendations SET status = 'completed', updated_at = NOW() WHERE id = %s"
                    cursor.execute(sql, (rec['id'],))

# 上传多个教材的订单
@publisher_bp.route('/upload_orders', methods=['POST'])
def upload_orders():
    """
    上传多个教材的订单
    请求数据:
        school_id: 学校ID
        books: [
            {
                book_id: 样书ID,
                shipped_quantity: 发货数量,
                remark: 备注
            }
        ]
    返回:
        处理结果
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})

    user_id = session.get('user_id')

    try:
        data = request.json
        school_id = data.get('school_id')
        books = data.get('books', [])

        if not school_id:
            return jsonify({"code": 1, "message": "请选择学校"})

        if not books:
            return jsonify({"code": 1, "message": "请选择教材"})

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 获取学校名称
                school_sql = "SELECT name FROM schools WHERE id = %s"
                cursor.execute(school_sql, (school_id,))
                school = cursor.fetchone()

                if not school:
                    return jsonify({"code": 1, "message": "学校不存在"})

                school_name = school['name']
                success_count = 0
                error_messages = []

                for book_data in books:
                    book_id = book_data.get('book_id')
                    shipped_quantity = book_data.get('shipped_quantity', 0)
                    remark = book_data.get('remark', '')

                    if not book_id or shipped_quantity <= 0:
                        error_messages.append(f"教材ID {book_id}: 数据无效")
                        continue

                    try:
                        # 验证样书是否存在并获取信息
                        book_sql = """
                            SELECT id, name, price, publisher_id
                            FROM sample_books
                            WHERE id = %s
                        """
                        cursor.execute(book_sql, (book_id,))
                        book = cursor.fetchone()

                        if not book:
                            error_messages.append(f"教材ID {book_id}: 教材不存在")
                            continue

                        # 验证是否为当前出版社的教材
                        if book['publisher_id'] != user_id:
                            error_messages.append(f"教材《{book['name']}》: 无权上传此教材订单")
                            continue

                        # 使用教材的默认价格
                        unit_price = book['price']

                        # 插入订单
                        insert_sql = """
                            INSERT INTO order_items
                            (book_id, school_name, shipped_quantity, returned_quantity, unit_price,
                             from_dealer, effective, remark, created_at, reconciliation_status, payment_status)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s)
                        """

                        cursor.execute(insert_sql, (
                            book_id,
                            school_name,
                            shipped_quantity,
                            0,  # returned_quantity
                            unit_price,
                            0,  # from_dealer
                            1,  # effective
                            remark,
                            'pre_settlement',  # reconciliation_status
                            0   # payment_status
                        ))

                        success_count += 1

                    except Exception as e:
                        error_messages.append(f"教材《{book.get('name', book_id)}》: {str(e)}")
                        continue

                connection.commit()

                # 返回结果
                if success_count > 0:
                    message = f"成功上传 {success_count} 个教材订单"
                    if error_messages:
                        message += f"，{len(error_messages)} 个失败"
                    return jsonify({
                        "code": 0,
                        "message": message,
                        "data": {
                            "success_count": success_count,
                            "error_count": len(error_messages),
                            "errors": error_messages
                        }
                    })
                else:
                    return jsonify({
                        "code": 1,
                        "message": "所有教材订单上传失败",
                        "data": {"errors": error_messages}
                    })

        finally:
            connection.close()

    except Exception as e:
        return jsonify({"code": 1, "message": f"上传订单失败: {str(e)}"})

# 批量上传订单（通过Excel文件）
@publisher_bp.route('/batch_upload_orders', methods=['POST'])
def batch_upload_orders():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    force_update = request.form.get('force_update') == 'true'  # 是否强制覆盖所有重复订单
    
    if 'excel_file' not in request.files:
        return jsonify({"code": 1, "message": "请上传Excel文件"})
    
    file = request.files['excel_file']
    if not file or file.filename == '':
        return jsonify({"code": 1, "message": "未选择文件"})
    
    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({"code": 1, "message": "请上传Excel文件(.xlsx或.xls)"})
    
    connection = get_db_connection()
    try:
        # 读取Excel文件
        import pandas as pd
        import datetime
        import random
        
        df = pd.read_excel(file)
        
        # 清理列名，移除可能的"（必填）"后缀和空格
        def clean_column_name(col_name):
            if pd.isna(col_name):
                return str(col_name)
            col_name = str(col_name).strip()
            # 移除"（必填）"和"(必填)"后缀
            col_name = col_name.replace('（必填）', '').replace('(必填)', '').strip()
            return col_name
        
        # 清理所有列名
        df.columns = [clean_column_name(col) for col in df.columns]
        
        # 调试信息：输出清理后的列名
        print(f"清理后的Excel列名: {list(df.columns)}")
        print(f"期望的必填列名: {required_fields}")
        
        # 定义字段映射和必填字段
        field_mapping = {
            '样书名称': 'name',
            '作者': 'author',
            'ISBN号': 'isbn',
            '版别': 'publisher_name',  # 添加版别字段映射
            '价格': 'price',
            '出版时间': 'publication_date',  # 新增出版时间字段映射
            '层次': 'level',
            '发货折扣': 'shipping_discount',
            '结算折扣': 'settlement_discount',
            '推广费率': 'promotion_rate',
            '图书类型': 'book_type',
            '教材材质': 'material_type',
            '色系': 'color_system',
            '国家规划': 'national_regulation',
            '省级规划': 'provincial_regulation',
            '获奖信息': 'award_info',
            '特色': 'features',
            '课件说明': 'courseware',
            '课件下载链接': 'courseware_download_url',  # 确保这个映射正确
            '资源说明': 'resources',
            '资源下载链接': 'resource_download_url',
            '样章下载链接': 'sample_download_url',
            '在线试读链接': 'online_reading_url'
        }
        
        # 必填字段（移除发货折扣、结算折扣、教材材质、图书类型）
        required_fields = ['样书名称', '作者', 'ISBN号', '版别', '价格', '层次']
        
        # 检查必要的列是否存在
        missing_columns = []
        for field in required_fields:
            if field not in df.columns:
                missing_columns.append(field)
        
        if missing_columns:
            return jsonify({"code": 1, "message": f"Excel文件缺少必要的列: {', '.join(missing_columns)}"})
        
        # 开始处理数据
        successful_orders = 0
        failed_orders = 0
        error_messages = []
        duplicate_orders = []
        
        for index, row in df.iterrows():
            try:
                # 提取数据
                isbn = str(row['ISBN号'])
                school_name = str(row['学校名称(必填)'])
                shipped_quantity = int(row['发货量(必填)'])
                returned_quantity = int(row['退货量']) if '退货量' in row and not pd.isna(row['退货量']) else 0
                unit_price = float(row['单价(必填)'])
                remark = str(row['备注']) if '备注' in row and not pd.isna(row['备注']) else ""
                
                # 生成订单编号：ORD + 年月日 + 6位随机数字
                date_str = datetime.datetime.now().strftime('%Y%m%d')
                random_digits = ''.join([str(random.randint(0, 9)) for _ in range(6)])
                order_number = f"ORD{date_str}{random_digits}"
                
                with connection.cursor() as cursor:
                    # 通过ISBN查询样书ID
                    sql = "SELECT id, publisher_id FROM sample_books WHERE isbn = %s"
                    cursor.execute(sql, (isbn,))
                    book = cursor.fetchone()
                    
                    if not book or book['publisher_id'] != publisher_id:
                        failed_orders += 1
                        error_messages.append(f"第{index+2}行: ISBN {isbn} 不存在或无权操作")
                        continue
                    
                    book_id = book['id']
                    
                    # 检查是否存在半年内相同样书和学校的订单
                    check_duplicate_sql = """
                        SELECT id, shipped_quantity, returned_quantity, unit_price, created_at, order_number, remark
                        FROM order_items
                        WHERE book_id = %s AND school_name = %s AND created_at > DATE_SUB(NOW(), INTERVAL 6 MONTH)
                        ORDER BY created_at DESC
                        LIMIT 1
                    """
                    cursor.execute(check_duplicate_sql, (book_id, school_name))
                    duplicate_order = cursor.fetchone()
                    
                    # 如果存在重复订单且不是强制覆盖模式，添加到重复列表中
                    if duplicate_order and not force_update:
                        duplicate_orders.append({
                            "row": index + 2,
                            "isbn": isbn,
                            "school_name": school_name,
                            "order_id": duplicate_order['id'],
                            "created_at": duplicate_order['created_at'].strftime('%Y-%m-%d %H:%M:%S'),
                            "existing_data": {
                                "shipped_quantity": duplicate_order['shipped_quantity'],
                                "returned_quantity": duplicate_order['returned_quantity'],
                                "unit_price": duplicate_order['unit_price'],
                                "order_number": duplicate_order['order_number']
                            },
                            "new_data": {
                                "shipped_quantity": shipped_quantity,
                                "returned_quantity": returned_quantity,
                                "unit_price": unit_price
                            }
                        })
                        continue
                    
                    # 如果是强制覆盖模式且存在重复订单，则更新现有订单
                    if duplicate_order and force_update:
                        update_sql = """
                            UPDATE order_items
                            SET shipped_quantity = %s, returned_quantity = %s, unit_price = %s, remark = %s
                            WHERE id = %s
                        """
                        cursor.execute(update_sql, (
                            shipped_quantity, returned_quantity, unit_price, remark, duplicate_order['id']
                        ))
                        successful_orders += 1
                        continue
                    
                    # 自动查找与样书ID和学校名称匹配、在有效期内的报备
                    promotion_report_id = None
                    sql = """
                    SELECT pr.id, pr.dealer_id, pr.status, pr.promotion_status, pr.expiry_date
                        FROM promotion_reports pr
                    WHERE pr.sample_book_id = %s 
                        AND pr.school_name = %s 
                        AND pr.status = 'approved'
                        AND (pr.expiry_date IS NULL OR pr.expiry_date >= CURDATE())
                    ORDER BY 
                        CASE WHEN pr.promotion_status = 'successful' THEN 0 ELSE 1 END,
                        pr.updated_at DESC
                    LIMIT 1
                    """
                    cursor.execute(sql, (book_id, school_name))
                    valid_report = cursor.fetchone()
                    
                    if valid_report:
                        promotion_report_id = valid_report['id']
                        
                        # 如果有有效报备且发货量大于退货量，更新报备状态为successful
                        if shipped_quantity > returned_quantity:
                            update_sql = """
                                UPDATE promotion_reports
                                SET promotion_status = 'successful',
                                    expiry_date = DATE_ADD(IFNULL(expiry_date, CURDATE()), INTERVAL 1 YEAR)
                                WHERE id = %s
                            """
                            cursor.execute(update_sql, (promotion_report_id,))
                    
                    # 创建订单
                    sql = """
                        INSERT INTO order_items (
                            book_id, school_name, shipped_quantity, returned_quantity,
                            unit_price, promotion_report_id, from_dealer, effective,
                            order_number, remark
                        ) VALUES (%s, %s, %s, %s, %s, %s, 0, 1, %s, %s)
                    """
                    cursor.execute(sql, (
                        book_id, school_name, shipped_quantity, returned_quantity,
                        unit_price, promotion_report_id, order_number, remark
                    ))
                    
                    successful_orders += 1
            except Exception as e:
                failed_orders += 1
                error_messages.append(f"第{index+2}行: {str(e)}")
        
        connection.commit()
        
        # 如果存在重复订单且不是强制覆盖模式，返回特殊响应码
        if duplicate_orders and not force_update:
            return jsonify({
                "code": 3,  # 特殊响应码：存在重复订单
                "message": f"检测到{len(duplicate_orders)}条半年内已存在的相同样书和学校的订单",
                "data": {
                    "success_count": successful_orders,
                    "failed_count": failed_orders,
                    "duplicate_count": len(duplicate_orders)
                },
                "duplicate_orders": duplicate_orders,
                "errors": error_messages[:10]
            })
        
        # 处理其他情况
        if failed_orders > 0:
            return jsonify({
                "code": 2,  # 部分成功
                "message": f"成功导入 {successful_orders} 条订单，失败 {failed_orders} 条",
                "data": {
                    "success_count": successful_orders,
                    "failed_count": failed_orders
                },
                "errors": error_messages[:10]  # 只返回前10条错误消息，避免过多
            })
        else:
            return jsonify({
                "code": 0,
                "message": f"成功导入 {successful_orders} 条订单",
                "data": {
                    "success_count": successful_orders,
                    "failed_count": 0
                }
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"批量导入订单失败: {str(e)}"})
    finally:
        connection.close()

# 下载订单Excel模板
@publisher_bp.route('/download_order_template', methods=['GET'])
def download_order_template():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    try:
        # 创建Excel模板
        import pandas as pd
        import tempfile
        
        # 准备模板数据
        data = {
            'ISBN(必填)': ['示例：9787040495072'],
            '学校名称(必填)': ['示例：某某大学'],
            '发货量(必填)': ['示例：100'],
            '退货量': ['示例：10'],
            '单价(必填)': ['示例：39.80'],
            '备注': ['示例：这是一个备注（可选）']
        }
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 创建临时Excel文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        
        # 写入Excel
        with pd.ExcelWriter(temp_file.name, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='订单批量导入模板', index=False)
            
            # 获取xlsxwriter工作簿和工作表对象
            workbook = writer.book
            worksheet = writer.sheets['订单批量导入模板']
            
            # 设置列宽
            for i, col in enumerate(df.columns):
                # 获取列中最长的内容
                max_len = max(df[col].astype(str).map(len).max(), len(col)) + 2
                # 设置列宽
                worksheet.set_column(i, i, max_len)
            
            # 添加说明
            worksheet.write(2, 0, '说明：')
            worksheet.write(3, 0, '1. 请按照模板格式填写订单信息')
            worksheet.write(4, 0, '2. 标记为"必填"的列必须填写')
            worksheet.write(5, 0, '3. 发货量和退货量必须是整数')
            worksheet.write(6, 0, '4. 单价必须是数值，可以有小数点')
            worksheet.write(7, 0, '5. 备注为可选项，可填写订单的额外说明信息')
            worksheet.write(8, 0, '6. 订单编号由系统自动生成，无需填写')
            worksheet.write(9, 0, '7. 系统会自动关联有效的报备申请，无需手动填写报备ID')
        
        # 返回文件
        return send_file(
            temp_file.name,
            as_attachment=True,
            download_name='订单批量导入模板.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        return jsonify({"code": 1, "message": f"下载模板失败: {str(e)}"})

# 审核经销商提交的订单
@publisher_bp.route('/review_dealer_order', methods=['POST'])
def review_dealer_order():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    
    # 获取表单数据
    data = request.json
    if not data:
        return jsonify({"code": 1, "message": "请提供数据"})
    
    order_id = data.get('order_id')
    action = data.get('action')  # 'approve' or 'reject'
    
    if not order_id or not action or action not in ['approve', 'reject']:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证订单是否存在且属于当前出版社
            sql = """
                SELECT oi.id, oi.book_id, oi.promotion_report_id, oi.shipped_quantity, oi.returned_quantity,
                       sb.publisher_id
                FROM order_items oi
                JOIN sample_books sb ON oi.book_id = sb.id
                WHERE oi.id = %s AND oi.from_dealer = 1 AND oi.effective = 0
            """
            cursor.execute(sql, (order_id,))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或不是待审核状态"})
            
            if order['publisher_id'] != publisher_id:
                return jsonify({"code": 1, "message": "无权操作该订单"})
            
            if action == 'approve':
                # 审核通过
                update_sql = """
                    UPDATE order_items
                    SET effective = 1
                    WHERE id = %s
                """
                cursor.execute(update_sql, (order_id,))
                
                # 如果有关联的报备，更新其推广状态
                if order['promotion_report_id'] and order['shipped_quantity'] > order['returned_quantity']:
                    report_sql = """
                        UPDATE promotion_reports
                        SET promotion_status = 'successful',
                            expiry_date = DATE_ADD(IFNULL(expiry_date, CURDATE()), INTERVAL 1 YEAR)
                        WHERE id = %s
                    """
                    cursor.execute(report_sql, (order['promotion_report_id'],))
                
                message = "订单已审核通过"
            else:
                reject_reason = data.get('reason','')
                # 审核拒绝，设置订单状态为2，并记录拒绝原因
                update_sql = """
                    UPDATE order_items
                    SET effective = 2, reject_reason = %s
                    WHERE id = %s
                """
                cursor.execute(update_sql, (reject_reason, order_id))
                
                message = "订单已拒绝"
            
            connection.commit()
            
            return jsonify({
                "code": 0,
                "message": message
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"审核订单失败: {str(e)}"})
    finally:
        connection.close()

# 批量审核经销商提交的订单
@publisher_bp.route('/batch_review_dealer_orders', methods=['POST'])
def batch_review_dealer_orders():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    
    # 获取表单数据
    data = request.json
    if not data:
        return jsonify({"code": 1, "message": "请提供数据"})
    
    order_ids = data.get('order_ids', [])
    action = data.get('action')  # 'approve' or 'reject'
    
    if not order_ids or not action or action not in ['approve', 'reject']:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证订单是否都属于当前出版社
            placeholders = ', '.join(['%s'] * len(order_ids))
            sql = f"""
                SELECT oi.id, oi.book_id, oi.promotion_report_id, oi.shipped_quantity, oi.returned_quantity,
                       sb.publisher_id
                FROM order_items oi
                JOIN sample_books sb ON oi.book_id = sb.id
                WHERE oi.id IN ({placeholders}) AND oi.from_dealer = 1 AND oi.effective = 0
                AND sb.publisher_id = %s
            """
            params = order_ids + [publisher_id]
            cursor.execute(sql, params)
            orders = cursor.fetchall()
            
            valid_order_ids = [order['id'] for order in orders]
            
            if not valid_order_ids:
                return jsonify({"code": 1, "message": "没有可审核的有效订单"})
            
            if action == 'approve':
                # 批量审核通过
                placeholders = ', '.join(['%s'] * len(valid_order_ids))
                update_sql = f"""
                    UPDATE order_items
                    SET effective = 1
                    WHERE id IN ({placeholders})
                """
                cursor.execute(update_sql, valid_order_ids)
                
                # 更新相关联的报备推广状态
                for order in orders:
                    if order['promotion_report_id'] and order['shipped_quantity'] > order['returned_quantity']:
                        report_sql = """
                            UPDATE promotion_reports
                            SET promotion_status = 'successful',
                                expiry_date = DATE_ADD(IFNULL(expiry_date, CURDATE()), INTERVAL 1 YEAR)
                            WHERE id = %s
                        """
                        cursor.execute(report_sql, (order['promotion_report_id'],))
                
                message = f"已批量通过 {len(valid_order_ids)} 个订单"
            else:
                # 批量拒绝
                reject_reason = data.get('reason', '批量拒绝')
                placeholders = ', '.join(['%s'] * len(valid_order_ids))
                update_sql = f"""
                    UPDATE order_items
                    SET effective = 2, reject_reason = %s
                    WHERE id IN ({placeholders})
                """
                cursor.execute(update_sql, [reject_reason] + valid_order_ids)
                
                message = f"已批量拒绝 {len(valid_order_ids)} 个订单"
            
            connection.commit()
            
            return jsonify({
                "code": 0,
                "message": message
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"批量审核订单失败: {str(e)}"})
    finally:
        connection.close()

# 获取订单详情
@publisher_bp.route('/get_order_detail', methods=['GET'])
def get_order_detail():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    order_id = request.args.get('id')
    
    if not order_id:
        return jsonify({"code": 1, "message": "订单ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT 
                    oi.id, 
                    oi.book_id, 
                    oi.school_name, 
                    oi.shipped_quantity, 
                    oi.returned_quantity, 
                    (oi.shipped_quantity - oi.returned_quantity) as quantity,
                    oi.unit_price, 
                    (oi.shipped_quantity - oi.returned_quantity) * oi.unit_price as total_price,
                    oi.promotion_report_id, 
                    oi.created_at, 
                    oi.from_dealer, 
                    oi.effective,
                    oi.order_number,
                    oi.remark,
                    oi.reject_reason,
                    sb.name as sample_name, 
                    sb.author, 
                    sb.isbn,
                    sb.attachment_link as cover_image,
                    sb.publisher_name,
                    pr.id as report_id, 
                    pr.status as report_status, 
                    pr.promotion_status,
                    pr.updated_at as process_date,
                    pr.expiry_date, 
                    pr.dealer_id,
                    pr.conflict_reason,
                    u.name as dealer_name,
                    u.phone_number as dealer_phone,
                    d.company_id,
                    dc.name as dealer_company,
                    oi.reconciliation_status,
                    oi.payment_status,
                    oi.publisher_quantity,
                    oi.dealer_quantity,
                    oi.publisher_confirm_status,
                    oi.dealer_confirm_status,
                    oi.matched_order_id,
                    oi.last_modified_by
                FROM order_items oi
                JOIN sample_books sb ON oi.book_id = sb.id
                LEFT JOIN promotion_reports pr ON oi.promotion_report_id = pr.id
                LEFT JOIN users u ON pr.dealer_id = u.user_id
                LEFT JOIN dealers d ON u.user_id = d.user_id
                LEFT JOIN dealer_companies dc ON d.company_id = dc.id
                WHERE oi.id = %s AND sb.publisher_id = %s
            """
            cursor.execute(sql, (order_id, publisher_id))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或无权访问"})
            
            # 格式化日期
            if order['created_at']:
                order['created_at'] = order['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if order.get('process_date'):
                order['process_date'] = order['process_date'].strftime('%Y-%m-%d %H:%M:%S')
            if order.get('expiry_date'):
                order['expiry_date'] = order['expiry_date'].strftime('%Y-%m-%d')
            
            # 添加状态映射
            if order['effective'] == 0:
                order['status'] = 'pending'
            elif order['effective'] == 1:
                order['status'] = 'approved'
            elif order['effective'] == 2:
                order['status'] = 'rejected'
            else:
                order['status'] = 'unknown'
            
            return jsonify({
                "code": 0,
                "data": order
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取订单详情失败: {str(e)}"})
    finally:
        connection.close()

# 删除自行上传的订单
@publisher_bp.route('/delete_order', methods=['POST'])
def delete_order():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    order_id = request.form.get('id')
    
    if not order_id:
        return jsonify({"code": 1, "message": "订单ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查订单是否存在且属于该出版社且是自行上传的
            check_sql = """
                SELECT oi.id, oi.promotion_report_id
                FROM order_items oi
                JOIN sample_books sb ON oi.book_id = sb.id
                WHERE oi.id = %s 
                AND sb.publisher_id = %s
                AND oi.from_dealer = 0
                AND oi.effective = 1
            """
            cursor.execute(check_sql, (order_id, publisher_id))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "未找到可删除的订单或订单不属于您或非自行上传订单"})
            
            # 检查是否有关联报备
            if order['promotion_report_id']:
                # 将关联报备的promotion_status更新为pending，并减少有效期1年
                update_report_sql = """
                    UPDATE promotion_reports
                    SET promotion_status = 'pending',
                        expiry_date = CASE 
                            WHEN expiry_date IS NOT NULL THEN DATE_SUB(expiry_date, INTERVAL 1 YEAR)
                            ELSE NULL
                        END
                    WHERE id = %s
                """
                cursor.execute(update_report_sql, (order['promotion_report_id'],))
            
            # 删除订单
            delete_sql = """
                DELETE FROM order_items
                WHERE id = %s
            """
            cursor.execute(delete_sql, (order_id,))
            connection.commit()
            
            return jsonify({
                "code": 0, 
                "message": "订单删除成功"
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"删除订单失败: {str(e)}"})
    finally:
        connection.close()

# 修改撤销订单处理函数
@publisher_bp.route('/revoke_order', methods=['POST'])
def revoke_order():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    order_id = request.form.get('id')
    
    if not order_id:
        return jsonify({"code": 1, "message": "订单ID不能为空"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查订单是否存在且属于该出版社且是经销商上传的
            check_sql = """
                SELECT oi.id, oi.promotion_report_id
                FROM order_items oi
                JOIN sample_books sb ON oi.book_id = sb.id
                WHERE oi.id = %s 
                AND sb.publisher_id = %s
                AND oi.from_dealer = 1
                AND oi.effective = 1
            """
            cursor.execute(check_sql, (order_id, publisher_id))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "未找到可撤销的订单或订单不属于您或非经销商上传订单"})
            
            # 检查是否有关联报备
            if order['promotion_report_id']:
                # 将关联报备的promotion_status更新为pending，并减少有效期1年
                update_report_sql = """
                    UPDATE promotion_reports
                    SET promotion_status = 'pending',
                        expiry_date = CASE 
                            WHEN expiry_date IS NOT NULL THEN DATE_SUB(expiry_date, INTERVAL 1 YEAR)
                            ELSE NULL
                        END
                    WHERE id = %s
                """
                cursor.execute(update_report_sql, (order['promotion_report_id'],))
            
            # 更新订单状态为待审核
            update_order_sql = """
                UPDATE order_items
                SET effective = 0
                WHERE id = %s
            """
            cursor.execute(update_order_sql, (order_id,))
            connection.commit()
            
            return jsonify({
                "code": 0, 
                "message": "订单已撤销处理"
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"撤销处理失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/get_book_recommendations', methods=['GET'])
def get_book_recommendations():
    """获取出版社可见的换版推荐列表"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查出版社是否有换版推荐权限
                cursor.execute("""
                    SELECT pcp.can_recommend_books FROM publisher_company_permissions pcp
                    JOIN users u ON u.publisher_company_id = pcp.company_id
                    WHERE u.user_id = %s
                """, (user_id,))
                permission = cursor.fetchone()
                
                if not permission or not permission['can_recommend_books']:
                    return jsonify({'success': True, 'recommendations': []})
                
                # 获取当前出版社用户的单位ID
                cursor.execute("""
                    SELECT publisher_company_id FROM users WHERE user_id = %s
                """, (user_id,))
                current_publisher = cursor.fetchone()
                current_publisher_company_id = current_publisher['publisher_company_id'] if current_publisher else None

                # 获取出版社可以看到的推荐（外部推荐）
                # 不返回经销商姓名，只返回经销商所在单位
                sql = """
                    SELECT br.id, br.initiator_id, br.initiator_company_id, br.school_id,
                           br.original_book_id, br.original_book_supplier_id, br.replacement_reason,
                           br.replacement_reason_other, br.requirement_no_monopoly, br.requirement_recent_publish,
                           br.requirement_sufficient_stock, br.requirement_national_priority, br.requirement_other,
                           br.recommendation_type, br.status, br.referrer_id, br.created_at, br.updated_at,
                           COALESCE(NULLIF(br.school_level, ''), s.school_level) as school_level,
                           s.name as school_name,
                           sb.name as original_book_name, sb.isbn as original_book_isbn,
                           sb.publisher_name as original_book_publisher, sb.price as original_book_price,
                           sb.publication_date as original_book_publication_date,
                           pc.name as supplier_name, dc.name as initiator_company_name
                    FROM book_recommendations br
                    JOIN schools s ON br.school_id = s.id
                    JOIN sample_books sb ON br.original_book_id = sb.id
                    LEFT JOIN publisher_companies pc ON br.original_book_supplier_id = pc.id
                    LEFT JOIN dealer_companies dc ON br.initiator_company_id = dc.id
                    WHERE br.recommendation_type = 'external' AND br.status IN ('pending', 'in_progress')
                    ORDER BY br.created_at DESC
                """
                cursor.execute(sql)
                recommendations = cursor.fetchall()

                # 格式化日期并添加状态信息
                for recommendation in recommendations:
                    # 格式化日期
                    if recommendation.get('original_book_publication_date'):
                        recommendation['original_book_publication_date'] = recommendation['original_book_publication_date'].strftime('%Y-%m-%d')
                    if recommendation.get('created_at'):
                        recommendation['created_at'] = recommendation['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    if recommendation.get('updated_at'):
                        recommendation['updated_at'] = recommendation['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

                    # 检查当前用户是否为发起人
                    is_initiator = recommendation['initiator_id'] == user_id

                    # 根据权限计算推荐结果数量
                    if is_initiator:
                        # 发起人可以看到所有推荐结果
                        cursor.execute("""
                            SELECT COUNT(*) as count FROM recommendation_results
                            WHERE recommendation_id = %s
                        """, (recommendation['id'],))
                    else:
                        # 出版社用户只能看到自己单位的推荐结果
                        cursor.execute("""
                            SELECT COUNT(*) as count FROM recommendation_results rr
                            JOIN users u ON rr.recommender_id = u.user_id
                            WHERE rr.recommendation_id = %s
                            AND u.publisher_company_id = %s
                        """, (recommendation['id'], current_publisher_company_id))

                    result = cursor.fetchone()
                    recommendation['result_count'] = result['count'] if result else 0

                    # 检查当前出版社是否已推荐
                    cursor.execute("""
                        SELECT COUNT(*) as count FROM recommendation_results
                        WHERE recommendation_id = %s AND recommender_id = %s
                    """, (recommendation['id'], user_id))
                    result = cursor.fetchone()
                    recommendation['has_recommended'] = result['count'] > 0
                
                return jsonify({'success': True, 'recommendations': recommendations})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@publisher_bp.route('/get_recommendation_detail', methods=['GET'])
def get_recommendation_detail():
    """获取换版推荐详情（出版社视角）"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        recommendation_id = request.args.get('id')
        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查出版社是否有换版推荐权限
                cursor.execute("""
                    SELECT pcp.can_recommend_books FROM publisher_company_permissions pcp
                    JOIN users u ON u.publisher_company_id = pcp.company_id
                    WHERE u.user_id = %s
                """, (user_id,))
                permission = cursor.fetchone()
                
                if not permission or not permission['can_recommend_books']:
                    return jsonify({'success': False, 'message': '您没有换版推荐权限'})
                
                # 获取推荐详情，只返回必要的原用教材信息，不暴露供应商等敏感信息
                sql = """
                    SELECT br.id, br.school_id, br.original_book_id, br.status, br.created_at, br.updated_at,
                           br.requirement_sufficient_stock, br.requirement_no_monopoly, br.requirement_recent_publish,
                           br.requirement_national_priority, br.recommendation_type, br.initiator_id, br.initiator_company_id,
                           COALESCE(NULLIF(br.school_level, ''), s.school_level) as school_level,
                           s.name as school_name,
                           sb.name as original_book_name, sb.author as original_book_author,
                           sb.price as original_book_price, sb.publication_date as original_book_publication_date,
                           dc.name as initiator_company_name
                    FROM book_recommendations br
                    JOIN schools s ON br.school_id = s.id
                    JOIN sample_books sb ON br.original_book_id = sb.id
                    LEFT JOIN dealer_companies dc ON br.initiator_company_id = dc.id
                    WHERE br.id = %s
                """
                cursor.execute(sql, (recommendation_id,))
                recommendation = cursor.fetchone()
                
                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在'})
                
                # 检查推荐类型是否为外部推荐
                if recommendation['recommendation_type'] != 'external':
                    return jsonify({'success': False, 'message': '只能查看外部推荐类型的推荐'})
                
                # 获取当前出版社用户的单位ID
                cursor.execute("""
                    SELECT publisher_company_id FROM users WHERE user_id = %s
                """, (user_id,))
                current_publisher = cursor.fetchone()
                current_publisher_company_id = current_publisher['publisher_company_id'] if current_publisher else None

                # 检查当前用户是否为发起人
                is_initiator = recommendation['initiator_id'] == user_id

                # 获取推荐结果（根据权限过滤）
                if is_initiator:
                    # 发起人可以看到所有推荐结果
                    sql = """
                        SELECT rr.*, sb.name as book_name, sb.author, sb.isbn, sb.publisher_name,
                               sb.price, sb.publication_date, u.name as recommender_name,
                               CASE
                                   WHEN u.role = 'publisher' THEN pc.name
                                   WHEN u.role = 'dealer' THEN dc.name
                                   ELSE u.name
                               END as recommender_company
                        FROM recommendation_results rr
                        JOIN sample_books sb ON rr.recommended_book_id = sb.id
                        JOIN users u ON rr.recommender_id = u.user_id
                        LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                        LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                        WHERE rr.recommendation_id = %s
                        ORDER BY rr.created_at ASC
                    """
                    cursor.execute(sql, (recommendation_id,))
                else:
                    # 出版社用户只能看到自己单位的推荐结果
                    sql = """
                        SELECT rr.*, sb.name as book_name, sb.author, sb.isbn, sb.publisher_name,
                               sb.price, sb.publication_date, u.name as recommender_name,
                               CASE
                                   WHEN u.role = 'publisher' THEN pc.name
                                   WHEN u.role = 'dealer' THEN dc.name
                                   ELSE u.name
                               END as recommender_company
                        FROM recommendation_results rr
                        JOIN sample_books sb ON rr.recommended_book_id = sb.id
                        JOIN users u ON rr.recommender_id = u.user_id
                        LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                        LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                        WHERE rr.recommendation_id = %s
                        AND u.publisher_company_id = %s
                        ORDER BY rr.created_at ASC
                    """
                    cursor.execute(sql, (recommendation_id, current_publisher_company_id))
                results = cursor.fetchall()
                
                # 格式化日期字段
                if recommendation.get('original_book_publication_date'):
                    recommendation['original_book_publication_date'] = recommendation['original_book_publication_date'].strftime('%Y-%m-%d')
                if recommendation.get('created_at'):
                    recommendation['created_at'] = recommendation['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                if recommendation.get('updated_at'):
                    recommendation['updated_at'] = recommendation['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                
                for result in results:
                    if result.get('publication_date'):
                        result['publication_date'] = result['publication_date'].strftime('%Y-%m-%d')
                    if result.get('created_at'):
                        result['created_at'] = result['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    if result.get('updated_at'):
                        result['updated_at'] = result['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                
                # 检查当前出版社是否已推荐
                has_recommended = any(result['recommender_id'] == user_id for result in results)
                recommendation['has_recommended'] = has_recommended
                recommendation['can_recommend'] = not has_recommended and recommendation['status'] in ['pending', 'in_progress']
                
                return jsonify({
                    'success': True, 
                    'recommendation': recommendation,
                    'results': results
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@publisher_bp.route('/get_recommendation_schools', methods=['GET'])
def get_recommendation_schools():
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查出版社是否有换版推荐权限
                cursor.execute("""
                    SELECT pcp.* FROM publisher_company_permissions pcp
                    JOIN users u ON u.publisher_company_id = pcp.company_id
                    WHERE u.user_id = %s AND pcp.can_recommend_books = 1
                """, (user_id,))
                permission = cursor.fetchone()
                
                if not permission:
                    return jsonify({'success': True, 'schools': []})
                
                # 获取推荐中的学校列表
                sql = """
                    SELECT DISTINCT s.id, s.name
                    FROM book_recommendations br
                    JOIN schools s ON br.school_id = s.id
                    WHERE
                        (br.receiver_id = %s) OR
                        (
                            br.recommendation_type = 'external' AND
                            br.status IN ('pending', 'in_progress')
                        )
                    ORDER BY s.name
                """
                cursor.execute(sql, (user_id,))
                schools = cursor.fetchall()
                
                return jsonify({'success': True, 'schools': schools})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@publisher_bp.route('/submit_book_recommendation', methods=['POST'])
def submit_book_recommendation():
    """出版社提交换版推荐结果"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.json
        recommendation_id = data.get('recommendation_id')
        recommended_books = data.get('recommended_books', [])

        # 验证必填字段
        if not recommendation_id or not recommended_books:
            return jsonify({'success': False, 'message': '请填写必要的推荐信息'})
        
        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查出版社是否有换版推荐权限
                cursor.execute("""
                    SELECT pcp.can_recommend_books FROM publisher_company_permissions pcp
                    JOIN users u ON u.publisher_company_id = pcp.company_id
                    WHERE u.user_id = %s
                """, (user_id,))
                permission = cursor.fetchone()
                
                if not permission or not permission['can_recommend_books']:
                    return jsonify({'success': False, 'message': '您没有换版推荐权限'})
                
                # 获取推荐详情并验证，包含学校名称
                cursor.execute("""
                    SELECT br.*, s.name as school_name
                    FROM book_recommendations br
                    LEFT JOIN schools s ON br.school_id = s.id
                    WHERE br.id = %s AND br.recommendation_type = 'external'
                    AND br.status IN ('pending', 'in_progress')
                """, (recommendation_id,))
                recommendation = cursor.fetchone()
                
                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在或不可处理'})
                
                success_count = 0
                duplicate_books = []

                # 处理每本推荐的样书
                for book_data in recommended_books:
                    book_id = book_data.get('book_id')
                    stock_quantity = book_data.get('stock_quantity')
                    notes = book_data.get('notes', '')

                    if not book_id:
                        continue

                    # 验证样书是否属于此出版社
                    cursor.execute("""
                        SELECT id, name FROM sample_books WHERE id = %s AND publisher_id = %s
                    """, (book_id, user_id))
                    book = cursor.fetchone()

                    if not book:
                        return jsonify({'success': False, 'message': f'样书ID {book_id} 不属于您的出版社'})

                    # 检查是否需要库存数量
                    if recommendation['requirement_sufficient_stock'] and (not stock_quantity or int(stock_quantity) <= 0):
                        return jsonify({'success': False, 'message': f'样书"{book["name"]}"要求填写库存数量'})
                
                    # 检查是否已经推荐过这本书
                    cursor.execute("""
                        SELECT id FROM recommendation_results
                        WHERE recommendation_id = %s AND recommender_id = %s AND recommended_book_id = %s
                    """, (recommendation_id, user_id, book_id))
                    existing = cursor.fetchone()

                    if existing:
                        duplicate_books.append(book['name'])
                        continue

                    # 检查包销冲突
                    is_monopoly_conflict = False
                    if recommendation['requirement_no_monopoly']:
                        # 检查被推荐样书的出版社类型
                        cursor.execute("""
                            SELECT pc.is_publisher FROM sample_books sb
                            LEFT JOIN users u ON sb.publisher_id = u.user_id
                            LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                            WHERE sb.id = %s
                        """, (book_id,))
                        book_publisher_info = cursor.fetchone()

                        # 如果样书的出版社不是"仅为出版社"，则为包销书，存在冲突
                        if book_publisher_info and not book_publisher_info.get('is_publisher'):
                            is_monopoly_conflict = True

                    # 插入推荐结果
                    cursor.execute("""
                        INSERT INTO recommendation_results
                        (recommendation_id, recommender_id, recommended_book_id, stock_quantity,
                         notes, is_monopoly_conflict, status)
                        VALUES (%s, %s, %s, %s, %s, %s, 'pending')
                    """, (recommendation_id, user_id, book_id, stock_quantity,
                          notes, is_monopoly_conflict))

                    # 自动生成已通过的报备记录
                    # 首先将该样书在该学校的在有效期内的已通过报备设为拒绝
                    # 只处理有效期内的报备：expiry_date IS NULL（永久有效）或 expiry_date >= 当前日期
                    cursor.execute("""
                        UPDATE promotion_reports
                        SET status = 'rejected',
                            reason = '因换版推荐自动生成新的报备记录，原报备已失效',
                            updated_at = NOW()
                        WHERE sample_book_id = %s
                        AND school_name = %s
                        AND status = 'approved'
                        AND (expiry_date IS NULL OR expiry_date >= CURDATE())
                    """, (book_id, recommendation['school_name']))

                    # 生成新的已通过报备记录
                    # 设置有效期为1年
                    cursor.execute("""
                        INSERT INTO promotion_reports
                        (dealer_id, sample_book_id, school_name, status, reason, expiry_date, promotion_status, created_at, updated_at)
                        VALUES (%s, %s, %s, 'approved', '通过换版推荐自动生成', DATE_ADD(CURDATE(), INTERVAL 1 YEAR), 'pending', NOW(), NOW())
                    """, (recommendation['initiator_id'], book_id, recommendation['school_name']))

                    success_count += 1
                
                # 推荐状态保持为 in_progress（推荐中）
                cursor.execute("""
                    UPDATE book_recommendations
                    SET updated_at = NOW()
                    WHERE id = %s
                """, (recommendation_id,))

                connection.commit()

                if duplicate_books:
                    message = f'成功推荐 {success_count} 本样书'
                    if duplicate_books:
                        message += f'，{len(duplicate_books)} 本样书已推荐过：{", ".join(duplicate_books)}'
                    return jsonify({'success': True, 'message': message, 'partial': True})
                else:
                    return jsonify({'success': True, 'message': f'成功推荐 {success_count} 本样书'})
                
        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@publisher_bp.route('/cancel_recommendation', methods=['POST'])
def cancel_recommendation():
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        data = request.json
        recommendation_id = data.get('recommendation_id')
        book_id = data.get('book_id')  # 可选，如果提供则只取消特定的书
        
        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})
        
        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取推荐详情，包含学校名称
                cursor.execute("""
                    SELECT br.*, s.name as school_name
                    FROM book_recommendations br
                    LEFT JOIN schools s ON br.school_id = s.id
                    WHERE br.id = %s
                """, (recommendation_id,))
                recommendation = cursor.fetchone()

                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在'})

                # 验证用户是否有推荐记录
                if book_id:
                    sql = """
                        SELECT * FROM recommendation_results
                        WHERE recommendation_id = %s AND recommender_id = %s AND recommended_book_id = %s
                    """
                    cursor.execute(sql, (recommendation_id, user_id, book_id))
                else:
                    sql = """
                        SELECT * FROM recommendation_results
                        WHERE recommendation_id = %s AND recommender_id = %s
                    """
                    cursor.execute(sql, (recommendation_id, user_id))

                results = cursor.fetchall()

                if not results:
                    return jsonify({'success': False, 'message': '未找到您的推荐记录'})

                # 删除对应的报备记录
                for result in results:
                    if book_id and result['recommended_book_id'] != book_id:
                        continue

                    # 删除该样书在该学校的由换版推荐自动生成的报备记录
                    cursor.execute("""
                        DELETE FROM promotion_reports
                        WHERE sample_book_id = %s
                        AND school_name = %s
                        AND dealer_id = %s
                        AND reason = '通过换版推荐自动生成'
                    """, (result['recommended_book_id'], recommendation['school_name'], recommendation['initiator_id']))

                # 删除推荐结果记录
                if book_id:
                    sql = """
                        DELETE FROM recommendation_results
                        WHERE recommendation_id = %s AND recommender_id = %s AND recommended_book_id = %s
                    """
                    cursor.execute(sql, (recommendation_id, user_id, book_id))
                    message = '成功取消该书籍的推荐，并删除了对应的报备记录'
                else:
                    sql = """
                        DELETE FROM recommendation_results
                        WHERE recommendation_id = %s AND recommender_id = %s
                    """
                    cursor.execute(sql, (recommendation_id, user_id))
                    message = '成功取消所有推荐，并删除了对应的报备记录'
                
                # 检查是否还有其他出版社的推荐
                sql = """
                    SELECT COUNT(*) as count FROM recommendation_results 
                    WHERE recommendation_id = %s
                """
                cursor.execute(sql, (recommendation_id,))
                result = cursor.fetchone()
                
                # 推荐状态保持为 in_progress（推荐中），不需要改回待处理状态
                
                connection.commit()
                return jsonify({'success': True, 'message': message})
            
        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@publisher_bp.route('/edit_book_recommendation', methods=['POST'])
def edit_book_recommendation():
    """编辑出版社换版推荐结果"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.json
        recommendation_id = data.get('recommendation_id')
        recommended_books = data.get('recommended_books', [])

        # 验证必填字段
        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})

        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查出版社是否有换版推荐权限
                cursor.execute("""
                    SELECT pcp.can_recommend_books FROM publisher_company_permissions pcp
                    JOIN users u ON u.publisher_company_id = pcp.company_id
                    WHERE u.user_id = %s
                """, (user_id,))
                permission = cursor.fetchone()

                if not permission or not permission['can_recommend_books']:
                    return jsonify({'success': False, 'message': '您没有换版推荐权限'})

                # 获取推荐详情并验证，包含学校名称
                cursor.execute("""
                    SELECT br.*, s.name as school_name
                    FROM book_recommendations br
                    LEFT JOIN schools s ON br.school_id = s.id
                    WHERE br.id = %s AND br.recommendation_type = 'external'
                    AND br.status IN ('pending', 'in_progress')
                """, (recommendation_id,))
                recommendation = cursor.fetchone()

                if not recommendation:
                    return jsonify({'success': False, 'message': '推荐不存在或不可处理'})

                # 获取当前用户已有的推荐记录
                cursor.execute("""
                    SELECT * FROM recommendation_results
                    WHERE recommendation_id = %s AND recommender_id = %s
                """, (recommendation_id, user_id))
                existing_results = cursor.fetchall()

                # 创建现有推荐的映射
                existing_books = {result['recommended_book_id']: result for result in existing_results}
                new_book_ids = {book_data.get('book_id') for book_data in recommended_books if book_data.get('book_id')}

                success_count = 0
                updated_count = 0
                deleted_count = 0

                # 处理删除的推荐（存在于数据库但不在新提交的列表中）
                for book_id, existing_result in existing_books.items():
                    if book_id not in new_book_ids:
                        # 删除对应的报备记录
                        cursor.execute("""
                            DELETE FROM promotion_reports
                            WHERE sample_book_id = %s
                            AND school_name = %s
                            AND dealer_id = %s
                            AND reason = '通过换版推荐自动生成'
                        """, (book_id, recommendation['school_name'], recommendation['initiator_id']))

                        # 删除推荐结果记录
                        cursor.execute("""
                            DELETE FROM recommendation_results
                            WHERE id = %s
                        """, (existing_result['id'],))
                        deleted_count += 1

                # 处理新增和更新的推荐
                for book_data in recommended_books:
                    book_id = book_data.get('book_id')
                    stock_quantity = book_data.get('stock_quantity')
                    notes = book_data.get('notes', '')

                    if not book_id:
                        continue

                    # 验证样书是否属于此出版社
                    cursor.execute("""
                        SELECT id, name FROM sample_books WHERE id = %s AND publisher_id = %s
                    """, (book_id, user_id))
                    book = cursor.fetchone()

                    if not book:
                        return jsonify({'success': False, 'message': f'样书ID {book_id} 不属于您的出版社'})

                    # 检查是否需要库存数量
                    if recommendation['requirement_sufficient_stock'] and (not stock_quantity or int(stock_quantity) <= 0):
                        return jsonify({'success': False, 'message': f'样书"{book["name"]}"要求填写库存数量'})

                    # 检查包销冲突
                    is_monopoly_conflict = False
                    if recommendation['requirement_no_monopoly']:
                        cursor.execute("""
                            SELECT pc.is_publisher FROM sample_books sb
                            LEFT JOIN users u ON sb.publisher_id = u.user_id
                            LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                            WHERE sb.id = %s
                        """, (book_id,))
                        book_publisher_info = cursor.fetchone()

                        if book_publisher_info and not book_publisher_info.get('is_publisher'):
                            is_monopoly_conflict = True

                    if book_id in existing_books:
                        # 更新现有推荐
                        cursor.execute("""
                            UPDATE recommendation_results
                            SET stock_quantity = %s, notes = %s, is_monopoly_conflict = %s, updated_at = NOW()
                            WHERE id = %s
                        """, (stock_quantity, notes, is_monopoly_conflict, existing_books[book_id]['id']))
                        updated_count += 1
                    else:
                        # 新增推荐
                        cursor.execute("""
                            INSERT INTO recommendation_results
                            (recommendation_id, recommender_id, recommended_book_id, stock_quantity,
                             notes, is_monopoly_conflict, status)
                            VALUES (%s, %s, %s, %s, %s, %s, 'pending')
                        """, (recommendation_id, user_id, book_id, stock_quantity,
                              notes, is_monopoly_conflict))

                        # 自动生成已通过的报备记录
                        cursor.execute("""
                            UPDATE promotion_reports
                            SET status = 'rejected',
                                reason = '因换版推荐自动生成新的报备记录，原报备已失效',
                                updated_at = NOW()
                            WHERE sample_book_id = %s
                            AND school_name = %s
                            AND status = 'approved'
                            AND (expiry_date IS NULL OR expiry_date >= CURDATE())
                        """, (book_id, recommendation['school_name']))

                        cursor.execute("""
                            INSERT INTO promotion_reports
                            (dealer_id, sample_book_id, school_name, status, reason, expiry_date, promotion_status, created_at, updated_at)
                            VALUES (%s, %s, %s, 'approved', '通过换版推荐自动生成', DATE_ADD(CURDATE(), INTERVAL 1 YEAR), 'pending', NOW(), NOW())
                        """, (recommendation['initiator_id'], book_id, recommendation['school_name']))

                        success_count += 1

                # 更新推荐时间
                cursor.execute("""
                    UPDATE book_recommendations
                    SET updated_at = NOW()
                    WHERE id = %s
                """, (recommendation_id,))

                connection.commit()

                # 构建返回消息
                message_parts = []
                if success_count > 0:
                    message_parts.append(f'新增推荐 {success_count} 本样书')
                if updated_count > 0:
                    message_parts.append(f'更新推荐 {updated_count} 本样书')
                if deleted_count > 0:
                    message_parts.append(f'删除推荐 {deleted_count} 本样书')

                if message_parts:
                    message = '推荐编辑成功：' + '，'.join(message_parts)
                    if success_count > 0:
                        message += f'，已自动生成 {success_count} 条报备记录'
                else:
                    message = '推荐编辑成功'

                return jsonify({'success': True, 'message': message})

        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@publisher_bp.route('/get_recommended_books', methods=['GET'])
def get_recommended_books():
    """获取出版社已推荐的样书列表"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})

        recommendation_id = request.args.get('recommendation_id')
        if not recommendation_id:
            return jsonify({'success': False, 'message': '缺少推荐ID'})

        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取当前用户对该推荐的已推荐样书
                cursor.execute("""
                    SELECT rr.*, sb.name as book_name, sb.author, sb.price, sb.isbn,
                           sb.publisher_name, sb.publication_date
                    FROM recommendation_results rr
                    LEFT JOIN sample_books sb ON rr.recommended_book_id = sb.id
                    WHERE rr.recommendation_id = %s AND rr.recommender_id = %s
                    ORDER BY rr.created_at DESC
                """, (recommendation_id, user_id))

                results = cursor.fetchall()

                # 格式化数据
                recommended_books = []
                for result in results:
                    recommended_books.append({
                        'id': result['id'],
                        'book_id': result['recommended_book_id'],
                        'book_name': result['book_name'],
                        'author': result['author'],
                        'price': float(result['price']) if result['price'] else 0,
                        'isbn': result['isbn'],
                        'publisher_name': result['publisher_name'],
                        'publication_date': result['publication_date'].strftime('%Y-%m-%d') if result['publication_date'] else '',
                        'stock_quantity': result['stock_quantity'],
                        'notes': result['notes'],
                        'status': result['status'],
                        'is_monopoly_conflict': bool(result['is_monopoly_conflict']),
                        'created_at': result['created_at'].strftime('%Y-%m-%d %H:%M:%S') if result['created_at'] else ''
                    })

                return jsonify({
                    'success': True,
                    'recommended_books': recommended_books
                })

        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@publisher_bp.route('/check_recommendation_success', methods=['POST'])
def check_recommendation_success():
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '请先登录'})
        
        data = request.json
        order_id = data.get('order_id')
        
        if not order_id:
            return jsonify({'success': False, 'message': '缺少订单ID'})
        
        connection = get_db_connection()
        try:
            connection.begin()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取订单项
                sql = """
                    SELECT oi.* 
                    FROM order_items oi
                    WHERE oi.order_number = %s AND oi.effective = 1
                """
                cursor.execute(sql, (order_id,))
                order_items = cursor.fetchall()
                
                if not order_items:
                    return jsonify({'success': True, 'message': '订单中没有有效的订单项'})
                
                # 检查每个订单项是否匹配推荐
                processed_recommendations = []
                
                for item in order_items:
                    book_id = item['book_id']
                    school_name = item['school_name']
                    
                    if not school_name:
                        continue
                    
                    # 查找匹配的学校
                    cursor.execute("SELECT id FROM schools WHERE name = %s", (school_name,))
                    school = cursor.fetchone()
                    
                    if not school:
                        continue
                    
                    school_id = school['id']
                    
                    # 查找进行中的推荐
                    sql = """
                        SELECT DISTINCT br.id 
                        FROM book_recommendations br
                        JOIN recommendation_results rr ON br.id = rr.recommendation_id
                        WHERE br.school_id = %s AND br.status IN ('pending', 'in_progress')
                        AND rr.recommended_book_id = %s AND rr.status = 'pending'
                    """
                    cursor.execute(sql, (school_id, book_id))
                    recommendations = cursor.fetchall()
                    
                    for rec in recommendations:
                        if rec['id'] not in processed_recommendations:
                            processed_recommendations.append(rec['id'])
                        
                        # 更新推荐结果状态
                        sql = """
                            UPDATE recommendation_results 
                            SET status = 'successful', updated_at = NOW() 
                            WHERE recommendation_id = %s AND recommended_book_id = %s
                        """
                        cursor.execute(sql, (rec['id'], book_id))
                        
                        # 检查是否所有推荐结果都已成功
                        sql = """
                            SELECT COUNT(*) as total, SUM(CASE WHEN status = 'successful' THEN 1 ELSE 0 END) as successful
                            FROM recommendation_results
                            WHERE recommendation_id = %s
                        """
                        cursor.execute(sql, (rec['id'],))
                        stats = cursor.fetchone()
                        
                        # 如果至少有一个推荐成功，则标记推荐为完成
                        if stats['successful'] > 0:
                            sql = "UPDATE book_recommendations SET status = 'completed', updated_at = NOW() WHERE id = %s"
                            cursor.execute(sql, (rec['id'],))
                
                connection.commit()
                return jsonify({'success': True, 'processed_count': len(processed_recommendations)})
        except Exception as e:
            connection.rollback()
            return jsonify({'success': False, 'message': str(e)})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@publisher_bp.route('/confirm_dealer_quantity', methods=['POST'])
def confirm_dealer_quantity():
    """
    出版社确认或否定经销商的数量
    请求数据:
        order_id: 订单ID
        action: 操作类型('confirm' 或 'reject')
        confirm_quantity: 确认的数量 (如果是确认操作)
    返回:
        处理结果
    """
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({"code": 1, "message": "请提供请求数据"})
    
    order_id = data.get('order_id')
    action = data.get('action')  # 'confirm' 或 'reject'
    confirm_quantity = data.get('confirm_quantity')  # 只有当action为confirm时使用
    
    if not order_id or not action:
        return jsonify({"code": 1, "message": "参数不完整"})
    
    if action not in ['confirm', 'reject']:
        return jsonify({"code": 1, "message": "无效的操作类型"})
    
    if action == 'confirm' and confirm_quantity is None:
        return jsonify({"code": 1, "message": "确认操作需要提供数量"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取订单信息
            sql = """
                SELECT oi.*, matched.id as matched_order_id, matched.shipped_quantity as dealer_shipped_quantity
                FROM order_items oi
                JOIN order_items matched ON oi.matched_order_id = matched.id
                WHERE oi.id = %s AND oi.from_dealer = 0
            """
            cursor.execute(sql, (order_id,))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或不是出版社订单"})
            
            # 验证样书是否属于当前出版社
            book_sql = "SELECT publisher_id FROM sample_books WHERE id = %s"
            cursor.execute(book_sql, (order['book_id'],))
            book = cursor.fetchone()
            
            if not book or book['publisher_id'] != publisher_id:
                return jsonify({"code": 1, "message": "无权操作该订单"})
            
            dealer_order_id = order['matched_order_id']
            
            if action == 'confirm':
                # 确认经销商数量
                try:
                    confirm_quantity = int(confirm_quantity)
                    if confirm_quantity < 0:
                        return jsonify({"code": 1, "message": "确认数量不能为负数"})
                except:
                    return jsonify({"code": 1, "message": "确认数量必须是整数"})
                
                # 更新出版社订单
                update_publisher_sql = """
                    UPDATE order_items
                    SET shipped_quantity = %s, dealer_quantity = %s, 
                        publisher_confirm_status = 'confirmed',
                        reconciliation_status = 'pending_payment',
                        last_modified_by = 'publisher',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_publisher_sql, (confirm_quantity, confirm_quantity, order_id))
                
                # 更新经销商订单
                update_dealer_sql = """
                    UPDATE order_items
                    SET shipped_quantity = %s, publisher_quantity = %s,
                        reconciliation_status = 'pending_payment',
                        last_modified_by = 'publisher',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_dealer_sql, (confirm_quantity, confirm_quantity, dealer_order_id))
                
                # 更新订单匹配记录
                update_match_sql = """
                    UPDATE order_matches
                    SET reconciliation_status = 'pending_payment'
                    WHERE publisher_order_id = %s AND dealer_order_id = %s
                """
                cursor.execute(update_match_sql, (order_id, dealer_order_id))
                
                # 添加对账历史记录
                history_sql = """
                    INSERT INTO order_reconciliation_history
                    (order_id, user_id, user_role, action_type, old_quantity, new_quantity, remark)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(history_sql, (
                    order_id,
                    publisher_id,
                    'publisher',
                    'confirm',
                    order['shipped_quantity'],
                    confirm_quantity,
                    '出版社确认接受经销商数量'
                ))
                
                message = "已确认经销商数量并更新为待支付状态"
            else:
                # 否定经销商数量
                update_publisher_sql = """
                    UPDATE order_items
                    SET publisher_confirm_status = 'rejected',
                        last_modified_by = 'publisher',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_publisher_sql, (order_id,))
                
                # 更新经销商订单
                update_dealer_sql = """
                    UPDATE order_items
                    SET publisher_confirm_status = 'rejected',
                        last_modified_by = 'publisher',
                        updated_at = NOW()
                    WHERE id = %s
                """
                cursor.execute(update_dealer_sql, (dealer_order_id,))
                
                # 添加对账历史记录
                history_sql = """
                    INSERT INTO order_reconciliation_history
                    (order_id, user_id, user_role, action_type, remark)
                    VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(history_sql, (
                    order_id,
                    publisher_id,
                    'publisher',
                    'reject',
                    '出版社否定经销商数量'
                ))
                
                message = "已否定经销商数量，等待协商"
            
            connection.commit()
            return jsonify({"code": 0, "message": message})
            
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"处理失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/modify_order_quantity', methods=['POST'])
def modify_order_quantity():
    """
    出版社修改订单数量
    请求数据:
        order_id: 订单ID
        quantity: 新数量
    返回:
        处理结果
    """
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({"code": 1, "message": "请提供请求数据"})
    
    order_id = data.get('order_id')
    quantity = data.get('quantity')
    
    if not order_id or quantity is None:
        return jsonify({"code": 1, "message": "参数不完整"})
    
    try:
        quantity = int(quantity)
        if quantity < 0:
            return jsonify({"code": 1, "message": "数量不能为负数"})
    except:
        return jsonify({"code": 1, "message": "数量必须是整数"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取订单信息
            sql = """
                SELECT oi.*, sb.publisher_id as book_publisher_id,
                       matched.id as matched_order_id, matched.shipped_quantity as dealer_shipped_quantity
                FROM order_items oi
                JOIN sample_books sb ON oi.book_id = sb.id
                LEFT JOIN order_items matched ON oi.matched_order_id = matched.id
                WHERE oi.id = %s AND oi.from_dealer = 0
            """
            cursor.execute(sql, (order_id,))
            order = cursor.fetchone()
            
            if not order:
                return jsonify({"code": 1, "message": "订单不存在或不是出版社订单"})
            
            # 验证样书是否属于当前出版社
            if order['book_publisher_id'] != publisher_id:
                return jsonify({"code": 1, "message": "无权操作该订单"})
            
            # 获取匹配的经销商订单ID
            dealer_order_id = order['matched_order_id']
            if not dealer_order_id:
                return jsonify({"code": 1, "message": "没有找到匹配的经销商订单"})
            
            old_quantity = order['shipped_quantity']
            dealer_quantity = order.get('dealer_shipped_quantity')
            
            # 判断是否与经销商数量一致
            is_quantity_matched = (dealer_quantity is not None and quantity == dealer_quantity)
            
            # 设置新的状态
            if is_quantity_matched:
                # 数量一致，自动进入待支付状态
                new_reconciliation_status = 'pending_payment'
                publisher_confirm_status = 'confirmed'
                dealer_confirm_status = 'confirmed'
            else:
                # 数量不一致，回到预结算状态
                new_reconciliation_status = 'pre_settlement'
                publisher_confirm_status = 'confirmed'
                dealer_confirm_status = 'unconfirmed'
            
            # 更新出版社订单数量
            update_sql = """
                UPDATE order_items
                SET shipped_quantity = %s,
                    publisher_quantity = %s,
                    publisher_confirm_status = %s,
                    dealer_confirm_status = %s, 
                    reconciliation_status = %s,
                    last_modified_by = 'publisher',
                    updated_at = NOW()
                WHERE id = %s
            """
            cursor.execute(update_sql, (
                quantity,
                quantity,
                publisher_confirm_status,
                dealer_confirm_status,
                new_reconciliation_status,
                order_id
            ))
            
            # 更新经销商订单数量
            update_dealer_sql = """
                UPDATE order_items
                SET publisher_quantity = %s,
                    publisher_confirm_status = %s,
                    dealer_confirm_status = %s,
                    reconciliation_status = %s,
                    last_modified_by = 'publisher',
                    updated_at = NOW()
                WHERE id = %s
            """
            cursor.execute(update_dealer_sql, (
                quantity,
                publisher_confirm_status,
                dealer_confirm_status,
                new_reconciliation_status,
                dealer_order_id
            ))
            
            # 更新订单匹配记录
            update_match_sql = """
                UPDATE order_matches
                SET reconciliation_status = %s
                WHERE publisher_order_id = %s AND dealer_order_id = %s
            """
            cursor.execute(update_match_sql, (new_reconciliation_status, order_id, dealer_order_id))
            
            # 添加对账历史记录
            history_sql = """
                INSERT INTO order_reconciliation_history
                (order_id, user_id, user_role, action_type, old_quantity, new_quantity, remark)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            
            if is_quantity_matched:
                remark = f'出版社修改订单数量至{quantity}，与经销商数量一致，自动进入待支付状态'
            else:
                remark = f'出版社修改订单数量至{quantity}'
            
            cursor.execute(history_sql, (
                order_id,
                publisher_id,
                'publisher',
                'modify',
                old_quantity,
                quantity,
                remark
            ))
            
            connection.commit()
            
            if is_quantity_matched:
                return jsonify({"code": 0, "message": "订单数量已修改，与经销商数量一致，已自动进入待支付状态"})
            else:
                return jsonify({"code": 0, "message": "订单数量修改成功"})
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"修改订单数量失败: {str(e)}"})
    finally:
        connection.close()

# 确认对账
@publisher_bp.route('/confirm_reconciliation/<int:order_id>', methods=['POST'])
def confirm_reconciliation(order_id):
    """
    出版社确认对账
    请求参数:
        order_id: 订单ID (URL路径参数)
    返回:
        处理结果
    """
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})

    publisher_id = session['user_id']

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取订单信息
            sql = """
                SELECT oi.*, sb.publisher_id as book_publisher_id,
                       matched.shipped_quantity as dealer_shipped_quantity
                FROM order_items oi
                JOIN sample_books sb ON oi.book_id = sb.id
                LEFT JOIN order_items matched ON oi.matched_order_id = matched.id
                WHERE oi.id = %s AND oi.from_dealer = 0
            """
            cursor.execute(sql, (order_id,))
            order = cursor.fetchone()

            if not order:
                return jsonify({"code": 1, "message": "订单不存在或不是出版社订单"})

            # 验证样书是否属于当前出版社
            if order['book_publisher_id'] != publisher_id:
                return jsonify({"code": 1, "message": "无权操作该订单"})

            # 检查订单状态
            if order['reconciliation_status'] != 'pre_settlement':
                return jsonify({"code": 1, "message": "订单不在预结算状态，无法确认对账"})

            # 检查是否有匹配的经销商订单
            if not order['matched_order_id']:
                # 如果没有匹配订单，但有经销商数量，尝试查找对应的经销商订单
                if order.get('dealer_quantity'):
                    # 尝试查找匹配的经销商订单
                    find_dealer_sql = """
                        SELECT id FROM order_items
                        WHERE book_id = %s AND school_name = %s AND from_dealer = 1
                        AND effective = 1 AND shipped_quantity = %s
                        ORDER BY created_at DESC LIMIT 1
                    """
                    cursor.execute(find_dealer_sql, (order['book_id'], order['school_name'], order['dealer_quantity']))
                    dealer_order = cursor.fetchone()

                    if dealer_order:
                        # 找到了对应的经销商订单，修复匹配关系
                        dealer_order_id = dealer_order['id']

                        # 更新出版社订单的匹配ID
                        update_publisher_sql = """
                            UPDATE order_items SET matched_order_id = %s WHERE id = %s
                        """
                        cursor.execute(update_publisher_sql, (dealer_order_id, order_id))

                        # 更新经销商订单的匹配ID
                        update_dealer_sql = """
                            UPDATE order_items SET matched_order_id = %s WHERE id = %s
                        """
                        cursor.execute(update_dealer_sql, (order_id, dealer_order_id))

                        # 创建或更新匹配记录
                        match_sql = """
                            INSERT INTO order_matches (publisher_order_id, dealer_order_id, reconciliation_status)
                            VALUES (%s, %s, 'pre_settlement')
                            ON DUPLICATE KEY UPDATE reconciliation_status = 'pre_settlement'
                        """
                        cursor.execute(match_sql, (order_id, dealer_order_id))

                        # 重新获取订单信息
                        order['matched_order_id'] = dealer_order_id
                    else:
                        return jsonify({"code": 1, "message": "订单数据异常：检测到经销商数量但未找到匹配的经销商订单，请联系管理员处理"})
                else:
                    return jsonify({"code": 1, "message": "此订单暂未匹配到经销商订单，无法进行对账操作"})

            dealer_order_id = order['matched_order_id']
            publisher_quantity = order['shipped_quantity']
            dealer_quantity = order['dealer_quantity']

            # 判断数量是否一致
            if publisher_quantity == dealer_quantity:
                # 数量一致，进入待支付状态
                new_status = 'pending_payment'
                confirm_status = 'confirmed'
                message = "对账确认成功，数量一致，已进入待支付状态"
            else:
                # 数量不一致，但出版社确认对账，仍进入待支付状态
                new_status = 'pending_payment'
                confirm_status = 'confirmed'
                message = f"对账确认成功，已进入待支付状态（出版社数量：{publisher_quantity}，经销商数量：{dealer_quantity}）"

            # 更新出版社订单状态
            update_publisher_sql = """
                UPDATE order_items
                SET reconciliation_status = %s,
                    publisher_confirm_status = %s,
                    dealer_confirm_status = %s,
                    last_modified_by = 'publisher',
                    updated_at = NOW()
                WHERE id = %s
            """
            cursor.execute(update_publisher_sql, (new_status, confirm_status, confirm_status, order_id))

            # 更新经销商订单状态
            update_dealer_sql = """
                UPDATE order_items
                SET reconciliation_status = %s,
                    publisher_confirm_status = %s,
                    dealer_confirm_status = %s,
                    last_modified_by = 'publisher',
                    updated_at = NOW()
                WHERE id = %s
            """
            cursor.execute(update_dealer_sql, (new_status, confirm_status, confirm_status, dealer_order_id))

            # 更新订单匹配记录
            update_match_sql = """
                UPDATE order_matches
                SET reconciliation_status = %s
                WHERE publisher_order_id = %s AND dealer_order_id = %s
            """
            cursor.execute(update_match_sql, (new_status, order_id, dealer_order_id))

            # 添加对账历史记录
            history_sql = """
                INSERT INTO order_reconciliation_history
                (order_id, user_id, user_role, action_type, remark)
                VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(history_sql, (
                order_id,
                publisher_id,
                'publisher',
                'confirm_reconciliation',
                f'出版社确认对账，订单进入待支付状态'
            ))

            connection.commit()

            return jsonify({
                "code": 0,
                "message": message,
                "reconciliation_status": new_status
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"确认对账失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/get_order_reconciliation_history', methods=['GET'])
def get_order_reconciliation_history():
    """
    获取订单对账历史
    请求参数:
        order_id: 订单ID
    返回:
        对账历史记录
    """
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    order_id = request.args.get('order_id')
    
    if not order_id:
        return jsonify({"code": 1, "message": "订单ID不能为空"})
    
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 验证订单是否属于当前出版社
            sql = """
                SELECT oi.id, sb.publisher_id
                FROM order_items oi
                JOIN sample_books sb ON oi.book_id = sb.id
                WHERE oi.id = %s
            """
            cursor.execute(sql, (order_id,))
            order = cursor.fetchone()
            
            if not order or order['publisher_id'] != publisher_id:
                return jsonify({"code": 1, "message": "订单不存在或无权访问"})
            
            # 查询订单本身和匹配订单的对账历史
            sql = """
                SELECT orh.id, orh.order_id, orh.user_id, orh.user_role, orh.action_type,
                       orh.old_quantity, orh.new_quantity, orh.remark, orh.created_at,
                       CASE
                           WHEN orh.user_role = 'publisher' THEN pc.name
                           WHEN orh.user_role = 'dealer' THEN dc.name
                           ELSE u.name
                       END as user_name
                FROM order_reconciliation_history orh
                JOIN users u ON orh.user_id = u.user_id
                LEFT JOIN publisher_companies pc ON orh.user_role = 'publisher' AND u.publisher_company_id = pc.id
                LEFT JOIN dealers d ON orh.user_role = 'dealer' AND u.user_id = d.user_id
                LEFT JOIN dealer_companies dc ON d.company_id = dc.id
                WHERE orh.order_id = %s OR orh.order_id IN (
                    SELECT matched_order_id FROM order_items WHERE id = %s
                )
                ORDER BY orh.created_at DESC
            """
            cursor.execute(sql, (order_id, order_id))
            history = cursor.fetchall()
            
            # 格式化时间
            for record in history:
                if record['created_at']:
                    record['created_at'] = record['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            
            return jsonify({"code": 0, "data": history})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取对账历史失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/filter_samples', methods=['GET'])
def filter_samples():
    """高级筛选样书"""
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    
    # 获取筛选参数
    search = request.args.get('search', '')
    levels = request.args.get('levels', '[]')
    types = request.args.get('types', '[]')
    ranks = request.args.get('ranks', '[]')
    national_levels = request.args.get('national_levels', '[]')
    provincial_levels = request.args.get('provincial_levels', '[]')
    publishers = request.args.get('publishers', '[]')
    features = request.args.get('features', '[]')
    publication_date_filter = request.args.get('publication_date_filter', '')
    publication_start_date = request.args.get('publication_start_date', '')
    publication_end_date = request.args.get('publication_end_date', '')
    
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 12))
    offset = (page - 1) * limit
    
    # 解析JSON参数
    import json
    try:
        levels = json.loads(levels) if levels else []
        types = json.loads(types) if types else []
        ranks = json.loads(ranks) if ranks else []
        national_levels = json.loads(national_levels) if national_levels else []
        provincial_levels = json.loads(provincial_levels) if provincial_levels else []
        publishers = json.loads(publishers) if publishers else []
        features = json.loads(features) if features else []
    except json.JSONDecodeError:
        return jsonify({"code": 1, "message": "筛选参数格式错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建基础SQL查询
            base_where = "sb.publisher_id = %s"
            sql = f"""
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info, 
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.discount_info, sb.attachment_link,
                       sb.color_system, sb.publisher_name, sb.shipping_discount, 
                       sb.settlement_discount, sb.promotion_rate,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name
                FROM sample_books sb
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE {base_where}
            """
            
            params = [publisher_id]
            
            # 添加搜索条件
            if search:
                sql += """ AND (
                    sb.name LIKE %s OR 
                    sb.author LIKE %s OR 
                    sb.isbn LIKE %s OR 
                    sb.publisher_name LIKE %s
                )"""
                params.extend([f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'])
            
            # 添加学校层次筛选
            if levels and len(levels) > 0:
                placeholders = ', '.join(['%s'] * len(levels))
                sql += f" AND sb.level IN ({placeholders})"
                params.extend(levels)
            
            # 添加图书类型筛选
            if types and len(types) > 0:
                placeholders = ', '.join(['%s'] * len(types))
                sql += f" AND (sb.book_type IN ({placeholders}))"
                params.extend(types)
            
            # 添加规划级别筛选
            if ranks and len(ranks) > 0:
                rank_conditions = []
                for rank in ranks:
                    if rank == '国家规划':
                        rank_conditions.append("sb.national_regulation = 1")
                    elif rank == '省级规划':
                        rank_conditions.append("sb.provincial_regulation = 1")
                    elif rank == '普通教材':
                        rank_conditions.append("(sb.national_regulation = 0 AND sb.provincial_regulation = 0)")
                
                if rank_conditions:
                    sql += " AND (" + " OR ".join(rank_conditions) + ")"
            
            # 添加国家规划级别筛选
            if national_levels and len(national_levels) > 0:
                placeholders = ', '.join(['%s'] * len(national_levels))
                sql += f" AND (sb.national_regulation_level_id IN ({placeholders}))"
                params.extend(national_levels)
            
            # 添加省级规划级别筛选
            if provincial_levels and len(provincial_levels) > 0:
                placeholders = ', '.join(['%s'] * len(provincial_levels))
                sql += f" AND (sb.provincial_regulation_level_id IN ({placeholders}))"
                params.extend(provincial_levels)
            
            # 添加出版社筛选
            if publishers and len(publishers) > 0:
                placeholders = ', '.join(['%s'] * len(publishers))
                sql += f" AND (sb.publisher_name IN ({placeholders}))"
                params.extend(publishers)
            
            # 添加特色筛选
            if features and len(features) > 0:
                placeholders = ', '.join(['%s'] * len(features))
                sql += f" AND (sb.id IN (SELECT sbf.sample_id FROM sample_book_features sbf WHERE sbf.feature_id IN ({placeholders})))"
                params.extend(features)

            # 添加出版日期筛选
            if publication_date_filter:
                if publication_date_filter == 'recent_three_years':
                    # 近三年：从三年前的今天到今天
                    sql += " AND sb.publication_date >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)"
                elif publication_date_filter == 'custom' and publication_start_date and publication_end_date:
                    # 自定义日期范围
                    sql += " AND sb.publication_date >= %s AND sb.publication_date <= %s"
                    params.extend([publication_start_date, publication_end_date])

            # 添加排序和分页
            sql += " ORDER BY sb.id DESC LIMIT %s OFFSET %s"
            params.extend([limit, offset])
            
            samples = get_samples_with_features(cursor, sql, params)
            
            # 查询总数
            count_sql = """
                SELECT COUNT(*) as total
                FROM sample_books sb
                WHERE sb.publisher_id = %s
            """
            count_params = [publisher_id]
            
            # 重新构建计数查询的条件
            if search:
                count_sql += """ AND (
                    sb.name LIKE %s OR 
                    sb.author LIKE %s OR 
                    sb.isbn LIKE %s OR 
                    sb.publisher_name LIKE %s
                )"""
                count_params.extend([f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'])
            
            if levels and len(levels) > 0:
                placeholders = ', '.join(['%s'] * len(levels))
                count_sql += f" AND (sb.level IN ({placeholders}))"
                count_params.extend(levels)
            
            if types and len(types) > 0:
                placeholders = ', '.join(['%s'] * len(types))
                count_sql += f" AND (sb.book_type IN ({placeholders}))"
                count_params.extend(types)
            
            if ranks and len(ranks) > 0:
                rank_conditions = []
                for rank in ranks:
                    if rank == '国家规划':
                        rank_conditions.append("sb.national_regulation = 1")
                    elif rank == '省级规划':
                        rank_conditions.append("sb.provincial_regulation = 1")
                    elif rank == '普通教材':
                        rank_conditions.append("(sb.national_regulation = 0 AND sb.provincial_regulation = 0)")
                
                if rank_conditions:
                    count_sql += " AND (" + " OR ".join(rank_conditions) + ")"
            
            if national_levels and len(national_levels) > 0:
                placeholders = ', '.join(['%s'] * len(national_levels))
                count_sql += f" AND (sb.national_regulation_level_id IN ({placeholders}))"
                count_params.extend(national_levels)
            
            if provincial_levels and len(provincial_levels) > 0:
                placeholders = ', '.join(['%s'] * len(provincial_levels))
                count_sql += f" AND (sb.provincial_regulation_level_id IN ({placeholders}))"
                count_params.extend(provincial_levels)
            
            if publishers and len(publishers) > 0:
                placeholders = ', '.join(['%s'] * len(publishers))
                count_sql += f" AND (sb.publisher_name IN ({placeholders}))"
                count_params.extend(publishers)
            
            if features and len(features) > 0:
                placeholders = ', '.join(['%s'] * len(features))
                count_sql += f" AND (sb.id IN (SELECT sbf.sample_id FROM sample_book_features sbf WHERE sbf.feature_id IN ({placeholders})))"
                count_params.extend(features)

            # 添加出版日期筛选到计数查询
            if publication_date_filter:
                if publication_date_filter == 'recent_three_years':
                    # 近三年：从三年前的今天到今天
                    count_sql += " AND sb.publication_date >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)"
                elif publication_date_filter == 'custom' and publication_start_date and publication_end_date:
                    # 自定义日期范围
                    count_sql += " AND sb.publication_date >= %s AND sb.publication_date <= %s"
                    count_params.extend([publication_start_date, publication_end_date])

            cursor.execute(count_sql, count_params)
            result = cursor.fetchone()
            total = result['total']
            
            return jsonify({
                "code": 0, 
                "data": {
                    "samples": samples,
                    "total": total,
                    "page": page,
                    "limit": limit
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"筛选样书失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/get_user_sample_publishers', methods=['GET'])
def get_user_sample_publishers():
    """
    获取当前用户样书中包含的出版社列表
    用于高级筛选中的出版社选项
    """
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 首先获取用户所属的出版社公司信息
            user_sql = """
                SELECT pc.id, pc.name, pc.is_publisher
                FROM users u
                JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                WHERE u.user_id = %s
            """
            cursor.execute(user_sql, (session['user_id'],))
            user_company = cursor.fetchone()
            
            # 如果用户所属公司是仅为出版社，则不需要返回出版社筛选选项
            if user_company and user_company['is_publisher'] == 1:
                return jsonify({
                    "code": 0,
                    "message": "获取成功",
                    "data": {
                        "user_company": user_company,
                        "publishers": [],
                        "show_publisher_filter": False
                    }
                })
            
            # 如果是多出版社公司，获取该用户样书中包含的所有出版社
            sample_publishers_sql = """
                SELECT DISTINCT sb.publisher_name
                FROM sample_books sb
                WHERE sb.publisher_id = %s AND sb.publisher_name IS NOT NULL AND sb.publisher_name != ''
                ORDER BY sb.publisher_name
            """
            cursor.execute(sample_publishers_sql, (session['user_id'],))
            publishers = cursor.fetchall()
            
            # 转换为简单的名称列表
            publisher_names = [{"name": p['publisher_name']} for p in publishers]
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "user_company": user_company,
                    "publishers": publisher_names,
                    "show_publisher_filter": True
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取出版社列表失败: {str(e)}"})
    finally:
        connection.close()

@publisher_bp.route('/process_duplicate_samples', methods=['POST'])
def process_duplicate_samples():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    publisher_id = session['user_id']
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({"code": 1, "message": "未提供处理数据"})
    
    duplicate_records = data.get('duplicate_records', [])
    action = data.get('action')  # 'replace' 或 'skip'
    
    # 获取之前上传的结果数据
    previous_success = data.get('previous_success', 0)
    previous_errors = data.get('previous_errors', [])
    
    if not duplicate_records:
        return jsonify({"code": 1, "message": "没有需要处理的重复记录"})
    
    if action not in ['replace', 'skip']:
        return jsonify({"code": 1, "message": "无效的处理操作"})
    
    connection = get_db_connection()
    try:
        success_count = 0
        skipped_count = 0
        error_count = 0
        error_details = []
        
        with connection.cursor() as cursor:
            for record in duplicate_records:
                try:
                    row_num = record.get('row')
                    new_data = record.get('new_data')
                    existing_id = record.get('existing_id')
                    
                    if not new_data or not existing_id:
                        error_count += 1
                        error_details.append({
                            'row': row_num,
                            'errors': ["数据格式不正确"]
                        })
                        continue
                    
                    if action == 'skip':
                        skipped_count += 1
                        continue
                    
                    # 处理替换操作
                    update_fields = []
                    update_values = []
                    
                    for key, value in new_data.items():
                        if key != 'isbn' and key != 'feature_ids':  # 排除ISBN和特色IDs
                            update_fields.append(f"{key} = %s")
                            update_values.append(value)
                    
                    if not update_fields:
                        skipped_count += 1
                        continue
                    
                    update_sql = f"""
                        UPDATE sample_books SET 
                        {', '.join(update_fields)}
                        WHERE id = %s AND publisher_id = %s
                    """
                    
                    update_values.append(existing_id)
                    update_values.append(publisher_id)
                    
                    cursor.execute(update_sql, update_values)
                    
                    # 处理特色关联
                    feature_ids = new_data.get('feature_ids', [])
                    if feature_ids:
                        # 先删除现有关联
                        cursor.execute("DELETE FROM sample_book_features WHERE sample_id = %s", (existing_id,))
                        
                        # 再添加新关联
                        for feature_id in feature_ids:
                            cursor.execute(
                                "INSERT INTO sample_book_features (sample_id, feature_id) VALUES (%s, %s)",
                                (existing_id, feature_id)
                            )
                    
                    success_count += 1
                except Exception as e:
                    error_count += 1
                    error_details.append({
                        'row': row_num,
                        'errors': [f"数据库错误: {str(e)}"]
                    })
        
        connection.commit()
        
        # 整合总体结果
        total_count = len(duplicate_records)
        
        return jsonify({
            "code": 0,
            "message": "重复记录处理完成",
            "data": {
                "total_count": total_count,
                "success_count": success_count,
                "skipped_count": skipped_count,
                "error_count": error_count,
                "error_details": error_details
            }
        })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"处理重复记录时发生错误: {str(e)}"})
    finally:
        connection.close()