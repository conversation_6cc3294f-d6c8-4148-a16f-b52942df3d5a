<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title></title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <style>
        :root {
            --primary: #3b82f6;
            --primary-hover: #2563eb;
            --primary-light: #93c5fd;
            --secondary: #14b8a6;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --sidebar-bg: #f8fafc;
            --sidebar-text: #64748b;
            --sidebar-active-bg: #eff6ff;
            --sidebar-active-text: #3b82f6;
            --sidebar-icon: #64748b;
            --sidebar-hover-bg: #f1f5f9;
            --sidebar-border: #e2e8f0;
        }
        
        body {
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #f1f5f9;
        }
        
        .sidebar {
            background-color: var(--sidebar-bg);
            width: 250px;
            min-width: 250px;
            box-shadow: 1px 0 5px rgba(0, 0, 0, 0.05);
            z-index: 40;
            border-right: 1px solid var(--sidebar-border);
            overflow-x: hidden;
            will-change: width, transform;
            transform: translateZ(0);
            transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .sidebar.collapsed {
            width: 0;
            min-width: 0;
            padding: 0;
            margin: 0;
            border: none;
            box-shadow: none;
            opacity: 0.95;
        }
        
        .navbar-nav-item {
            border-radius: 8px;
            transition: background-color 0.15s ease;
            margin: 0.35rem 0;
            overflow: hidden;
            white-space: nowrap;
        }
        
        .navbar-nav-item:hover {
            background-color: var(--sidebar-hover-bg);
        }
        
        .navbar-nav-item.active {
            background-color: var(--sidebar-active-bg);
            color: var(--sidebar-active-text);
        }
        
        .navbar-nav-item.active i {
            color: var(--sidebar-active-text);
        }
        
        .navbar-nav-item.active span {
            color: var(--sidebar-active-text);
            font-weight: 500;
        }
        
        .sidebar-icon {
            color: var(--sidebar-icon);
            width: 20px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            transition: color 0.15s ease;
            font-size: 14px;
        }
        
        .navbar-nav-item:hover .sidebar-icon {
            color: var(--primary);
        }
        
        .sidebar-text {
            color: var(--sidebar-text);
        }
        
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 250ms cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: 4px;
            padding-left: 12px;
            border-left: 1px solid var(--sidebar-border);
        }
        
        .submenu.open {
            max-height: none; /* 让内容自适应高度 */
        }
        
        /* 子菜单项过渡 */
        .submenu li {
            opacity: 0;
            transform: translateY(-8px);
            transition: opacity 180ms ease-out, transform 180ms ease-out;
            transition-delay: calc(var(--item-index, 0) * 30ms);
        }
        
        .submenu.open li {
            opacity: 1;
            transform: translateY(0);
        }
        
        .toggle-arrow {
            transition: transform 0.15s ease;
        }
        
        .toggle-arrow.rotated {
            transform: rotate(90deg);
        }
        
        .main-content {
            transition: margin-left 300ms cubic-bezier(0.4, 0, 0.2, 1);
            will-change: margin-left;
            transform: translateZ(0);
        }
        
        .top-navbar {
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            height: 64px;
        }
        
        .user-dropdown {
            opacity: 0;
            transform: translateY(10px) scale(0.95);
            transition: opacity 0.2s ease, transform 0.2s ease;
            transform-origin: top right;
            pointer-events: none;
        }
        
        .user-dropdown.show {
            opacity: 1;
            transform: translateY(0) scale(1);
            pointer-events: auto;
        }
        
        .loading-overlay {
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(2px);
        }
        
        /* 用户头像 */
        .user-avatar {
            background-color: #3b82f6;
            box-shadow: 0 2px 5px rgba(59, 130, 246, 0.2);
        }

        /* 折叠侧边栏时的过渡 */
        .nav-text, .sidebar-icon {
            opacity: 1;
            transition: opacity 120ms ease;
        }

        .sidebar.collapsed .nav-content {
            opacity: 0;
            visibility: hidden;
        }
        
        /* 导航容器 */
        .brand-logo {
            color: var(--primary);
            font-size: 1.25rem;
            margin-right: 0.75rem;
        }
        
        /* 品牌名称 */
        .brand-name {
            color: #1e293b;
            font-weight: 600;
        }
        
        /* 高性能滚动容器 */
        .scroll-container {
            overscroll-behavior: contain;
            -webkit-overflow-scrolling: touch;
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 5px;
            height: 5px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c5c5c5;
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #a0a0a0;
        }
        
        /* 动画 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.3s ease forwards;
        }
        
        @keyframes slideUp {
            from { transform: translateY(10px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .animate-slide-up {
            animation: slideUp 0.3s ease forwards;
        }
        
        /* 添加导航内容过渡效果 */
        .nav-content {
            opacity: 1;
            transition: opacity 180ms ease;
            width: 100%;
        }

        /* 解决iframe在移动端的缩放问题 */
        @media (max-width: 768px) {
            #contentFrame {
                width: 1px;
                min-width: 100%;
            }
            
            /* 移动端侧边栏样式 */
            .sidebar:not(.collapsed) {
                position: fixed;
                left: 0;
                top: 0;
                height: 100%;
                width: 100%;
                max-width: 100%;
                z-index: 50;
            }
            
            /* 移动端顶部导航栏样式 */
            .top-navbar {
                padding-left: 1rem;
                padding-right: 1rem;
            }
            
            /* 隐藏移动端不必要元素 */
            .brand-name {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="flex h-screen overflow-hidden">
        <!-- 侧边导航 -->
        <aside id="sidebar" class="sidebar">
            <div class="nav-content">
                <!-- 品牌LOGO -->
                <div class="flex items-center h-16 px-5 mt-1 mb-2 border-b border-gray-100">
                    <div class="flex items-center overflow-hidden">
                        <img id="siteLogo" class="brand-logo mr-2 w-8 h-8 object-contain hidden" alt="网站Logo">
                        <i id="defaultLogo" class="fas fa-book-open brand-logo mr-2"></i>
                        <h1 id="siteName" class="brand-name text-lg truncate whitespace-nowrap"></h1>
                    </div>
                </div>
                
                <!-- 用户信息卡片 -->
                <div class="mx-4 mb-6 rounded-lg p-3 border border-gray-100 bg-white">
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <div class="user-avatar w-10 h-10 rounded-full flex items-center justify-center text-white text-lg font-bold">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-700 truncate">
                                <span id="userNameDisplay">{{ session.get('name', session.get('username', '用户')) }}</span>
                            </p>
                            <p class="text-xs text-gray-500 truncate">
                                <span id="userRoleDisplay">{{ session.get('role', '') }}</span>
                            </p>
                        </div>
                    </div>
        </div>
        
                <!-- 导航菜单 -->
                <nav id="navContainer" class="px-4 py-2 scroll-container overflow-y-auto" style="max-height: calc(100vh - 180px);">
                    <div class="text-gray-400 text-xs uppercase tracking-wider font-semibold mb-3">
                        <i class="fas fa-circle-notch fa-spin mr-2"></i>
                        菜单加载中...
                    </div>
                    <!-- 导航菜单将在这里动态加载 -->
                </nav>
            </div>
        </aside>

        <!-- 主内容区 -->
        <div class="flex-1 flex flex-col overflow-hidden main-content">
            <!-- 顶部导航栏 -->
            <header class="top-navbar flex items-center justify-between px-6">
                <!-- 左侧 - 切换侧边栏按钮和面包屑 -->
                <div class="flex items-center space-x-4">
                    <button id="sidebarToggle" class="text-gray-600 hover:text-blue-600 focus:outline-none transition-transform duration-200">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    
                    <!-- 当前页面标题 - 移动端使用 -->
                    <h1 id="mobilePageTitle" class="font-medium text-gray-800 md:hidden">仪表盘</h1>
                    
                    <!-- 面包屑 - 仅桌面端显示 -->
                    <div id="breadcrumb" class="hidden md:flex items-center space-x-2 text-sm">
                        <a href="javascript:void(0);" class="text-gray-500 hover:text-blue-600 transition-colors duration-200">首页</a>
                        <span class="text-gray-400">/</span>
                        <span id="currentPageTitle" class="text-gray-700 font-medium">仪表盘</span>
                    </div>
                </div>
                
                <!-- 右侧 - 工具栏 -->
                <div class="flex items-center space-x-4">
                    <!-- 刷新按钮 -->
                    <button id="refreshBtn" class="p-2 text-gray-600 hover:text-blue-600 rounded-full hover:bg-blue-50 focus:outline-none relative transition-colors duration-200">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    
                    <!-- 用户菜单 -->
                    <div class="relative" id="userMenu">
                        <button id="userMenuBtn" class="flex items-center space-x-2 focus:outline-none">
                            <span class="text-sm text-gray-700 font-medium hidden md:block">{{ session.get('username', '用户') }}</span>
                            <div class="user-avatar w-8 h-8 rounded-full flex items-center justify-center text-white">
                                <i class="fas fa-user"></i>
                            </div>
                        </button>
                        
                        <!-- 用户下拉菜单 -->
                        <div id="userDropdown" class="user-dropdown hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl py-2 z-50">
                            <a href="javascript:void(0);" onclick="loadPage('/edit_profile')"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200">
                                <i class="fas fa-user-edit mr-2 text-blue-500"></i>
                                <span>修改资料</span>
                            </a>
                            <!-- 切换角色按钮 -->
                            <a href="javascript:void(0);" id="switchRoleBtn"
                               class="hidden flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-600 transition-colors duration-200">
                                <i class="fas fa-exchange-alt mr-2 text-green-500"></i>
                                <span id="switchRoleText">切换角色</span>
                            </a>
                            <div class="border-t border-gray-100 my-1"></div>
                            <a href="javascript:void(0);" id="logoutBtn"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors duration-200">
                                <i class="fas fa-sign-out-alt mr-2 text-red-500"></i>
                                <span>退出登录</span>
                            </a>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 内容区域 -->
            <main class="flex-1 overflow-hidden bg-gray-50 p-0">
                <iframe id="contentFrame" 
                        src="" 
                        class="w-full h-full border-0"
                        scrolling="auto"
                        frameborder="0"
                        allowfullscreen>
                </iframe>
            </main>
        </div>
    </div>
    
    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay fixed inset-0 flex items-center justify-center z-50 hidden animate-fade-in">
        <div class="bg-white rounded-lg shadow-xl p-6 max-w-sm">
            <div class="flex items-center space-x-4">
                <div class="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <p class="text-gray-700 font-medium">加载中，请稍候...</p>
            </div>
        </div>
    </div>
    
    <!-- 全局消息提示 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 w-72"></div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // DOM元素引用
        const sidebar = document.getElementById('sidebar');
        const navContent = document.querySelector('.nav-content');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const contentFrame = document.getElementById('contentFrame');
        const refreshBtn = document.getElementById('refreshBtn');
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const logoutBtn = document.getElementById('logoutBtn');
        const switchRoleBtn = document.getElementById('switchRoleBtn');
        const navContainer = document.getElementById('navContainer');
        const messageContainer = document.getElementById('messageContainer');
        const mainContent = document.querySelector('.main-content');
        
        // 状态变量
        let sidebarCollapsed = false;
        let activeNavItem = null;
        
        // 提升动画性能的函数
        function optimizeForAnimation(element) {
            if (!element) return;
            element.style.backfaceVisibility = 'hidden';
            element.style.webkitBackfaceVisibility = 'hidden';
            element.style.willChange = 'transform, opacity';
        }
        
        // 优化关键元素
        optimizeForAnimation(sidebar);
        optimizeForAnimation(mainContent);
        
        // 侧边栏折叠/展开功能
        sidebarToggle.addEventListener('click', () => {
            sidebarCollapsed = !sidebarCollapsed;
            
            if(sidebarCollapsed) {
                // 先淡出内容，然后收缩宽度
                navContent.style.opacity = '0';
                sidebarToggle.innerHTML = '<i class="fas fa-expand-alt text-xl"></i>';
                
                // 等待内容淡出后再收缩宽度
                setTimeout(() => {
                    sidebar.classList.add('collapsed');
                    
                    // 移除移动端覆盖层
                    document.getElementById('mobile-overlay')?.remove();
                }, 180);
            } else {
                // 先展开宽度，再显示内容
                sidebar.classList.remove('collapsed');
                sidebarToggle.innerHTML = '<i class="fas fa-bars text-xl"></i>';
                
                // 移动端添加遮罩层和关闭按钮
                if (window.innerWidth < 768) {
                    // 创建遮罩层
                    const overlay = document.createElement('div');
                    overlay.id = 'mobile-overlay';
                    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-40';
                    overlay.addEventListener('click', () => {
                        sidebarToggle.click(); // 点击遮罩层收起侧边栏
                    });
                    document.body.appendChild(overlay);
                    
                    // 添加关闭按钮
                    if (!document.getElementById('mobile-close-btn')) {
                        const closeBtn = document.createElement('button');
                        closeBtn.id = 'mobile-close-btn';
                        closeBtn.className = 'absolute top-4 right-4 text-gray-500 hover:text-gray-700 focus:outline-none';
                        closeBtn.innerHTML = '<i class="fas fa-times text-xl"></i>';
                        closeBtn.addEventListener('click', () => {
                            sidebarToggle.click(); // 点击关闭按钮收起侧边栏
                            sidebarToggle.innerHTML = '<i class="fas fa-bars text-xl"></i>';
                        });
                        navContent.appendChild(closeBtn);
                    }
                }
                
                // 等待宽度展开后再显示内容
                setTimeout(() => {
                    navContent.style.opacity = '1';
                    // 应用CSS动画效果
                    document.querySelectorAll('.navbar-nav-item').forEach((item, index) => {
                        item.style.opacity = '0';
                        item.style.transform = 'translateX(-20px)';
                        item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                        item.style.transitionDelay = `${index * 30}ms`;
                        
                        setTimeout(() => {
                            item.style.opacity = '1';
                            item.style.transform = 'translateX(0)';
                        }, 50);
                    });
                }, 50);
            }
        });
        
        // 刷新iframe内容
        refreshBtn.addEventListener('click', () => {
            showLoading();
            contentFrame.src = contentFrame.src;
        });
        
        // 用户菜单显示/隐藏
        userMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            userDropdown.classList.toggle('hidden');
            
            // 使用requestAnimationFrame使CSS动画更流畅
            requestAnimationFrame(() => {
                userDropdown.classList.toggle('show');
            });
        });
        
        // 点击其他区域关闭用户菜单
        document.addEventListener('click', () => {
            userDropdown.classList.remove('show');
            
            // 延迟隐藏使动画能够完成
            setTimeout(() => {
                if (!userDropdown.classList.contains('show')) {
                    userDropdown.classList.add('hidden');
                }
            }, 200);
        });
        
        // iframe加载完成隐藏加载遮罩
        contentFrame.addEventListener('load', () => {
            hideLoading();
            
            // 尝试从iframe内容获取标题和URL
            try {
                // 获取标题
                const iframeTitle = contentFrame.contentDocument.title;
                if (iframeTitle) {
                    document.getElementById('currentPageTitle').textContent = iframeTitle;
                }
                
                // 获取当前iframe的URL并同步导航菜单选中状态
                const currentUrl = contentFrame.contentWindow.location.pathname;
                syncNavMenuWithUrl(currentUrl);
                
                // 监听iframe内部的链接点击，以便更新导航菜单
                contentFrame.contentDocument.addEventListener('click', (e) => {
                    // 延迟检查URL变化，等待可能的页面加载完成
                    setTimeout(() => {
                        const newUrl = contentFrame.contentWindow.location.pathname;
                        if (newUrl !== currentUrl) {
                            syncNavMenuWithUrl(newUrl);
                        }
                    }, 100);
                });
            } catch (e) {
                // 可能因为跨域问题无法访问iframe内容
                console.log('无法从iframe获取信息:', e);
            }
        });

        // 切换角色功能
        switchRoleBtn.addEventListener('click', () => {
            showLoading();
            $.ajax({
                url: '/api/common/switch_role',
                method: 'POST',
                success: function(response) {
                    hideLoading();
                    if(response.status === 'success') {
                        showMessage(response.message, 'success');
                        setTimeout(() => {
                            window.location.href = response.redirect;
                        }, 1000);
                    } else {
                        showMessage(response.message || '角色切换失败', 'error');
                    }
                },
                error: function() {
                    hideLoading();
                    showMessage('角色切换失败', 'error');
                }
            });
        });

        // 退出登录功能
        logoutBtn.addEventListener('click', () => {
            showLoading();
            $.ajax({
                url: '/api/common/logout',
                method: 'POST',
                success: function(response) {
                    hideLoading();
                    if(response.status === 'success') {
                        showMessage('注销成功', 'success');
                        setTimeout(() => {
                                window.location.href = response.redirect;
                        }, 1500);
                        } else {
                        showMessage('注销失败: ' + response.message, 'error');
                        }
                    },
                    error: function() {
                    hideLoading();
                    showMessage('注销请求失败，请重试', 'error');
                    }
            });
        });

        // 页面加载
        function loadPage(url) {
            // 如果是登录页面，则直接跳转整个页面
            if (url === '/login' || url.includes('/login')) {
                window.location.href = url;
                return;
            }
            
            showLoading();
            contentFrame.src = url;
            
            // 更新面包屑
            updateBreadcrumb(url);
            
            // 同步导航菜单选中状态
            syncNavMenuWithUrl(url);
        }
        
        // 更新面包屑
        function updateBreadcrumb(url) {
            // 根据URL获取页面标题
            const pageTitles = {
                '/admin/dashboard': '仪表盘',
                '/admin_manage_samples': '管理样书',
                '/admin_manage_sample_requests': '管理样书申请',
                '/admin_manage_report_requests': '管理报备申请',
                '/manage_users': '用户管理',
                '/request_samples': '申请样书',
                '/manage_sample_requests': '管理样书申请',
                '/edit_profile': '修改资料'
                // 可以根据需要添加更多的页面标题
            };
            
            // 提取URL路径
            const path = url.split('?')[0];
            
            // 更新标题
            const currentPageTitle = document.getElementById('currentPageTitle');
            const mobilePageTitle = document.getElementById('mobilePageTitle');
            const pageTitle = pageTitles[path] || '页面';
            
            if (currentPageTitle) {
                currentPageTitle.textContent = pageTitle;
            }
            
            if (mobilePageTitle) {
                mobilePageTitle.textContent = pageTitle;
            }
        }
        
        // 显示加载遮罩
        function showLoading() {
            loadingOverlay.classList.remove('hidden');
        }
        
        // 隐藏加载遮罩
        function hideLoading() {
            loadingOverlay.classList.add('hidden');
        }
        
        // 显示全局消息
        function showMessage(message, type = 'info') {
            const id = 'msg-' + Date.now();
            const bgColor = type === 'success' ? 'bg-green-500' : 
                           type === 'error' ? 'bg-red-500' : 
                           type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';
            
            const icon = type === 'success' ? 'fa-check-circle' : 
                        type === 'error' ? 'fa-exclamation-circle' : 
                        type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';
            
            const html = `
                <div id="${id}" class="flex items-center p-4 mb-3 rounded-lg shadow-lg ${bgColor} text-white animate-slide-up">
                    <i class="fas ${icon} mr-3"></i>
                    <p class="flex-1">${message}</p>
                    <button onclick="document.getElementById('${id}').remove()" class="ml-2 text-white hover:text-gray-200 focus:outline-none">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            // 使用优化的方式添加元素
            messageContainer.insertAdjacentHTML('afterbegin', html);
            
            // 获取元素引用后再操作
            const messageElement = document.getElementById(id);
            optimizeForAnimation(messageElement);
            
            setTimeout(() => {
                if (messageElement) {
                    messageElement.style.opacity = '0';
                    messageElement.style.transform = 'translateY(-10px)';
                    messageElement.style.transition = 'opacity 0.3s, transform 0.3s';
                    setTimeout(() => {
                        if (messageElement && messageElement.parentNode) {
                            messageElement.parentNode.removeChild(messageElement);
                        }
                    }, 300);
                }
            }, 5000);
        }
        
        // 设置导航项激活状态
        function setActiveNavItem(element) {
            if (activeNavItem) {
                activeNavItem.classList.remove('active');
            }
            element.classList.add('active');
            activeNavItem = element;
            
            // 如果是子菜单项，确保父菜单展开
            const parentSubmenu = element.closest('.submenu');
            if (parentSubmenu && !parentSubmenu.classList.contains('open')) {
                const parentButton = parentSubmenu.previousElementSibling;
                toggleSubmenu(parentButton);
            }
        }
        
        // 切换子菜单
        function toggleSubmenu(element) {
            const submenu = element.nextElementSibling;
            const arrow = element.querySelector('.toggle-arrow');
            
            if (submenu.classList.contains('open')) {
                // 收缩子菜单
                arrow.classList.remove('rotated');
                
                // 先设置固定高度，然后过渡到0
                submenu.style.maxHeight = submenu.scrollHeight + 'px';
                
                // 强制重绘，确保前一行代码生效
                submenu.offsetHeight;
                
                // 开始过渡到0高度
                submenu.style.maxHeight = '0px';
                
                // 等待过渡完成后移除open类
                setTimeout(() => {
                    submenu.classList.remove('open');
                }, 250);
            } else {
                // 展开子菜单
                submenu.classList.add('open');
                arrow.classList.add('rotated');
                
                // 设置最大高度以便有过渡效果
                const scrollHeight = submenu.scrollHeight;
                submenu.style.maxHeight = '0px';
                
                // 强制重绘
                submenu.offsetHeight;
                
                // 过渡到实际高度
                submenu.style.maxHeight = scrollHeight + 'px';
                
                // 过渡结束后设置为auto，以便适应内容变化
                setTimeout(() => {
                    submenu.style.maxHeight = 'none';
                }, 350);
            }
        }
        
        // 生成导航菜单HTML - 优化渲染方式
        function generateNavItem(item) {
            if(item.sub_items && item.sub_items.length > 0) {
                const submenuItems = item.sub_items.map(subItem => {
                    return `
                        <li>
                            <a href="javascript:void(0);" 
                               onclick="loadPage('${subItem.url}'); setActiveNavItem(this);" 
                               class="navbar-nav-item flex items-center px-4 py-2 rounded-md text-sm">
                                <i class="${subItem.icon || 'fas fa-circle'} text-xs mr-3 w-4 text-center sidebar-icon"></i>
                                <span class="sidebar-text">${subItem.title}</span>
                            </a>
                        </li>
                    `;
                }).join('');
                
                return `
                    <div class="mb-3">
                        <button class="navbar-nav-item flex items-center justify-between w-full px-4 py-2 rounded-md"
                                onclick="toggleSubmenu(this)">
                            <div class="flex items-center">
                                <i class="${item.icon || 'fas fa-folder'} mr-3 w-5 text-center sidebar-icon"></i>
                                <span class="sidebar-text">${item.title}</span>
                            </div>
                            <i class="fas fa-chevron-right text-xs toggle-arrow transition-transform duration-200"></i>
                        </button>
                        <ul class="submenu mt-1 transition-all duration-300">
                            ${submenuItems}
                        </ul>
                    </div>
                `;
            } else {
                return `
                    <div class="mb-2">
                        <a href="javascript:void(0);" 
                           onclick="loadPage('${item.url}'); setActiveNavItem(this);" 
                           class="navbar-nav-item flex items-center px-4 py-2 rounded-md">
                            <i class="${item.icon || 'fas fa-circle'} mr-3 w-5 text-center sidebar-icon"></i>
                            <span class="sidebar-text">${item.title}</span>
                        </a>
                    </div>
                `;
            }
        }
        
        // 加载网站信息
        function loadSiteInfo(userRole) {
            $.ajax({
                url: '/api/common/get_site_info',
                method: 'GET',
                data: { role: userRole },
                success: function(response) {
                    if(response.code === 0 && response.data) {
                        // 更新网站名称
                        if(response.data.site_name) {
                            document.getElementById('siteName').textContent = response.data.site_name;
                            // 同步更新页面title标签
                            document.title = response.data.site_name;
                        }

                        // 更新Logo
                        if(response.data.logo_url) {
                            document.getElementById('siteLogo').src = response.data.logo_url;
                            document.getElementById('siteLogo').classList.remove('hidden');
                            document.getElementById('defaultLogo').classList.add('hidden');
                        } else {
                            document.getElementById('siteLogo').classList.add('hidden');
                            document.getElementById('defaultLogo').classList.remove('hidden');
                        }
                    }
                },
                error: function() {
                    console.log('获取网站信息失败，使用默认配置');
                }
            });
        }

        // 检查切换角色权限
        function checkRoleSwitchPermission() {
            $.ajax({
                url: '/api/common/check_role_switch_permission',
                method: 'GET',
                success: function(response) {
                    if(response.status === 'success' && response.can_switch) {
                        // 显示切换角色按钮
                        switchRoleBtn.classList.remove('hidden');

                        // 更新按钮文本
                        const targetRole = response.target_role === 'dealer' ? '经销商' : '供应商';
                        const targetName = response.target_user_info.company_name || response.target_user_info.name || response.target_user_info.username;
                        document.getElementById('switchRoleText').textContent = `切换到${targetRole}`;
                        switchRoleBtn.title = `切换到: ${targetName}`;
                    } else {
                        // 隐藏切换角色按钮
                        switchRoleBtn.classList.add('hidden');
                    }
                },
                error: function() {
                    // 出错时隐藏切换角色按钮
                    switchRoleBtn.classList.add('hidden');
                }
            });
        }

        // 加载导航菜单数据 - 优化DOM操作
        function loadNavMenu() {
            $.ajax({
                url: '/api/common/get_nav_data',
                method: 'GET',
                success: function(response) {
                    if(response.status === 'success') {
                        // 创建文档片段，减少DOM重排
                        const fragment = document.createDocumentFragment();

                        // 根据用户角色设置标题
                        const roleTitle = response.user_role === 'admin' ? '管理员' :
                                         response.user_role === 'teacher' ? '教师' :
                                         response.user_role === 'publisher' ? '供应商' :
                                         response.user_role === 'dealer' ? '经销商' : '用户';

                        // 设置角色信息
                        document.getElementById('userRoleDisplay').textContent = roleTitle;

                        // 加载网站信息
                        loadSiteInfo(response.user_role);
                        
                        // 添加导航菜单分类
                        const navHeader = document.createElement('div');
                        navHeader.className = 'text-xs font-semibold uppercase tracking-wider text-gray-500 mb-4 pl-3';
                        navHeader.innerHTML = `<span>功能导航</span>`;
                        fragment.appendChild(navHeader);
                        
                        // 一次性生成HTML字符串
                        let menuHtml = '';
                        response.nav_items.forEach(item => {
                            menuHtml += generateNavItem(item);
                        });
                        
                        // 创建一个容器元素并设置HTML
                        const menuContainer = document.createElement('div');
                        menuContainer.innerHTML = menuHtml;
                        
                        // 将所有子元素添加到片段
                        while (menuContainer.firstChild) {
                            fragment.appendChild(menuContainer.firstChild);
                        }
                        
                        // 清空导航并一次性添加所有元素
                        navContainer.innerHTML = '';
                        navContainer.appendChild(fragment);
                        
                        // 设置第一个菜单项为活动状态
                        const firstNavItem = navContainer.querySelector('.navbar-nav-item');
                        if(firstNavItem) {
                            setActiveNavItem(firstNavItem);
                        }
                        
                        // 触发自定义事件，通知菜单加载完成
                        document.dispatchEvent(new CustomEvent('navMenuLoaded'));
                } else {
                        navContainer.innerHTML = `
                            <div class="px-4 py-3 text-red-400">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <span>加载导航菜单失败</span>
                            </div>
                        `;
                    }
                },
                error: function() {
                    navContainer.innerHTML = `
                        <div class="px-4 py-3 text-red-400">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span>网络错误，无法加载菜单</span>
                        </div>
                    `;
                }
            });
        }
        
        // 设置子菜单项的过渡延迟
        function setupSubmenuAnimations() {
            document.querySelectorAll('.submenu').forEach(submenu => {
                const items = submenu.querySelectorAll('li');
                items.forEach((item, index) => {
                    item.style.setProperty('--item-index', index);
                });
            });
        }
        
        // 根据URL同步导航菜单选中状态
        function syncNavMenuWithUrl(url) {
            // 标准化URL，移除参数和末尾斜杠
            const normalizedUrl = url.split('?')[0].replace(/\/$/, '');
            
            // 查找所有导航项
            const allNavItems = document.querySelectorAll('.navbar-nav-item');
            let found = false;
            
            // 首先查找完全匹配的导航项
            allNavItems.forEach(navItem => {
                // 获取导航项的URL
                const navItemUrl = navItem.getAttribute('onclick')?.match(/loadPage\(['"](.*?)['"]\)/)?.[1];
                
                if (navItemUrl && (navItemUrl === normalizedUrl || normalizedUrl.endsWith(navItemUrl))) {
                    setActiveNavItem(navItem);
                    found = true;
                    
                    // 如果是在子菜单中，确保父菜单展开
                    const parentSubmenu = navItem.closest('.submenu');
                    if (parentSubmenu && !parentSubmenu.classList.contains('open')) {
                        const parentButton = parentSubmenu.previousElementSibling;
                        toggleSubmenu(parentButton);
                    }
                }
            });
            
            // 如果没有找到完全匹配的，尝试查找部分匹配
            if (!found) {
                allNavItems.forEach(navItem => {
                    const navItemUrl = navItem.getAttribute('onclick')?.match(/loadPage\(['"](.*?)['"]\)/)?.[1];
                    
                    if (navItemUrl && normalizedUrl.includes(navItemUrl) && navItemUrl !== '/') {
                        setActiveNavItem(navItem);
                        found = true;
                        
                        // 如果是在子菜单中，确保父菜单展开
                        const parentSubmenu = navItem.closest('.submenu');
                        if (parentSubmenu && !parentSubmenu.classList.contains('open')) {
                            const parentButton = parentSubmenu.previousElementSibling;
                            toggleSubmenu(parentButton);
                        }
                    }
                });
            }
            
            return found;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 预加载CSS动画，优化首次动画体验
            document.body.style.opacity = "0.99";
            setTimeout(() => document.body.style.opacity = "1", 0);
            
            loadNavMenu();

            // 检查切换角色权限
            checkRoleSwitchPermission();

            // 监听导航菜单加载完成
            document.addEventListener('navMenuLoaded', setupSubmenuAnimations);
            
            // 检测是否为移动设备
            const isMobile = window.innerWidth < 768;
            
            // 移动设备默认收起侧边栏
            if (isMobile) {
                sidebarCollapsed = true;
                navContent.style.opacity = '0';
                sidebarToggle.innerHTML = '<i class="fas fa-bars text-xl"></i>';
                sidebar.classList.add('collapsed');
            }
            
            // 获取初始页面URL
            $.ajax({
                url: '/api/common/get_nav_data',
                method: 'GET',
                success: function(response) {
                    if(response.status === 'success') {
                        // 从API响应中获取初始页面URL
                        const initialPage = response.initial_page || '/login';
                        
                        // 加载初始页面
                        loadPage(initialPage);
                    } else {
                        // 加载出错时重定向到登录页面
                        window.location.href = '/login';
                    }
                },
                error: function() {
                    // 请求失败时的处理
                    showMessage('无法获取导航数据，请重新登录', 'error');
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                }
            });
            
            // 初始化面包屑
            updateBreadcrumb(contentFrame.src);
        });
        
        // 添加窗口大小变化监听器，处理响应式布局变化
        window.addEventListener('resize', function() {
            const isMobile = window.innerWidth < 768;
            
            // 小屏幕下保持侧边栏收起状态
            if (isMobile && !sidebarCollapsed) {
                sidebarCollapsed = true;
                navContent.style.opacity = '0';
                sidebarToggle.innerHTML = '<i class="fas fa-expand-alt text-xl"></i>';
                sidebar.classList.add('collapsed');
            }
        });
    </script>
</body>
</html> 