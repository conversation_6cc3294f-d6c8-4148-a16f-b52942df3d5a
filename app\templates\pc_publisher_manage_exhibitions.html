<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书展活动管理 - 供应商中心</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 状态标签样式 */
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-draft {
            background-color: #e5e7eb;
            color: #4b5563;
        }
        .status-published {
            background-color: #dcfce7;
            color: #166534;
        }
        .status-cancelled {
            background-color: #fee2e2;
            color: #b91c1c;
        }
        .status-ended {
            background-color: #f3f4f6;
            color: #1f2937;
        }
        
        /* 动画效果 */
        .animate-fadeIn {
            animation: fadeIn 0.3s ease-in;
        }
        .animate-fadeOut {
            animation: fadeOut 0.3s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
        }
        
        /* 活动卡片悬停效果 */
        .exhibition-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .exhibition-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        /* 模态框背景 */
        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(2px);
        }
        
        /* 消息提示容器 - 确保最高层级 */
        #messageContainer {
            z-index: 9999 !important; 
            pointer-events: none;
        }
        
        /* 消息样式 */
        .message-toast {
            pointer-events: auto;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
            min-width: 250px;
        }
        
        /* 响应式调整 */
        @media (max-width: 640px) {
            .status-tabs {
                overflow-x: auto;
                white-space: nowrap;
                -webkit-overflow-scrolling: touch;
                padding-bottom: 0.5rem;
            }
            .status-tab {
                display: inline-block;
            }
        }

        /* 移动端模态框样式调整 */
        @media (max-width: 768px) {
            .modal-content {
                width: 92% !important;
                max-width: 92% !important;
                max-height: 92% !important;
                margin: 0 auto;
            }
            
            /* 参展人员模态框样式 */
            .participants-modal-content {
                width: 92% !important;
                max-width: 92% !important;
                max-height: 85vh !important;
                margin: 0 auto;
                display: flex;
                flex-direction: column;
            }
            
            .modal-body {
                max-height: calc(85vh - 130px);
                overflow-y: auto;
            }
        }
        
        /* 桌面端参展人员模态框样式 */
        .participants-modal-content {
            max-width: 650px;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
        }
        
        /* 模态框内容样式 */
        .modal-header {
            position: sticky;
            top: 0;
            background-color: white;
            z-index: 10;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .modal-body {
            flex-grow: 1;
            overflow-y: auto;
            max-height: calc(80vh - 130px);
        }
        
        .modal-footer {
            position: sticky;
            bottom: 0;
            background-color: white;
            z-index: 10;
            border-top: 1px solid #e5e7eb;
        }
        
        /* 错误信息模态框样式 */
        .error-modal-content {
            max-width: 450px !important;
        }
        
        /* 所有模态框容器 */
        #errorModalContainer, #confirmModalContainer, #detailModalContainer, #participantsModalContainer, #registrationDetailContainer {
            z-index: 9000;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-[9999] flex flex-col items-end space-y-2"></div>
    
    <div class="container mx-auto px-4 py-6">
        <header class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800">书展活动管理</h1>
            <p class="text-gray-600">查看并参与书展活动</p>
        </header>
        
        <!-- 标签状态切换 -->
        <div class="bg-white rounded-lg shadow p-4 mb-6">
            <div class="status-tabs flex space-x-2 border-b border-gray-200 pb-3 mb-4">
                <button id="allTab" class="status-tab px-4 py-2 rounded-t-lg font-medium border-b-2 border-blue-500 text-blue-600 bg-blue-50">
                    全部 <span id="allCount" class="inline-flex items-center justify-center w-5 h-5 ml-1 text-xs bg-blue-100 text-blue-600 rounded-full">0</span>
                </button>
                <button id="publishedTab" class="status-tab px-4 py-2 rounded-t-lg font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50">
                    进行中 <span id="publishedCount" class="inline-flex items-center justify-center w-5 h-5 ml-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                </button>
                <button id="upcomingTab" class="status-tab px-4 py-2 rounded-t-lg font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50">
                    即将开始 <span id="upcomingCount" class="inline-flex items-center justify-center w-5 h-5 ml-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                </button>
                <button id="registerableTab" class="status-tab px-4 py-2 rounded-t-lg font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50">
                    可报名 <span id="registerableCount" class="inline-flex items-center justify-center w-5 h-5 ml-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                </button>
                <button id="registeredTab" class="status-tab px-4 py-2 rounded-t-lg font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50">
                    已报名 <span id="registeredCount" class="inline-flex items-center justify-center w-5 h-5 ml-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                </button>
                <button id="cancelledTab" class="status-tab px-4 py-2 rounded-t-lg font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50">
                    已取消 <span id="cancelledCount" class="inline-flex items-center justify-center w-5 h-5 ml-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                </button>
                <button id="endedTab" class="status-tab px-4 py-2 rounded-t-lg font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50">
                    已结束 <span id="endedCount" class="inline-flex items-center justify-center w-5 h-5 ml-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                </button>
            </div>
            
            <!-- 筛选和搜索区域 -->
            <div class="flex flex-col gap-4">
                <div class="flex flex-col sm:flex-row sm:flex-wrap gap-4">
                    <div class="relative w-full sm:w-48">
                        <label for="dateFilter" class="block text-sm font-medium text-gray-700 mb-1">时间筛选</label>
                        <select id="dateFilter" class="block w-full bg-white border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-8 shadow-sm">
                            <option value="all">全部时间</option>
                            <option value="upcoming">即将开始</option>
                            <option value="ongoing">进行中</option>
                            <option value="past">已结束</option>
                            <option value="custom">自定义时间</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700" style="top: 22px;">
                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                        </div>
                    </div>
                    
                    <div class="relative w-full sm:w-48">
                        <label for="schoolFilter" class="block text-sm font-medium text-gray-700 mb-1">学校筛选</label>
                        <select id="schoolFilter" class="block w-full bg-white border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-8 shadow-sm">
                            <option value="all">全部学校</option>
                            <!-- 这里将通过JS动态添加学校选项 -->
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700" style="top: 22px;">
                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                        </div>
                    </div>
                    
                    <div id="customDateContainer" class="hidden w-full sm:w-auto sm:flex sm:items-end">
                        <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                                <input type="date" id="startDate" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
                            </div>
                            <span class="text-center self-end pb-2">至</span>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                                <input type="date" id="endDate" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center w-full">
                    <input type="text" id="searchInput" placeholder="搜索书展主题..." 
                           class="border border-gray-300 rounded-l-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm w-full">
                    <button id="searchBtn" class="bg-blue-500 text-white px-4 py-2 rounded-r-md hover:bg-blue-600 shadow-sm">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 活动列表容器 -->
        <div id="exhibitionsContainer" class="space-y-4">
            <!-- 这里将通过JS动态添加活动卡片 -->
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-spinner fa-spin text-2xl mb-3"></i>
                <p>加载中，请稍候...</p>
            </div>
        </div>
        
        <!-- 分页控件 -->
        <div class="flex justify-between items-center mt-6 bg-white rounded-lg shadow p-2">
            <button id="prevPageBtn" class="px-4 py-2 border-r border-gray-200 text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                <i class="fas fa-chevron-left mr-1"></i>上一页
            </button>
            <div id="pageNumbers" class="flex"></div>
            <button id="nextPageBtn" class="px-4 py-2 border-l border-gray-200 text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                下一页<i class="fas fa-chevron-right ml-1"></i>
            </button>
        </div>
        
        <!-- 书展详情模态框 -->
        <div id="detailModalContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
            <div class="modal-content bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
                <div class="modal-header p-4 flex justify-between items-center">
                    <h3 id="detailModalTitle" class="text-xl font-semibold text-gray-800">书展详情</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#detailModalContainer').addClass('hidden')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body p-6 overflow-y-auto" id="detailModalBody">
                    <!-- 详情内容将通过JS动态加载 -->
                </div>
                <div class="modal-footer p-4 flex justify-end gap-3">
                    <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300" onclick="$('#detailModalContainer').addClass('hidden')">关闭</button>
                    <button id="registerBtn" type="button" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700" onclick="showParticipantsModal()">报名参展</button>
                    <button id="viewParticipantsBtn" type="button" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 hidden" onclick="viewRegistrationDetail()">查看报名信息</button>
                    <button id="cancelRegistrationBtn" type="button" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 hidden" onclick="showCancelConfirm()">取消报名</button>
                </div>
            </div>
        </div>
        
        <!-- 参展人员模态框 -->
        <div id="participantsModalContainer" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
            <div class="participants-modal-content bg-white rounded-lg shadow-xl w-full">
                <div class="modal-header p-4 flex justify-between items-center">
                    <h3 id="participantsModalTitle" class="text-xl font-semibold text-gray-800">报名参展</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#participantsModalContainer').addClass('hidden')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body p-6">
                    <form id="participantsForm" class="space-y-4">
                        <input type="hidden" id="exhibitionId" name="exhibitionId">
                        
                        <div id="participantsList" class="space-y-4">
                            <!-- 参展人员将通过JS动态添加 -->
                        </div>
                        
                        <div class="flex justify-center">
                            <button type="button" class="px-4 py-2 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 flex items-center" onclick="addParticipantField()">
                                <i class="fas fa-plus mr-1"></i> 添加参展人员
                            </button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer p-4 flex justify-end gap-3">
                    <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300" onclick="$('#participantsModalContainer').addClass('hidden')">取消</button>
                    <button id="participantsSubmitBtn" type="button" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700" onclick="submitRegistration()">提交报名</button>
                </div>
            </div>
        </div>
        
        <!-- 确认模态框 -->
        <div id="confirmModalContainer" class="fixed inset-0 z-50 flex items-center justify-center modal-backdrop hidden">
            <div class="bg-white rounded-lg shadow-lg max-w-md w-full">
                <div class="modal-header p-6 border-b border-gray-200">
                    <h3 id="confirmModalTitle" class="text-lg font-bold text-gray-800">确认操作</h3>
                </div>
                <div id="confirmModalBody" class="modal-body p-6">
                    <!-- 确认内容将在这里动态添加 -->
                </div>
                <div class="modal-footer p-4 border-t border-gray-200 flex justify-end space-x-2">
                    <button id="cancelBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300" onclick="document.getElementById('confirmModalContainer').classList.add('hidden')">
                        取消
                    </button>
                    <button id="confirmBtn" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
                        确认
                    </button>
                </div>
            </div>
        </div>

        <!-- 报名详情模态框 -->
        <div id="registrationDetailContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
            <div class="modal-content bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] flex flex-col">
                <div class="modal-header p-4 flex justify-between items-center">
                    <h3 class="text-xl font-semibold text-gray-800">报名信息</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#registrationDetailContainer').addClass('hidden')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body p-6 overflow-y-auto" id="registrationDetailBody">
                    <!-- 报名详情内容将通过JS动态加载 -->
                </div>
                <div class="modal-footer p-4 flex justify-end gap-3">
                    <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300" onclick="$('#registrationDetailContainer').addClass('hidden')">关闭</button>
                    <button id="editParticipantsBtn" type="button" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700" onclick="editParticipants()">编辑参展人员</button>
                </div>
            </div>
        </div>

        <!-- 添加错误信息模态框 -->
        <div id="errorModalContainer" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
            <div class="error-modal-content bg-white rounded-lg shadow-xl w-full">
                <div class="modal-header p-4 flex justify-between items-center bg-red-50">
                    <h3 class="text-xl font-semibold text-red-700">
                        <i class="fas fa-exclamation-circle mr-2"></i>错误提示
                    </h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#errorModalContainer').addClass('hidden')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body p-6">
                    <div id="errorModalBody" class="text-gray-700"></div>
                </div>
                <div class="modal-footer p-4 flex justify-end">
                    <button type="button" class="px-6 py-2 bg-red-600 text-white rounded hover:bg-red-700" onclick="$('#errorModalContainer').addClass('hidden')">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script> -->
    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 0;
        let currentTab = 'all';
        let searchText = '';
        let startDate = '';
        let endDate = '';
        let schoolFilter = 'all';
        let participantCount = 1;
        let currentExhibitionId = null;
        
        // 页面加载完成后执行
        $(document).ready(function() {
            // 初始化加载书展列表
            loadExhibitions();
            
            // 加载学校筛选选项
            loadSchools();
            
            // 标签切换事件
            $('.status-tab').click(function() {
                // 移除所有标签的激活状态
                $('.status-tab').removeClass('border-blue-500 text-blue-600 bg-blue-50').addClass('text-gray-500 border-transparent');
                $('.status-tab span').removeClass('bg-blue-100 text-blue-600').addClass('bg-gray-100 text-gray-600');
                
                // 激活当前标签 - 设置背景色、文字颜色和底部边框
                $(this).removeClass('text-gray-500 border-transparent')
                       .addClass('border-blue-500 text-blue-600 bg-blue-50');
                
                // 激活当前标签的计数器样式
                $(this).find('span').removeClass('bg-gray-100 text-gray-600')
                                    .addClass('bg-blue-100 text-blue-600');
                
                // 设置当前标签状态
                const tabId = $(this).attr('id');
                if (tabId === 'allTab') currentTab = 'all';
                else if (tabId === 'publishedTab') currentTab = 'published';
                else if (tabId === 'upcomingTab') currentTab = 'upcoming';
                else if (tabId === 'registerableTab') currentTab = 'registerable';
                else if (tabId === 'registeredTab') currentTab = 'registered';
                else if (tabId === 'cancelledTab') currentTab = 'cancelled';
                else if (tabId === 'endedTab') currentTab = 'ended';
                
                // 重置分页并重新加载数据
                currentPage = 1;
                loadExhibitions();
            });
            
            // 在页面加载时添加日期筛选器变化的监听事件
            $('#customDateContainer').hide();
            
            // 监听日期筛选下拉框变化
            $('#dateFilter').change(function() {
                const value = $(this).val();
                
                // 只有当选择"自定义时间"选项时才显示日期选择器
                if (value === 'custom') {
                    $('#customDateContainer').show();
                } else {
                    // 其他选项时隐藏日期选择器并清空日期值
                    $('#customDateContainer').hide();
                    $('#startDate').val('');
                    $('#endDate').val('');
                    
                    // 执行筛选（不带自定义日期参数）
                    currentPage = 1;
                    loadExhibitions();
                }
            });
            
            // 日期选择器变化时的处理
            $('#startDate, #endDate').change(function() {
                startDate = $('#startDate').val();
                endDate = $('#endDate').val();
                
                // 验证结束日期不小于开始日期
                if (startDate && endDate && endDate < startDate) {
                    showMessage('结束日期不能早于开始日期', 'error');
                    $('#endDate').val('');
                    endDate = '';
                    return;
                }
                
                // 只有在自定义日期模式下且有日期输入时才执行筛选
                if ($('#dateFilter').val() === 'custom' && (startDate || endDate)) {
                    currentPage = 1;
                    loadExhibitions();
                }
            });
            
            // 学校筛选事件
            $('#schoolFilter').change(function() {
                schoolFilter = $(this).val();
                currentPage = 1;
                loadExhibitions();
            });
            
            // 搜索按钮点击事件
            $('#searchBtn').click(function() {
                searchText = $('#searchInput').val().trim();
                currentPage = 1;
                loadExhibitions();
            });
            
            // 搜索框回车事件
            $('#searchInput').keypress(function(e) {
                if (e.which === 13) {
                    searchText = $(this).val().trim();
                    currentPage = 1;
                    loadExhibitions();
                }
            });
            
            // 分页按钮事件
            $('#prevPageBtn').click(function() {
                if (currentPage > 1) {
                    currentPage--;
                    loadExhibitions();
                }
            });
            
            $('#nextPageBtn').click(function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    loadExhibitions();
                }
            });
            
            // 添加参展人员按钮事件
            $('#addParticipantBtn').click(function() {
                addParticipantField();
            });
            
            // 参展人员表单提交事件
            $('#participantsForm').submit(function(e) {
                e.preventDefault();
                submitRegistration();
            });

            // 使用事件委托来处理联系人单选按钮变化，而不是给每个按钮添加事件
            $(document).on('change', 'input[name="contactPerson"]', function() {
                updatePhoneRequirement();
            });
            
            // 添加性能优化：对输入框使用事件委托而不是直接绑定
            // 这样可以减少因为频繁创建和删除DOM元素导致的内存泄漏和性能问题
            $('#participantsForm').on('input', '.participant-name, .participant-phone', function() {
                // 输入框值变化时的处理逻辑可以放在这里
                // 现在是空的，仅为了示例
            });
        });
        
        // 这里只是占位符，后续API接口函数将在第二步实现...
        
        // 加载书展列表
        function loadExhibitions() {
            const tab = currentTab;
            const search = $('#searchInput').val();
            const dateFilter = $('#dateFilter').val();
            const schoolId = $('#schoolFilter').val();
            
            // 只有在自定义日期模式下才使用日期参数
            let startDateParam = '';
            let endDateParam = '';
            if (dateFilter === 'custom') {
                startDateParam = $('#startDate').val();
                endDateParam = $('#endDate').val();
            }
            
            // 显示加载中状态
            $('#exhibitionsContainer').html(`
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-spinner fa-spin text-2xl mb-3"></i>
                    <p>加载中，请稍候...</p>
                </div>
            `);
            
            // 构建API请求参数
            const params = {
                page: currentPage,
                limit: pageSize,
                tab: tab,
                search: search,
                start_date: startDateParam,
                end_date: endDateParam,
                school_id: schoolId
            };
            
            // 发送请求
            $.ajax({
                url: '/api/common/get_exhibitions',
                type: 'GET',
                data: params,
                success: function(response) {
                    if (response.code === 0) {
                        renderExhibitions(response.data);
                        updateStatusCounts(response.data.status_counts);
                        renderPagination(response.data.total);
                    } else {
                        showMessage(response.message || '加载失败', 'error');
                        $('#exhibitionsContainer').html(`
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-exclamation-triangle text-2xl mb-3 text-yellow-500"></i>
                                <p>${response.message || '加载失败'}</p>
                            </div>
                        `);
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后再试', 'error');
                    $('#exhibitionsContainer').html(`
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-exclamation-triangle text-2xl mb-3 text-yellow-500"></i>
                            <p>网络错误，请稍后再试</p>
                        </div>
                    `);
                }
            });
        }
        
        // 渲染书展列表
        function renderExhibitions(data) {
            const exhibitions = data.exhibitions;
            
            if (exhibitions.length === 0) {
                $('#exhibitionsContainer').html(`
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-search text-2xl mb-3"></i>
                        <p>没有找到符合条件的书展活动</p>
                    </div>
                `);
                return;
            }
            
            let html = '';
            
            exhibitions.forEach(exhibition => {
                // 获取活动状态信息
                const statusInfo = getExhibitionStatusInfo(exhibition);
                
                // 构建卡片HTML
                html += `
                    <div class="exhibition-card ${statusInfo.main.type} bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow">
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3">
                            <div class="flex items-center mb-2 sm:mb-0">
                                <h3 class="text-lg font-medium text-gray-800 mr-3">${exhibition.title}</h3>
                                <span class="status-badge ${statusInfo.main.class} mr-1">${statusInfo.main.text}</span>
                                ${statusInfo.secondary ? `<span class="status-badge ${statusInfo.secondary.class}">${statusInfo.secondary.text}</span>` : ''}
                            </div>
                            <div class="flex space-x-2 mt-2 sm:mt-0">
                                <button class="view-exhibition-btn bg-blue-50 text-blue-600 px-3 py-1 rounded-md hover:bg-blue-100 transition-colors" data-id="${exhibition.id}">
                                    <i class="fas fa-eye mr-1"></i>查看
                                </button>
                                ${exhibition.is_registered ? `
                                    <span class="bg-green-50 text-green-600 px-3 py-1 rounded-md">
                                        <i class="fas fa-check-circle mr-1"></i>已报名
                                    </span>
                                ` : ''}
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-3">
                            <div>
                                <p class="text-sm text-gray-500">发起单位</p>
                                <p class="text-gray-800">${exhibition.school_name}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">开始时间</p>
                                <p class="text-gray-800">${exhibition.start_time}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">结束时间</p>
                                <p class="text-gray-800">${exhibition.end_time}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">报名截止</p>
                                <p class="text-gray-800">${exhibition.registration_deadline}</p>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap justify-between items-center text-sm">
                            <div class="flex items-center text-gray-500">
                                ${exhibition.school_address ? `<span class="mr-4"><i class="fas fa-university mr-1"></i>${exhibition.school_address}</span>` : ''}
                                <span class="mr-4"><i class="fas fa-map-marker-alt mr-1"></i>${exhibition.location}</span>
                            </div>
                            <div class="flex items-center text-gray-500 mt-2 sm:mt-0">
                                <span class="mr-4"><i class="fas fa-user mr-1"></i>联系人: ${exhibition.contact_name}</span>
                                <span><i class="fas fa-clock mr-1"></i>创建于 ${exhibition.created_at}</span>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            $('#exhibitionsContainer').html(html);
            
            // 绑定查看按钮点击事件
            $('.view-exhibition-btn').click(function() {
                const id = $(this).data('id');
                viewExhibitionDetail(id);
            });
        }
        
        // 更新状态数量
        function updateStatusCounts(counts) {
            $('#allCount').text(counts.all || 0);
            $('#publishedCount').text(counts.published || 0);
            $('#upcomingCount').text(counts.upcoming || 0);
            $('#registerableCount').text(counts.registerable || 0);
            $('#registeredCount').text(counts.registered || 0);
            $('#cancelledCount').text(counts.cancelled || 0);
            $('#endedCount').text(counts.ended || 0);
        }
        
        // 渲染分页
        function renderPagination(total) {
            totalPages = Math.ceil(total / pageSize);
            
            // 禁用或启用上一页、下一页按钮
            $('#prevPageBtn').prop('disabled', currentPage <= 1);
            $('#nextPageBtn').prop('disabled', currentPage >= totalPages);
            
            // 生成页码
            let pageHtml = '';
            const maxPageButtons = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
            let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
            
            if (endPage - startPage + 1 < maxPageButtons) {
                startPage = Math.max(1, endPage - maxPageButtons + 1);
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentPage;
                pageHtml += `
                    <button class="page-number px-4 py-2 border-r border-gray-200 ${isActive ? 'bg-blue-50 text-blue-600 font-medium' : 'hover:bg-gray-50 text-gray-600'}" data-page="${i}">
                        ${i}
                    </button>
                `;
            }
            
            $('#pageNumbers').html(pageHtml);
            
            // 绑定页码点击事件
            $('.page-number').click(function() {
                currentPage = parseInt($(this).data('page'));
                loadExhibitions();
            });
        }
        
        // 获取状态信息
        function getStatusInfo(status) {
            const statusMap = {
                'draft': { text: '未发布', class: 'status-draft' },
                'published': { text: '进行中', class: 'status-published' },
                'cancelled': { text: '已取消', class: 'status-cancelled' },
                'ended': { text: '已结束', class: 'status-ended' }
            };
            
            return statusMap[status] || { text: '未知', class: '' };
        }
        
        // 检查是否可以报名
        function isRegisterable(exhibition) {
            if (!exhibition) return false;
            
            // 解析时间字符串为日期对象
            const now = new Date();
            const deadlineDate = exhibition.registration_deadline ? new Date(exhibition.registration_deadline) : null;
            
            // 如果没有设置截止时间或当前时间已经超过截止时间，则不可报名
            if (!deadlineDate || now > deadlineDate) {
                return false;
            }
            
            // 已经报过名的不能再次报名
            if (exhibition.is_registered) {
                return false;
            }
            
            return true;
        }
        
        // 获取活动状态信息
        function getExhibitionStatusInfo(exhibition) {
            const now = new Date();
            const startDate = exhibition.start_time ? new Date(exhibition.start_time) : null;
            const endDate = exhibition.end_time ? new Date(exhibition.end_time) : null;
            const deadlineDate = exhibition.registration_deadline ? new Date(exhibition.registration_deadline) : null;
            
            // 初始化状态数组，可以包含多个状态
            let statusInfo = {
                main: { text: '', class: '', type: '' },
                secondary: null
            };
            
            // 判断是否已结束
            if (exhibition.status === 'ended' || (endDate && now > endDate)) {
                statusInfo.main = { text: '已结束', class: 'status-ended', type: 'ended' };
                return statusInfo;
            }
            
            // 判断是否在进行中
            if (startDate && endDate && now >= startDate && now <= endDate) {
                statusInfo.main = { text: '进行中', class: 'status-published', type: 'published' };
            } else {
                // 不在进行中，判断是否即将开始
                if (startDate) {
                    const threeDaysInMs = 3 * 24 * 60 * 60 * 1000; // 3天的毫秒数
                    const timeDiff = startDate.getTime() - now.getTime();
                    
                    if (timeDiff > 0 && timeDiff <= threeDaysInMs) {
                        statusInfo.secondary = { text: '即将开始', class: 'bg-yellow-100 text-yellow-800', type: 'upcoming' };
                    }
                }
            }
            
            // 判断报名状态（无论活动状态如何）
            if (deadlineDate && now <= deadlineDate) {
                statusInfo.main = { text: '可报名', class: 'bg-green-100 text-green-800', type: 'registerable' };
            } else if (deadlineDate) {
                statusInfo.main = { text: '报名已截止', class: 'bg-red-100 text-red-800', type: 'deadline_passed' };
            }
            
            // 如果没有设置主状态，使用默认
            if (!statusInfo.main.text) {
                statusInfo.main = getStatusInfo(exhibition.status);
            }
            
            return statusInfo;
        }
        
        // 加载学校列表
        function loadSchools() {
            $.ajax({
                url: '/api/common/get_schools',
                type: 'GET',
                data: { has_exhibitions: true },
                success: function(response) {
                    if (response.code === 0 && response.data) {
                        let options = '<option value="all">全部学校</option>';
                        response.data.forEach(school => {
                            options += `<option value="${school.id}">${school.name}</option>`;
                        });
                        $('#schoolFilter').html(options);
                    }
                }
            });
        }
        
        // 格式化日期
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 显示消息提示
        function showMessage(message, type = 'success') {
            const id = Date.now();
            const typeClass = type === 'success' ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200';
            const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';
            
            const messageHtml = `
                <div id="message-${id}" class="message-toast ${typeClass} px-4 py-3 rounded-lg shadow-md border mb-3 animate-fadeIn">
                    <div class="flex items-center">
                        <i class="fas fa-${icon} mr-2"></i>
                        <span>${message}</span>
                    </div>
                </div>
            `;
            
            $('#messageContainer').append(messageHtml);
            
            // 3秒后自动移除
            setTimeout(function() {
                $(`#message-${id}`).removeClass('animate-fadeIn').addClass('animate-fadeOut');
                setTimeout(function() {
                    $(`#message-${id}`).remove();
                }, 300);
            }, 3000);
        }
        
        // 查看书展详情
        function viewExhibitionDetail(id) {
            $.ajax({
                url: '/api/common/get_exhibition_detail',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    if (response.code === 0) {
                        renderExhibitionDetail(response.data);
                        $('#detailModalContainer').removeClass('hidden');
                    } else {
                        showMessage(response.message || '获取详情失败', 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后再试', 'error');
                }
            });
        }
        
        // 渲染书展详情
        function renderExhibitionDetail(exhibition) {
            // 设置模态框标题
            $('#detailModalTitle').text(exhibition.title);
            
            // 获取状态信息
            const statusInfo = getExhibitionStatusInfo(exhibition);
            
            // 构建基本信息HTML
            let html = `
                <div class="space-y-6">
                    <div>
                        <span class="status-badge ${statusInfo.main.class} mb-2">${statusInfo.main.text}</span>
                        <h3 class="text-xl font-medium text-gray-800">${exhibition.title}</h3>
                        ${exhibition.logo_url ? `
                            <div class="flex justify-center mt-4 mb-2">
                                <img src="${exhibition.logo_url}" alt="活动Logo" class="object-contain max-h-48 border rounded p-2">
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h4 class="font-medium text-gray-700 mb-2">基本信息</h4>
                        <div class="space-y-2">
                            <div class="flex">
                                <span class="text-gray-500 w-24">发起单位:</span>
                                <span class="text-gray-800">${exhibition.school_name}</span>
                            </div>
                            ${exhibition.school_address ? `
                            <div class="flex">
                                <span class="text-gray-500 w-24">学校地址:</span>
                                <span class="text-gray-800">${exhibition.school_address}</span>
                            </div>
                            ` : ''}
                            <div class="flex">
                                <span class="text-gray-500 w-24">开始时间:</span>
                                <span class="text-gray-800">${exhibition.start_time}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">结束时间:</span>
                                <span class="text-gray-800">${exhibition.end_time}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">报名截止:</span>
                                <span class="text-gray-800">${exhibition.registration_deadline}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h4 class="font-medium text-gray-700 mb-2">发起人信息</h4>
                        <div class="space-y-2">
                            <div class="flex">
                                <span class="text-gray-500 w-24">姓名:</span>
                                <span class="text-gray-800">${exhibition.initiator.name}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">电话:</span>
                                <span class="text-gray-800">${exhibition.initiator.phone}</span>
                            </div>
                            ${exhibition.initiator.department ? `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">部门:</span>
                                    <span class="text-gray-800">${exhibition.initiator.department}</span>
                                </div>
                            ` : ''}
                            ${exhibition.initiator.position ? `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">职务:</span>
                                    <span class="text-gray-800">${exhibition.initiator.position}</span>
                                </div>
                            ` : ''}
                            ${exhibition.initiator.email ? `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">邮箱:</span>
                                    <span class="text-gray-800">${exhibition.initiator.email}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    
                    ${exhibition.description ? `
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium text-gray-700 mb-2">书展介绍</h4>
                            <p class="text-gray-800 whitespace-pre-line">${exhibition.description}</p>
                        </div>
                    ` : ''}
                    
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h4 class="font-medium text-gray-700 mb-2">活动地点和要求</h4>
                        <div class="space-y-2">
                            <div class="flex">
                                <span class="text-gray-500 w-24">地点:</span>
                                <span class="text-gray-800">${exhibition.location}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">允许停车:</span>
                                <span class="text-gray-800">${exhibition.allows_parking ? '是' : '否'}</span>
                            </div>
                            
                            ${exhibition.requires_campus_registration ? `
                                <div class="mt-2">
                                    <p class="text-gray-500 font-medium">进校报备要求:</p>
                                    <div class="bg-white p-3 rounded border border-gray-200 mt-1">
                                        <p class="text-gray-800 whitespace-pre-line">${exhibition.registration_requirements || '无特殊要求'}</p>
                                        ${exhibition.registration_qrcode ? `
                                            <div class="flex justify-center mt-2">
                                                <img src="${exhibition.registration_qrcode}" alt="报备二维码" class="max-h-48 object-contain border rounded p-2">
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            ` : `
                                <div class="flex">
                                    <span class="text-gray-500 w-24">进校报备:</span>
                                    <span class="text-gray-800">不需要报备</span>
                                </div>
                            `}
                            
                            ${exhibition.requirements ? `
                                <div class="mt-2">
                                    <p class="text-gray-500 font-medium">其他要求:</p>
                                    <div class="bg-white p-3 rounded border border-gray-200 mt-1">
                                        <p class="text-gray-800 whitespace-pre-line">${exhibition.requirements}</p>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
            
            // 设置详情内容
            $('#detailModalBody').html(html);
            
            // 根据是否已报名和是否可报名显示不同的按钮
            const canRegister = isRegisterable(exhibition);
            
            if (exhibition.is_registered) {
                // 已报名 - 显示查看和取消按钮
                $('#registerBtn').addClass('hidden');
                $('#viewParticipantsBtn').removeClass('hidden');
                $('#cancelRegistrationBtn').removeClass('hidden');
            } else if (canRegister) {
                // 未报名但可以报名 - 显示报名按钮
                $('#registerBtn').removeClass('hidden').prop('disabled', false);
                $('#viewParticipantsBtn').addClass('hidden');
                $('#cancelRegistrationBtn').addClass('hidden');
            } else {
                // 不可报名（已截止）- 显示禁用的报名按钮
                $('#registerBtn').removeClass('hidden').prop('disabled', true).text('报名已截止');
                $('#viewParticipantsBtn').addClass('hidden');
                $('#cancelRegistrationBtn').addClass('hidden');
            }

            // 保存当前展览ID，用于后续操作
            currentExhibitionId = exhibition.id;
        }
        
        // 查看报名详情
        function viewRegistrationDetail() {
            const exhibitionId = currentExhibitionId;
            
            $.ajax({
                url: '/api/common/get_registration_detail',
                type: 'GET',
                data: { exhibition_id: exhibitionId },
                success: function(response) {
                    if (response.code === 0) {
                        // 显示模态框
                        $('#registrationDetailContainer').removeClass('hidden');
                        
                        // 将参展人员信息填充到模态框
                        renderRegistrationDetail(response.data);
                        
                        // 显示成功消息
                        showMessage('成功获取报名信息', 'success');
                    } else {
                        // 显示错误消息
                        showErrorModal(response.message || '获取报名信息失败');
                    }
                },
                error: function() {
                    showErrorModal('网络错误，请稍后再试');
                }
            });
        }
        
        // 渲染报名详情
        function renderRegistrationDetail(data) {
            let html = `
                <div class="space-y-4">
                    <div class="bg-gray-50 p-3 rounded-md">
                        <h4 class="font-medium text-gray-700 mb-2">报名信息</h4>
                        <div class="space-y-2">
                            <div class="flex">
                                <span class="text-gray-500 w-24">报名时间:</span>
                                <span class="text-gray-800">${data.registration.created_at}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">状态:</span>
                                <span class="text-gray-800">${data.registration.status === 'registered' ? '已报名' : '已取消'}</span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="font-medium text-gray-700 mb-2">参展人员</h4>
                        <div class="space-y-3">
            `;

            data.participants.forEach((participant, index) => {
                html += `
                    <div class="bg-gray-50 p-3 rounded-md">
                        <div class="flex justify-between mb-1">
                            <h5 class="font-medium">参展人员 ${index+1}</h5>
                            ${participant.is_contact ? '<span class="text-blue-600 text-sm font-medium">联系人</span>' : ''}
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                            <div class="flex">
                                <span class="text-gray-500 w-20">姓名:</span>
                                <span class="text-gray-800">${participant.name}</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-20">电话:</span>
                                <span class="text-gray-800">${participant.phone}</span>
                            </div>
                            ${participant.role ? `
                            <div class="flex md:col-span-2">
                                <span class="text-gray-500 w-20">角色:</span>
                                <span class="text-gray-800">${participant.role}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });

            html += `
                        </div>
                    </div>
                </div>
            `;

            $('#registrationDetailBody').html(html);
        }

        // 编辑参展人员
        function editParticipants() {
            // 隐藏报名详情模态框
            $('#registrationDetailContainer').addClass('hidden');
            
            // 显示参展人员模态框
            $('#participantsModalContainer').removeClass('hidden');
            
            // 设置模态框标题和按钮文本
            $('#participantsModalTitle').text('编辑参展人员');
            $('#participantsSubmitBtn').text('保存修改').attr('data-is-edit', 'true');
            
            // 获取当前展览ID
            const exhibitionId = currentExhibitionId;
            
            // 获取现有参展人员信息填充表单
            $.ajax({
                url: '/api/common/get_registration_detail',
                type: 'GET',
                data: { exhibition_id: exhibitionId },
                success: function(response) {
                    if (response.code === 0) {
                        // 填充参展人员表单
                        fillParticipantsForm(response.data.participants);
                    } else {
                        showErrorModal(response.message || '获取参展人员信息失败');
                        $('#participantsModalContainer').addClass('hidden');
                    }
                },
                error: function() {
                    showErrorModal('网络错误，请稍后再试');
                    $('#participantsModalContainer').addClass('hidden');
                }
            });
        }

        // 填充参展人员表单
        function fillParticipantsForm(participants) {
            // 清空现有表单
            $('#participantsList').empty();
            participantCount = 0; // 重置计数器
            
            // 如果没有参展人员数据，添加一个默认的空表单
            if (!participants || participants.length === 0) {
                addParticipantField();
                return;
            }
            
            // 添加每个参展人员
            participants.forEach((participant, index) => {
                addParticipantField(); // 这会增加participantCount并添加表单字段
                
                // 填充表单字段
                const currentIndex = participantCount - 1;
                $(`input[name="participantName_${currentIndex}"]`).val(participant.name);
                $(`input[name="participantPhone_${currentIndex}"]`).val(participant.phone || '');
                $(`input[name="participantRole_${currentIndex}"]`).val(participant.role || '');
                
                // 如果是联系人，选中对应的单选按钮
                if (participant.is_contact) {
                    $(`input[name="contactPerson"][value="${currentIndex}"]`).prop('checked', true);
                }
            });
            
            // 更新手机号必填状态
            updatePhoneRequirement();
        }
        
        // 更新手机号要求文本 - 优化版本
        function updatePhoneRequirement() {
            const contactPersonIndex = parseInt($('input[name="contactPerson"]:checked').val());
            
            // 批量更新而不是一个一个更新
            $('.participant-item').each(function(index) {
                const phoneLabel = $(this).find('.phone-label');
                const phoneInput = $(this).find(`input[name^="participantPhone_"]`);
                
                if (!phoneLabel.length || !phoneInput.length) return;
                
                const isContact = (index === contactPersonIndex);
                
                // 只在状态变化时更新，减少DOM操作
                const labelText = isContact ? '手机号码 (必填)' : '手机号码 (选填)';
                if (phoneLabel.text() !== labelText) {
                    phoneLabel.text(labelText);
                }
                
                if (isContact && !phoneInput.prop('required')) {
                    phoneInput.prop('required', true);
                } else if (!isContact && phoneInput.prop('required')) {
                    phoneInput.prop('required', false);
                }
            });
        }
        
        // 验证参展人员表单
        function validateParticipantsForm() {
            // 检查是否有参展人员
            if ($('.participant-item').length === 0) {
                showErrorModal('请至少添加一名参展人员');
                return false;
            }
            
            // 检查是否选择了联系人
            if ($('input[name="contactPerson"]:checked').length === 0) {
                showErrorModal('请指定一名联系人');
                return false;
            }
            
            const contactPersonIndex = parseInt($('input[name="contactPerson"]:checked').val());
            
            // 验证每个参展人员的必填字段
            let isValid = true;
            $('.participant-item').each(function(index) {
                const nameInput = $(this).find('input[type="text"][name^="participantName"]');
                const phoneInput = $(this).find('input[type="tel"][name^="participantPhone"]');
                
                if (!nameInput.val()) {
                    showErrorModal('请填写所有参展人员的姓名');
                    isValid = false;
                    return false;
                }
                
                // 联系人必须填写手机号
                if (index === contactPersonIndex) {
                    if (!phoneInput.val()) {
                        showErrorModal('请填写联系人的手机号');
                        isValid = false;
                        return false;
                    }
                    
                    // 验证手机号格式
                    const phoneRegex = /^1[3-9]\d{9}$/;
                    if (!phoneRegex.test(phoneInput.val())) {
                        showErrorModal('请输入正确的联系人手机号格式');
                        isValid = false;
                        return false;
                    }
                } else if (phoneInput.val()) {
                    // 非联系人如果填写了手机号，也需验证格式
                    const phoneRegex = /^1[3-9]\d{9}$/;
                    if (!phoneRegex.test(phoneInput.val())) {
                        showErrorModal('请输入正确的手机号格式');
                        isValid = false;
                        return false;
                    }
                }
            });
            
            return isValid;
        }
        
        // 显示确认模态框
        function showConfirmModal(title, message, callback) {
            // 设置标题和内容
            $('#confirmModalTitle').text(title);
            $('#confirmModalBody').html(`<p class="text-gray-700">${message}</p>`);
            
            // 绑定确认按钮点击事件
            $('#confirmBtn').off('click').on('click', callback);
            
            // 显示模态框
            $('#confirmModalContainer').removeClass('hidden');
        }

        // 显示错误提示模态框
        function showErrorModal(errorMessage) {
            $('#errorModalBody').html(errorMessage);
            $('#errorModalContainer').removeClass('hidden');
        }

        // 提交参展报名
        function submitRegistration() {
            // 阻止表单重复提交
            const submitBtn = $('#participantsSubmitBtn');
            if (submitBtn.prop('disabled')) {
                return;
            }
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>提交中...');

            // 验证表单是否存在参展人员
            if ($('.participant-item').length === 0) {
                showErrorModal('请至少添加一名参展人员');
                submitBtn.prop('disabled', false).html('确认参展');
                return;
            }

            // 检查是否选择了联系人
            if ($('input[name="contactPerson"]:checked').length === 0) {
                showErrorModal('请指定一名联系人');
                submitBtn.prop('disabled', false).html('确认参展');
                return;
            }

            // 获取联系人索引（基于DOM顺序而非原始索引）
            let contactPersonValue = $('input[name="contactPerson"]:checked').val();
            let contactPersonDomIndex = -1;
            $('.participant-item').each(function(domIndex) {
                if ($(this).find(`input[name="contactPerson"][value="${contactPersonValue}"]`).length > 0) {
                    contactPersonDomIndex = domIndex;
                    return false; // 中断 each 循环
                }
            });
            
            // 构建参展人员数据
            const participants = [];
            let validationError = null;
            
            $('.participant-item').each(function(domIndex) {
                // 获取此DOM元素中的字段（不再依赖原始索引）
                const nameInput = $(this).find('input[type="text"][name^="participantName"]');
                const phoneInput = $(this).find('input[type="tel"][name^="participantPhone"]');
                const roleInput = $(this).find('input[type="text"][name^="participantRole"]');
                
                const name = nameInput.val();
                let phone = phoneInput.val();
                const role = roleInput.val() || '';
                const isContact = (domIndex === contactPersonDomIndex);
                
                // 必须填写姓名
                if (!name || name.trim() === '') {
                    validationError = '请填写所有参展人员的姓名';
                    return false; // 中断each循环
                }
                
                // 检查联系人手机号是否为空
                if (isContact && (!phone || phone.trim() === '')) {
                    validationError = '联系人的手机号码不能为空';
                    return false; // 中断each循环
                }
                
                // 检查手机号格式 (如果提供了手机号)
                if (phone && phone.trim() !== '') {
                    const phonePattern = /^1[3-9]\d{9}$/;
                    if (!phonePattern.test(phone)) {
                        validationError = '手机号格式不正确，请输入11位数字，以1开头';
                        return false; // 中断each循环
                    }
                }
                
                // 对于非联系人，如果手机号为空，设置为空字符串
                if (!isContact && (!phone || phone.trim() === '')) {
                    phone = '';
                }
                
                participants.push({
                    name: name,
                    phone: phone,
                    role: role,
                    is_contact: isContact
                });
            });
            
            // 如果验证出错，显示错误并返回
            if (validationError) {
                showErrorModal(validationError);
                submitBtn.prop('disabled', false).html('确认参展');
                return;
            }
            
            // 判断是新报名还是编辑报名
            const isEdit = $('#participantsSubmitBtn').attr('data-is-edit') === 'true';
            const buttonTextOriginal = submitBtn.text();
            
            // 发送AJAX请求
            $.ajax({
                url: '/api/common/register_exhibition',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    exhibition_id: currentExhibitionId,
                    participants: participants
                }),
                success: function(response) {
                    if (response.code === 0) {
                        // 关闭modal
                        $('#participantsModalContainer').addClass('hidden');
                        
                        // 显示成功消息
                        const successMsg = isEdit ? '参展人员信息已更新！' : '参展报名成功！';
                        showMessage(response.message || successMsg, 'success');
                        
                        // 刷新当前书展详情
                        viewExhibitionDetail(currentExhibitionId);
                        
                        // 如果是编辑操作，更新报名详情
                        if (isEdit && $('#registrationDetailContainer').length > 0) {
                            viewRegistrationDetail();
                        }
                        
                        // 重新加载展会列表
                        loadExhibitions();
                    } else {
                        showErrorModal(response.message || '报名失败，请稍后重试');
                    }
                    submitBtn.prop('disabled', false).html(buttonTextOriginal);
                },
                error: function(xhr) {
                    let errorMsg = '报名失败，请稍后重试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    showErrorModal(errorMsg);
                    submitBtn.prop('disabled', false).html(buttonTextOriginal);
                }
            });
        }

        // 添加参展人员字段 - 优化版本
        function addParticipantField() {
            // 防止过快重复点击
            const addBtn = $('button[onclick="addParticipantField()"]');
            if (addBtn.prop('disabled')) return;
            
            // 临时禁用按钮，防止重复点击
            addBtn.prop('disabled', true);
            setTimeout(() => addBtn.prop('disabled', false), 300);
            
            // 重置参展人员计数器，如果是重新报名且当前没有任何参展人员
            if ($('.participant-item').length === 0) {
                participantCount = 0;
            }
            
            // 递增参展人员计数器
            participantCount++;
            
            // 创建参展人员表单HTML - 优化版，减少不必要的DOM属性和事件绑定
            const participantHtml = `
                <div class="participant-item bg-gray-50 rounded-lg p-4 relative">
                    <div class="absolute right-2 top-2">
                        <button type="button" class="text-gray-400 hover:text-red-500" onclick="removeParticipant(this)">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="mb-3">
                        <h4 class="text-gray-700 font-medium mb-1">参展人员 ${participantCount}</h4>
                        <div class="mb-3">
                            <div class="flex items-center">
                                <input type="radio" name="contactPerson" id="contactPerson_${participantCount-1}" value="${participantCount-1}" 
                                       class="contact-radio w-4 h-4 text-blue-600" 
                                       ${participantCount === 1 ? 'checked' : ''}>
                                <label for="contactPerson_${participantCount-1}" class="ml-2 text-sm font-medium text-gray-700">
                                    指定为联系人
                                </label>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">联系人是书展主办方联系的对象，必须提供手机号</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">姓名 <span class="text-red-500">*</span></label>
                            <input type="text" name="participantName_${participantCount-1}" required
                                   class="participant-name w-full px-3 py-2 border border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="phone-label block text-sm font-medium text-gray-700 mb-1">手机号码 ${participantCount === 1 ? '(必填)' : '(选填)'}</label>
                            <input type="tel" name="participantPhone_${participantCount-1}" ${participantCount === 1 ? 'required' : ''}
                                   class="participant-phone w-full px-3 py-2 border border-gray-300 rounded-md">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">角色/职务</label>
                            <input type="text" name="participantRole_${participantCount-1}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md"
                                   placeholder="可选，如：销售经理、产品经理等">
                        </div>
                    </div>
                </div>
            `;
            
            // 添加到参展人员列表
            $('#participantsList').append(participantHtml);
            
            // 延迟更新手机号必填状态，减少卡顿
            setTimeout(updatePhoneRequirement, 50);
        }
        
        // 移除参展人员
        function removeParticipant(btnElement) {
            // 找到要移除的表单项
            const participantItem = $(btnElement).closest('.participant-item');
            
            // 检查是否为联系人
            const isContact = participantItem.find('input[name="contactPerson"]:checked').length > 0;
            
            // 移除表单项
            participantItem.remove();
            
            // 重新编号所有参展人员
            $('.participant-item').each(function(index) {
                // 更新标题
                $(this).find('h4').text(`参展人员 ${index + 1}`);
            });
            
            // 如果移除的是联系人，且还有其他参展人员，则自动选择第一个人员为联系人
            if (isContact && $('.participant-item').length > 0) {
                $('.participant-item:first input[name="contactPerson"]').prop('checked', true);
            }
            
            // 更新手机号必填状态
            updatePhoneRequirement();
        }

        // 显示参展人员模态框
        function showParticipantsModal() {
            // 检查是否可以报名
            $.ajax({
                url: '/api/common/get_exhibition_detail',
                type: 'GET',
                data: { id: currentExhibitionId },
                success: function(response) {
                    if (response.code === 0) {
                        const exhibition = response.data;
                        const canRegister = isRegisterable(exhibition);
                        
                        if (!canRegister) {
                            showErrorModal('报名已截止，无法完成报名');
                            return;
                        }
                        
                        // 清空现有参与者
                        $('#participantsList').empty();
                        participantCount = 0;
                        
                        // 添加一个默认的参展人员字段
                        addParticipantField();
                        
                        // 设置展览ID
                        $('#exhibitionId').val(currentExhibitionId);
                        
                        // 设置模态框标题和按钮文本
                        $('#participantsModalTitle').text('报名参展');
                        $('#participantsSubmitBtn').text('提交报名').removeAttr('data-is-edit');
                        
                        // 显示模态框
                        $('#participantsModalContainer').removeClass('hidden');
                    } else {
                        showErrorModal(response.message || '获取书展信息失败');
                    }
                },
                error: function() {
                    showErrorModal('网络错误，请稍后再试');
                }
            });
        }
        
        // 取消报名确认
        function showCancelConfirm() {
            showConfirmModal(
                '取消报名确认',
                '您确定要取消此书展的报名吗？取消后您可以随时重新报名。',
                cancelRegistration
            );
        }
        
        // 取消报名
        function cancelRegistration() {
            // 隐藏确认框
            $('#confirmModalContainer').addClass('hidden');
            
            // 发送取消请求
            $.ajax({
                url: '/api/common/cancel_registration',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ exhibition_id: currentExhibitionId }),
                success: function(response) {
                    if (response.code === 0) {
                        // 关闭详情模态框
                        $('#detailModalContainer').addClass('hidden');
                        
                        // 显示成功信息
                        showMessage(response.message || '报名已成功取消', 'success');
                        
                        // 重新加载书展列表
                        loadExhibitions();
                    } else {
                        showMessage(response.message || '取消报名失败', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    let errorMsg = '网络错误，请稍后重试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = '取消报名失败: ' + xhr.responseJSON.message;
                    } else if (xhr.status) {
                        errorMsg = `取消报名失败: ${xhr.status} ${error}`;
                    }
                    showMessage(errorMsg, 'error');
                }
            });
        }
    </script>
</body>
</html> 