import smtplib
import logging
import random
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from email.utils import formataddr
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
import sys
import os
import base64
import re

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.config import get_db_connection

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EmailService:
    """邮件服务类 - 提供统一的邮件发送功能"""
    
    def __init__(self):
        self.configs = []
        self._load_config()
    
    def _validate_config(self, config) -> bool:
        """验证邮件配置是否有效"""
        try:
            # 验证必要字段存在
            required_fields = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'from_email']
            for field in required_fields:
                if field not in config or not config[field]:
                    logger.warning(f"邮件配置 ID:{config['id']} 缺少必要字段 {field}")
                    return False
                    
            # 验证端口是整数
            if not isinstance(config['smtp_port'], int):
                logger.warning(f"邮件配置 ID:{config['id']} 的端口不是有效数字")
                return False
                
            # 可以添加更多验证，如邮箱格式验证等
            return True
        except Exception as e:
            logger.warning(f"验证邮件配置失败 ID:{config.get('id', 'unknown')}: {e}")
            return False
    
    def _load_config(self) -> None:
        """从数据库加载所有启用的邮件配置并验证有效性"""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM email_config WHERE is_active = 1")
                all_configs = cursor.fetchall()
                
                if not all_configs:
                    logger.error("没有找到活跃的邮件配置")
                    return
                
                # 验证配置有效性
                valid_configs = []
                for config in all_configs:
                    if self._validate_config(config):
                        valid_configs.append(config)
                        logger.info(f"有效邮件配置: ID:{config['id']}, {config['from_email']} ({config['smtp_host']})")
                    else:
                        logger.warning(f"无效邮件配置: ID:{config['id']}, {config['from_email']} ({config['smtp_host']})")
                
                if not valid_configs:
                    logger.error("没有找到有效的邮件配置")
                    return
                    
                self.configs = valid_configs
                logger.info(f"成功加载 {len(valid_configs)}/{len(all_configs)} 个有效邮件配置")
                
        except Exception as e:
            logger.error(f"加载邮件配置失败: {e}")
    
    def _get_email_template(self, title: str, content: str, email_type: str = 'default') -> str:
        """生成邮件HTML模板"""
        
        # 根据邮件类型选择主题色
        color_schemes = {
            'default': {
                'primary': '#3b82f6',     # 蓝色
                'bg': '#f8fafc',
                'border': '#e2e8f0'
            },
            'notification': {
                'primary': '#3b82f6',     # 蓝色
                'bg': '#f0f9ff',
                'border': '#bfdbfe'
            },
            'warning': {
                'primary': '#f59e0b',     # 橙色
                'bg': '#fffbeb',
                'border': '#fed7aa'
            },
            'success': {
                'primary': '#10b981',     # 绿色
                'bg': '#f0fdf4',
                'border': '#bbf7d0'
            },
            'error': {
                'primary': '#ef4444',     # 红色
                'bg': '#fef2f2',
                'border': '#fecaca'
            }
        }
        
        colors = color_schemes.get(email_type, color_schemes['default'])
        current_time = datetime.now().strftime('%Y年%m月%d日 %H:%M')
        
        template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{title}</title>
            <style>
                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
                    line-height: 1.6;
                    color: #374151;
                    background-color: #f3f4f6;
                    padding: 20px;
                }}
                .email-container {{
                    max-width: 600px;
                    margin: 0 auto;
                    background-color: white;
                    border-radius: 12px;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    overflow: hidden;
                }}
                .email-header {{
                    background: linear-gradient(135deg, {colors['primary']}, {colors['primary']}dd);
                    color: white;
                    padding: 24px;
                    text-align: center;
                }}
                .email-header h1 {{
                    font-size: 24px;
                    font-weight: 600;
                    margin-bottom: 8px;
                }}
                .email-header .logo {{
                    font-size: 16px;
                    opacity: 0.9;
                }}
                .email-body {{
                    padding: 32px 24px;
                    background-color: {colors['bg']};
                }}
                .email-content {{
                    background-color: white;
                    padding: 24px;
                    border-radius: 8px;
                    border-left: 4px solid {colors['primary']};
                    margin-bottom: 24px;
                }}
                .email-content p {{
                    margin-bottom: 16px;
                    font-size: 16px;
                    line-height: 1.7;
                }}
                .email-content p:last-child {{
                    margin-bottom: 0;
                }}
                .email-footer {{
                    padding: 24px;
                    text-align: center;
                    background-color: #f9fafb;
                    border-top: 1px solid {colors['border']};
                }}
                .email-footer p {{
                    font-size: 14px;
                    color: #6b7280;
                    margin-bottom: 8px;
                }}
                .email-time {{
                    font-size: 13px;
                    color: #9ca3af;
                }}
                .highlight {{
                    background-color: {colors['primary']}22;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-weight: 500;
                }}
                @media only screen and (max-width: 600px) {{
                    body {{
                        padding: 10px;
                    }}
                    .email-header {{
                        padding: 20px 16px;
                    }}
                    .email-header h1 {{
                        font-size: 20px;
                    }}
                    .email-body {{
                        padding: 24px 16px;
                    }}
                    .email-content {{
                        padding: 20px;
                    }}
                    .email-footer {{
                        padding: 20px 16px;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="email-header">
                    <h1>{title}</h1>
                    <div class="logo">📚 样书管理系统</div>
                </div>
                
                <div class="email-body">
                    <div class="email-content">
                        {self._format_content(content)}
                    </div>
                </div>
                
                <div class="email-footer">
                    <p>此邮件由系统自动发送，请勿直接回复</p>
                    <p>如有疑问，请联系系统管理员</p>
                    <div class="email-time">发送时间：{current_time}</div>
                </div>
            </div>
        </body>
        </html>
        """
        
        return template
    
    def _format_content(self, content: str) -> str:
        """格式化邮件内容，将纯文本转换为HTML段落"""
        if not content:
            return ""
        
        # 将换行符转换为段落
        paragraphs = content.split('\n\n')
        html_content = ""
        
        for paragraph in paragraphs:
            if paragraph.strip():
                # 处理单个换行符为<br>
                formatted_paragraph = paragraph.replace('\n', '<br>')
                html_content += f"<p>{formatted_paragraph}</p>"
        
        return html_content
    
    def _format_sender_address(self, name: str, email: str) -> str:
        """根据RFC2047标准格式化发件人地址
        
        Args:
            name: 发件人昵称
            email: 发件人邮箱地址
            
        Returns:
            str: 格式化后的发件人地址
                - 如果昵称为空，直接返回邮箱地址
                - 如果昵称为ASCII字符，返回 "name <email>" 格式
                - 如果昵称包含非ASCII字符，返回编码后的 "=?UTF-8?B?...?= <email>" 格式
        """
        # 检查昵称是否为空
        if not name or name.strip() == "":
            # 昵称为空，直接返回邮箱地址
            return email
            
        # 检查昵称是否包含非ASCII字符
        is_ascii = all(ord(c) < 128 for c in name)
        
        if is_ascii:
            # 纯ASCII字符则直接使用formataddr
            return formataddr((name, email))
        else:
            # 包含非ASCII字符，使用Header编码
            encoded_name = Header(name, 'utf-8').encode()
            return formataddr((encoded_name, email))
    
    def _get_random_config(self) -> Optional[Dict]:
        """随机选择一个邮件配置"""
        if not self.configs:
            return None
        return random.choice(self.configs)

    def _create_smtp_connection(self, config: Dict) -> smtplib.SMTP:
        """根据配置创建合适的SMTP连接

        Args:
            config: 邮件配置字典

        Returns:
            smtplib.SMTP: 配置好的SMTP服务器连接

        Raises:
            Exception: 连接失败时抛出异常
        """
        smtp_host = config['smtp_host']
        smtp_port = config['smtp_port']
        use_tls = config.get('use_tls', 1)
        use_ssl = config.get('use_ssl', 0)

        logger.info(f"创建SMTP连接: {smtp_host}:{smtp_port}, TLS={use_tls}, SSL={use_ssl}")

        # 根据端口和配置智能选择连接方式
        if smtp_port == 465:
            # 465端口：标准SSL端口，使用SMTP_SSL
            logger.debug(f"使用SSL连接到 {smtp_host}:{smtp_port} (465端口)")
            server = smtplib.SMTP_SSL(smtp_host, smtp_port)
            server.ehlo()
        elif smtp_port == 587:
            # 587端口：STARTTLS端口，使用SMTP + STARTTLS
            logger.debug(f"使用STARTTLS连接到 {smtp_host}:{smtp_port} (587端口)")
            server = smtplib.SMTP(smtp_host, smtp_port)
            server.ehlo()
            server.starttls()
            server.ehlo()
        elif use_ssl:
            # 其他端口但启用了SSL，使用SMTP_SSL
            logger.debug(f"使用SSL连接到 {smtp_host}:{smtp_port} (SSL配置)")
            server = smtplib.SMTP_SSL(smtp_host, smtp_port)
            server.ehlo()
        elif use_tls:
            # 其他端口但启用了TLS，使用STARTTLS
            logger.debug(f"使用STARTTLS连接到 {smtp_host}:{smtp_port} (TLS配置)")
            server = smtplib.SMTP(smtp_host, smtp_port)
            server.ehlo()
            server.starttls()
            server.ehlo()
        else:
            # 普通连接（不推荐）
            logger.debug(f"使用普通连接到 {smtp_host}:{smtp_port}")
            server = smtplib.SMTP(smtp_host, smtp_port)
            server.ehlo()

        return server
    
    def send_email(self, 
                   to_emails: List[str], 
                   subject: str, 
                   content: str, 
                   email_type: str = 'default',
                   cc_emails: Optional[List[str]] = None,
                   bcc_emails: Optional[List[str]] = None) -> Dict[str, Any]:
        """发送邮件
        
        Args:
            to_emails: 收件人邮箱列表
            subject: 邮件主题
            content: 邮件内容
            email_type: 邮件类型 ('default', 'notification', 'warning', 'success', 'error')
            cc_emails: 抄送邮箱列表
            bcc_emails: 密送邮箱列表
            
        Returns:
            Dict: 包含发送结果的字典
        """
        # 检查配置
        if not self.configs:
            logger.error("没有可用的邮件配置")
            return {
                'success': False,
                'message': '没有可用的邮件配置',
                'sent_count': 0,
                'failed_emails': to_emails,
                'total_recipients': len(to_emails)
            }
        
        # 随机选择一个邮件配置
        config = random.choice(self.configs)
        logger.info(f"选择邮箱 {config['from_email']} (ID:{config['id']}) 发送邮件给 {', '.join(to_emails)}")
            
        # 检查收件人
        if not to_emails:
            logger.warning("收件人列表为空")
            return {
                'success': False,
                'message': '收件人列表为空',
                'sent_count': 0,
                'failed_emails': [],
                'total_recipients': 0
            }
            
        # 生成HTML内容
        html_content = self._get_email_template(subject, content, email_type)
        
        # 初始化邮件消息
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = self._format_sender_address(config['from_name'], config['from_email'])
        msg['To'] = ", ".join(to_emails)
        
        # 添加抄送
        if cc_emails:
            msg['Cc'] = ", ".join(cc_emails)
            
        # 添加纯文本和HTML内容
        text_part = MIMEText(content, 'plain', 'utf-8')
        html_part = MIMEText(html_content, 'html', 'utf-8')
        msg.attach(text_part)
        msg.attach(html_part)
            
        # 准备所有收件人列表
        all_recipients = to_emails.copy()
        if cc_emails:
            all_recipients.extend(cc_emails)
        if bcc_emails:
            all_recipients.extend(bcc_emails)
            
        try:
            # 根据配置选择合适的SMTP连接方式
            server = self._create_smtp_connection(config)

            # 登录
            server.login(config['smtp_username'], config['smtp_password'])
                
            # 发送邮件
            sent_count = 0
            failed_emails = []
            total = len(all_recipients)
            
            for recipient in all_recipients:
                try:
                    server.sendmail(config['from_email'], recipient, msg.as_string())
                    sent_count += 1
                except Exception as e:
                    logger.error(f"发送邮件给 {recipient} 失败: {e}")
                    failed_emails.append(recipient)
            
            server.quit()
            
            # 记录发送结果，包含使用的邮箱信息
            sender_name = config['from_name'] or config['from_email']
            logger.info(f"邮件从 '{sender_name}' <{config['from_email']}> (ID:{config['id']}) 发送完成，成功: {sent_count}/{total} 个收件人")
            
            return {
                'success': True,
                'message': f"邮件发送完成，成功: {sent_count}/{total} 个收件人",
                'sent_count': sent_count,
                'failed_emails': failed_emails,
                'total_recipients': total,
                'sender_email': config['from_email'],
                'sender_id': config['id']
            }
            
        except smtplib.SMTPAuthenticationError as e:
            error_msg = f"邮箱 {config['from_email']} (ID:{config['id']}) SMTP认证失败: {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'sent_count': 0,
                'failed_emails': to_emails,
                'total_recipients': len(to_emails),
                'sender_email': config['from_email'],
                'sender_id': config['id']
            }
        except smtplib.SMTPServerDisconnected as e:
            error_msg = f"邮箱 {config['from_email']} (ID:{config['id']}) SMTP服务器连接断开: {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'sent_count': 0,
                'failed_emails': to_emails,
                'total_recipients': len(to_emails),
                'sender_email': config['from_email'],
                'sender_id': config['id']
            }
        except Exception as e:
            error_msg = f"邮箱 {config['from_email']} (ID:{config['id']}) 发送邮件失败: {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'sent_count': 0,
                'failed_emails': to_emails,
                'total_recipients': len(to_emails),
                'sender_email': config['from_email'],
                'sender_id': config['id']
            }

    # 便捷方法
    def send_notification(self, to_emails: List[str], subject: str, content: str) -> Dict[str, Any]:
        """发送通知邮件"""
        return self.send_email(to_emails, subject, content, 'notification')
    
    def send_warning(self, to_emails: List[str], subject: str, content: str) -> Dict[str, Any]:
        """发送警告邮件"""
        return self.send_email(to_emails, subject, content, 'warning')
    
    def send_success(self, to_emails: List[str], subject: str, content: str) -> Dict[str, Any]:
        """发送成功通知邮件"""
        return self.send_email(to_emails, subject, content, 'success')
    
    def send_error(self, to_emails: List[str], subject: str, content: str) -> Dict[str, Any]:
        """发送错误通知邮件"""
        return self.send_email(to_emails, subject, content, 'error')
    
    def reload_config(self) -> bool:
        """重新加载邮件配置"""
        try:
            self._load_config()
            return len(self.configs) > 0
        except Exception as e:
            logger.error(f"重新加载配置失败: {e}")
            return False
            
    def get_config_list(self) -> List[Dict]:
        """获取所有可用配置的简要信息"""
        return [{
            'id': config['id'],
            'smtp_username': config['smtp_username'], 
            'from_email': config['from_email'],
            'from_name': config['from_name'],
            'is_active': config['is_active']
        } for config in self.configs]
    
    def test_connection(self) -> Dict[str, Any]:
        """测试SMTP连接"""
        if not self.configs:
            return {
                'success': False,
                'message': '邮件配置未加载'
            }
        
        try:
            # 随机选择一个配置进行测试
            config = random.choice(self.configs)
            username = config['smtp_username']
            password = config['smtp_password']

            logger.info(f"测试邮箱配置 {config['from_email']} (ID:{config['id']}) 的SMTP连接")

            # 使用统一的连接创建方法
            server = self._create_smtp_connection(config)

            server.login(username, password)
            server.quit()
            
            logger.info(f"邮箱 {config['from_email']} (ID:{config['id']}) SMTP连接测试成功")
            
            return {
                'success': True,
                'message': f"邮箱 {config['from_email']} SMTP连接测试成功",
                'config_id': config['id'],
                'email': config['from_email']
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'SMTP连接测试失败: {str(e)}'
            }


# 全局邮件服务实例
email_service = EmailService()

# 便捷调用函数
def send_email(to_emails, subject, content, email_type='default'):
    """
    发送邮件的便捷函数
    
    Args:
        to_emails: 收件人邮箱（字符串或列表）
        subject: 邮件主题
        content: 邮件内容
        email_type: 邮件类型
    """
    if isinstance(to_emails, str):
        to_emails = [to_emails]
    
    return email_service.send_email(to_emails, subject, content, email_type)

def send_notification_email(to_emails, subject, content):
    """发送通知邮件的便捷函数"""
    return send_email(to_emails, subject, content, 'notification')

def send_warning_email(to_emails, subject, content):
    """发送警告邮件的便捷函数"""
    return send_email(to_emails, subject, content, 'warning')

def send_success_email(to_emails, subject, content):
    """发送成功邮件的便捷函数"""
    return send_email(to_emails, subject, content, 'success')

def send_error_email(to_emails, subject, content):
    """发送错误邮件的便捷函数"""
    return send_email(to_emails, subject, content, 'error')

# 测试代码
if __name__ == "__main__":
    # 测试邮件服务
    print("正在测试邮件服务...")
    
    # 初始化服务
    service = EmailService()
    
    # 测试连接
    connection_result = service.test_connection()
    print("连接测试结果:", connection_result)
    
    # if connection_result['success']:
    #     # 发送测试邮件（取消注释下面的代码来测试）
    #     result = service.send_notification(
    #         ['<EMAIL>'],
    #         '测试邮件',
    #         '这是一封测试邮件，用于验证邮件服务功能是否正常。\n\n如果您收到此邮件，说明邮件服务配置成功！'
    #     )
    #     print("发送结果:", result)
    # else:
    #     print("连接测试失败，无法发送测试邮件")