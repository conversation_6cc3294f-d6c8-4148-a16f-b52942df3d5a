#!/usr/bin/env python3
"""
分享书籍清单API测试脚本
用于验证公开访问接口的基本功能
"""

import requests
import json
import uuid

# 测试配置
BASE_URL = "http://localhost:5000"
API_BASE = f"{BASE_URL}/api/share"

def test_share_token_validation():
    """测试分享令牌格式验证"""
    print("测试分享令牌格式验证...")
    
    # 测试无效令牌格式
    invalid_token = "invalid-token-format"
    response = requests.get(f"{API_BASE}/public/shared-lists/{invalid_token}")
    
    print(f"无效令牌响应状态: {response.status_code}")
    if response.status_code == 400:
        print("✅ 无效令牌格式验证通过")
    else:
        print("❌ 无效令牌格式验证失败")
    
    # 测试有效令牌格式（但不存在的令牌）
    valid_token = str(uuid.uuid4())
    response = requests.get(f"{API_BASE}/public/shared-lists/{valid_token}")
    
    print(f"不存在令牌响应状态: {response.status_code}")
    if response.status_code == 404:
        print("✅ 不存在令牌验证通过")
    else:
        print("❌ 不存在令牌验证失败")

def test_api_endpoints():
    """测试API端点可访问性"""
    print("\n测试API端点可访问性...")
    
    # 测试测试接口
    try:
        response = requests.get(f"{API_BASE}/test")
        print(f"测试接口响应状态: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"测试接口响应: {data.get('message', '')}")
            print("✅ 测试接口正常")
        else:
            print("❌ 测试接口异常")
    except Exception as e:
        print(f"❌ 测试接口连接失败: {str(e)}")

def test_password_verification():
    """测试密码验证接口"""
    print("\n测试密码验证接口...")
    
    # 使用无效令牌测试
    invalid_token = "invalid-token"
    test_data = {"password": "test123"}
    
    try:
        response = requests.post(
            f"{API_BASE}/public/shared-lists/{invalid_token}/verify",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"密码验证接口响应状态: {response.status_code}")
        if response.status_code == 400:
            print("✅ 密码验证接口令牌格式验证通过")
        else:
            print("❌ 密码验证接口令牌格式验证失败")
    except Exception as e:
        print(f"❌ 密码验证接口连接失败: {str(e)}")

def test_book_detail_endpoint():
    """测试样书详情接口"""
    print("\n测试样书详情接口...")
    
    # 使用无效令牌测试
    invalid_token = "invalid-token"
    book_id = 1
    
    try:
        response = requests.get(f"{API_BASE}/public/shared-lists/{invalid_token}/books/{book_id}")
        
        print(f"样书详情接口响应状态: {response.status_code}")
        if response.status_code == 400:
            print("✅ 样书详情接口令牌格式验证通过")
        else:
            print("❌ 样书详情接口令牌格式验证失败")
    except Exception as e:
        print(f"❌ 样书详情接口连接失败: {str(e)}")

def main():
    """主测试函数"""
    print("开始测试分享书籍清单公开访问API...")
    print("=" * 50)
    
    # 基础连接测试
    test_api_endpoints()
    
    # 令牌验证测试
    test_share_token_validation()
    
    # 密码验证测试
    test_password_verification()
    
    # 样书详情测试
    test_book_detail_endpoint()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n注意：这些是基础的格式验证测试。")
    print("完整的功能测试需要：")
    print("1. 启动Flask应用服务器")
    print("2. 创建测试数据（清单、样书等）")
    print("3. 使用真实的分享令牌进行测试")

if __name__ == "__main__":
    main()