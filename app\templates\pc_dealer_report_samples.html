<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>推广报备</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <!-- <script defer src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js"></script> -->
    <script defer src="/static/js/alpine.min.js"></script>
    <style>
        [x-cloak] { display: none !important; }
        .modal {
            background-color: rgba(0, 0, 0, 0.5);
            transition: opacity 0.3s ease;
        }
        .modal-content {
            transition: transform 0.3s ease;
        }
        /* 消息提示动画效果 */
        .message-toast {
            transition: all 0.3s ease;
            opacity: 1;
            transform: translateY(0);
        }
        .animate-fadeIn { animation: fadeIn 0.3s ease-in; }
        .animate-fadeOut { animation: fadeOut 0.3s ease-out; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes fadeOut { from { opacity: 1; transform: translateY(0); } to { opacity: 0; transform: translateY(-20px); } }
    </style>
</head>
<body class="bg-gray-50 min-h-screen" x-data="dealerReportApp()" x-init="init()">
    <!-- 消息提示区 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-[100]"></div>

    <!-- 报备申请结果模态框 -->
    <div x-show="showResultModal" class="fixed inset-0 z-[200] overflow-y-auto modal flex items-center justify-center" x-cloak
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="fixed inset-0 bg-black bg-opacity-50" @click="closeResultModal()"></div>
        <div class="bg-white rounded-lg shadow-xl overflow-hidden transform transition-all sm:max-w-lg sm:w-full modal-content relative"
             x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" 
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <div class="bg-white px-4 py-3 border-b sm:px-6 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900" x-text="resultTitle"></h3>
                <button @click="closeResultModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="mb-4" x-html="resultMessage"></div>
                <div x-show="conflicts && conflicts.length > 0" class="mb-4">
                    <h4 class="font-medium text-yellow-600 mb-2">存在冲突的样书：</h4>
                    <ul class="list-disc list-inside text-sm space-y-1">
                        <template x-for="(conflict, index) in conflicts" :key="index">
                            <li x-text="conflict.sample_name" class="text-yellow-600"></li>
                        </template>
                    </ul>
        </div>
                <div x-show="duplicates && duplicates.length > 0" class="mb-4">
                    <h4 class="font-medium text-orange-600 mb-2">重复报备的样书：</h4>
                    <ul class="list-disc list-inside text-sm space-y-1">
                        <template x-for="(duplicate, index) in duplicates" :key="index">
                            <li x-text="duplicate.sample_name" class="text-orange-600"></li>
                        </template>
                    </ul>
                </div>
                <div class="mt-6 text-right">
                    <button @click="closeResultModal(true)" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        确认
                    </button>
                        </div>
            </div>
                        </div>
                    </div>
                    
    <!-- 冲突处理表单模态框 -->
    <div x-show="showConflictModal" class="fixed inset-0 z-[200] overflow-y-auto modal flex items-center justify-center" x-cloak
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="fixed inset-0 bg-black bg-opacity-50" @click="showConflictModal = false"></div>
        <div class="bg-white rounded-lg shadow-xl overflow-hidden transform transition-all sm:max-w-lg sm:w-full modal-content relative"
             x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" 
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <div class="bg-white px-4 py-3 border-b sm:px-6 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">样书报备冲突处理</h3>
                <button @click="showConflictModal = false" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
                        </div>
            <div class="p-6">
                <div class="mb-4 text-red-500 font-medium">以下样书与其他经销商的报备存在冲突：</div>
                <div class="max-h-40 overflow-y-auto mb-4 border border-gray-200 rounded-md p-2">
                    <ul class="list-disc pl-5 space-y-1">
                        <template x-for="(conflict, index) in conflicts" :key="index">
                            <li x-text="conflict.sample_name"></li>
                        </template>
                    </ul>
                        </div>
                
                <div class="mb-4">
                    <p class="text-gray-700 mb-2">您可以提交冲突处理申请，说明您有权在 <strong x-text="conflictSchoolName"></strong> 推广这些样书的理由，并上传相关证明材料。</p>
                    </div>
                    
                <form @submit.prevent="submitConflictForm" class="space-y-4">
                    <div>
                        <label for="conflict_reason" class="block text-sm font-medium text-gray-700 mb-1">冲突处理理由 <span class="text-red-500">*</span></label>
                        <textarea id="conflict_reason" x-model="conflictReason" name="conflict_reason" rows="4" required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500"></textarea>
                        </div>
                    
                    <div>
                        <label for="conflict_attachment" class="block text-sm font-medium text-gray-700 mb-1">证明材料 <span class="text-red-500">*</span></label>
                        <input type="file" id="conflict_attachment" name="attachment" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500">
                        <p class="text-xs text-gray-500 mt-1">支持的文件格式：PDF, JPG, PNG, DOC, DOCX (最大10MB)</p>
                    </div>
                    
                    <div class="flex space-x-3 pt-2">
                        <button type="button" @click="showConflictModal = false" class="flex-1 py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            取消
                        </button>
                        <button type="submit" :disabled="isSubmitting" class="flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <span x-show="!isSubmitting">提交冲突处理</span>
                            <span x-show="isSubmitting"><i class="fas fa-spinner fa-spin mr-2"></i>提交中...</span>
                        </button>
                        </div>
                </form>
                            </div>
                        </div>
                    </div>
                    
    <!-- 报备表单模态框 -->
    <div x-show="showReportModal" class="fixed inset-0 z-[200] overflow-y-auto modal flex items-center justify-center" x-cloak
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="fixed inset-0 bg-black bg-opacity-50" @click="showReportModal = false"></div>
        <div class="bg-white rounded-lg shadow-xl overflow-hidden transform transition-all sm:max-w-lg sm:w-full modal-content relative"
             x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" 
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <div class="bg-white px-4 py-3 border-b sm:px-6 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">提交报备申请</h3>
                <button @click="showReportModal = false" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
                        </div>
            <div class="p-6">
                <form @submit.prevent="submitReport" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">推广院校</label>
                        <div class="school-search-container relative">
                            <input type="text" id="schoolSearch" x-model="schoolSearch" class="w-full border border-gray-300 rounded px-3 py-2" 
                                placeholder="请输入院校名称搜索" @focus="showSchoolDropdown = true" @blur="handleSchoolBlur">
                            <div x-show="showSchoolDropdown && filteredSchools.length > 0" 
                                class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded shadow-lg max-h-60 overflow-y-auto">
                                <template x-for="school in filteredSchools" :key="school.id">
                                    <div @click="selectSchool(school)" class="px-3 py-2 hover:bg-gray-100 cursor-pointer">
                                        <span x-text="school.name"></span>
                            </div>
                                </template>
                        </div>
                            <div x-show="selectedSchool" class="mt-2">
                                <div class="flex items-center bg-gray-50 text-gray-700 p-2 rounded">
                                    <span x-text="selectedSchool ? selectedSchool.name : ''" class="flex-grow"></span>
                                    <button type="button" @click="clearSchool" class="text-gray-500 hover:text-gray-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                    </div>
                        </div>
                            </div>
                        </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">经销商姓名</label>
                        <input type="text" x-model="dealerName" class="w-full border border-gray-300 rounded px-3 py-2" 
                              placeholder="请输入经销商姓名">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                        <input type="text" x-model="dealerPhone" class="w-full border border-gray-300 rounded px-3 py-2" 
                              placeholder="请输入联系电话">
                    </div>
                    
                    <div class="pt-4">
                        <button type="submit" :disabled="isSubmitting" class="w-full bg-blue-500 text-white py-2 rounded hover:bg-blue-600">
                            <span x-show="!isSubmitting">提交报备</span>
                            <span x-show="isSubmitting"><i class="fas fa-spinner fa-spin mr-2"></i>提交中...</span>
                        </button>
                    </div>
                </form>
                    </div>
                </div>
            </div>
            
    <div class="container mx-auto p-6">
        <div class="flex justify-between items-center mb-8">
            <div>
                <button @click="openBookSelector()" class="px-6 py-2 bg-blue-500 text-white font-medium rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 flex items-center">
                    <i class="fas fa-book-open mr-2"></i>选择样书
                </button>
            </div>
        </div>
        
        <!-- 申请列表区域 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-medium text-gray-800">待报备样书列表 (<span x-text="selectedBooks.length"></span> 本)</h2>
                <div class="flex space-x-3">
                    <button @click="clearSelectedBooks()" 
                            class="px-3 py-1 bg-red-50 text-red-700 rounded hover:bg-red-100 text-sm flex items-center transition-all duration-200"
                            :class="{'opacity-50 cursor-not-allowed': selectedBooks.length === 0}"
                            :disabled="selectedBooks.length === 0">
                        <i class="fas fa-trash-alt mr-1"></i> 清空列表
                    </button>
                    <button @click="showReportModal = true" 
                            class="px-4 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm flex items-center transition-all duration-200"
                            :class="{'opacity-50 cursor-not-allowed': selectedBooks.length === 0}"
                            :disabled="selectedBooks.length === 0">
                        <i class="fas fa-paper-plane mr-1"></i> 提交报备
                    </button>
                </div>
            </div>
            
            <div class="overflow-x-auto border border-gray-200 rounded-lg">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    序号
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    样书名称
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    作者
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    出版社
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    ISBN
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    发货费率
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    结算费率
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    推广费率
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    操作
                                </th>
                            </tr>
                        </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-if="selectedBooks.length === 0">
                            <tr>
                                <td colspan="9" class="px-6 py-8 text-center text-gray-500">
                                    暂无待报备样书，请点击"选择样书"按钮添加
                                </td>
                            </tr>
                        </template>
                        <template x-for="(book, index) in selectedBooks" :key="book.id">
                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900" x-text="index + 1"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900" x-text="book.name"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500" x-text="book.author || '-'"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500" x-text="book.publisher_name || '-'"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500" x-text="book.isbn || '-'"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500" x-text="book.rateInfo ? book.rateInfo.shipping : '-'"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500" x-text="book.rateInfo ? book.rateInfo.settlement : '-'"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500" x-text="book.rateInfo ? book.rateInfo.promotion : '-'"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button @click="removeSelectedBook(index)" class="text-red-500 hover:text-red-600 transition-colors duration-200">
                                        <i class="fas fa-trash-alt mr-1"></i>删除
                                    </button>
                                </td>
                            </tr>
                        </template>
                        </tbody>
                    </table>
                </div>
            </div>
            
        <!-- 说明区域 -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">报备须知</h2>
            <div class="text-sm text-gray-600 space-y-2">
                <!-- <p><i class="fas fa-info-circle text-gray-500 mr-2"></i> 样书报备成功后，即可在该学校开展推广活动。</p> -->
                <p><i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i> 若报备学校与其他经销商产生冲突，需提供相关证明材料进行处理。</p>
                <!-- <p><i class="fas fa-clock text-gray-500 mr-2"></i> 报备有效期为1年，若逾期未形成订单，需重新报备。</p> -->
                <p><i class="fas fa-check-circle text-green-500 mr-2"></i> 报备成功后，可在"我的推广报备申请"页面查看报备状态。</p>
            </div>
        </div>
    </div>
    
    <script>
    // 获取样书费率的通用函数
    function getBookRates(book) {
        if (!book) return { shipping: '-', settlement: '-', promotion: '-' };
        
        const shipping = book.shipping_discount ? (book.shipping_discount * 100).toFixed(2) + '%' : '-';
        const settlement = book.settlement_discount ? (book.settlement_discount * 100).toFixed(2) + '%' : '-';
        let promotion;
        
        if (book.promotion_rate !== null && book.promotion_rate !== undefined) {
            promotion = (book.promotion_rate * 100).toFixed(2) + '%';
        } else if (book.shipping_discount && book.settlement_discount) {
            // 如果没有明确设置推广费率，则默认为发货折扣-结算折扣
            const calcRate = (book.shipping_discount - book.settlement_discount) * 100;
            promotion = calcRate.toFixed(2) + '%';
                        } else {
            promotion = '-';
        }
        
        return {
            shipping: shipping,
            settlement: settlement, 
            promotion: promotion
        };
    }
    
    function dealerReportApp() {
        return {
            // 样书状态
            selectedBooks: [],
            
            // 学校搜索
            schools: [],
            schoolSearch: '',
            selectedSchool: null,
            showSchoolDropdown: false,
            
            // 申请表单
            dealerName: '',
            dealerPhone: '',
            
            // 模态框状态
            showReportModal: false,
            showResultModal: false,
            showConflictModal: false,
            
            // 结果状态
            resultTitle: '',
            resultMessage: '',
            conflicts: [],
            duplicates: [],
            conflictSchoolName: '',
            conflictReason: '',
            
            // 提交状态
            isSubmitting: false,
            originalFormData: null,
            
            init() {
                this.loadDealerInfo();
                this.loadSchools();
                
                // 恢复本地存储的样书列表
                const storedBooks = localStorage.getItem('dealerSelectedBooks');
                if (storedBooks) {
                    try {
                        this.selectedBooks = JSON.parse(storedBooks) || [];
                    } catch (e) {
                        console.error('解析已保存样书失败:', e);
                        this.selectedBooks = [];
                    }
                }
                
                // 监听样书选择器的消息
                window.addEventListener('message', this.handleBookSelectorMessage.bind(this));
            },
            
            // 打开样书选择器
            openBookSelector() {
                // 获取屏幕尺寸，使窗口最大化
                const width = screen.availWidth;
                const height = screen.availHeight;
                const left = 0;
                const top = 0;

                window.open('/common/book_selector', '样书选择器',
                    `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`);
            },
            
            // 处理样书选择器的消息
            handleBookSelectorMessage(event) {
                if (event.data && event.data.type === 'SELECTED_BOOKS_FROM_SELECTOR') {
                    const newBooks = event.data.books || [];
                    if (newBooks.length > 0) {
                        // 合并已选择的书籍，避免重复
                        newBooks.forEach(newBook => {
                            if (!this.selectedBooks.some(book => book.id === newBook.id)) {
                                // 确保费率信息可用于显示
                                if (!newBook.rateInfo) {
                                    newBook.rateInfo = getBookRates(newBook);
                                }
                                this.selectedBooks.push(newBook);
                            }
                        });
                        
                        // 保存到本地存储
                        this.saveSelectedBooks();
                        this.showMessage(`已添加 ${newBooks.length} 本样书`, 'success');
                    }
                }
            },
            
            // 获取经销商信息
            loadDealerInfo() {
                fetch('/api/dealer/get_dealer_info')
                    .then(res => res.json())
                    .then(res => {
                        if (res.code === 0 && res.data) {
                            this.dealerName = res.data.name || '';
                            this.dealerPhone = res.data.phone_number || '';
                        }
                    })
                    .catch(err => {
                        console.error('获取经销商信息失败:', err);
                    });
            },
            
            // 加载学校列表
            loadSchools() {
                fetch('/api/dealer/get_schools')
                    .then(res => res.json())
                    .then(res => {
                        if (res.code === 0) {
                            this.schools = res.data || [];
                        }
                    })
                    .catch(err => {
                        console.error('加载学校列表失败:', err);
                    });
            },
            
            // 过滤学校列表
            get filteredSchools() {
                const search = this.schoolSearch.toLowerCase().trim();
                if (!search) return this.schools;
                
                return this.schools.filter(school => 
                    school.name.toLowerCase().includes(search)
                );
            },
            
            // 处理学校选择框失去焦点
            handleSchoolBlur() {
                // 延迟关闭下拉框，以便可以点击选项
                setTimeout(() => {
                    this.showSchoolDropdown = false;
                }, 200);
            },
            
            // 选择学校
            selectSchool(school) {
                this.selectedSchool = school;
                this.schoolSearch = school ? school.name : '';
                this.showSchoolDropdown = false;
            },
            
            // 清除选择的学校
            clearSchool() {
                this.selectedSchool = null;
                this.schoolSearch = '';
            },
            
            // 移除选择的样书
            removeSelectedBook(index) {
                if (index >= 0 && index < this.selectedBooks.length) {
                    this.selectedBooks.splice(index, 1);
                    this.saveSelectedBooks();
                }
            },
            
            // 清空选择的样书
            clearSelectedBooks() {
                if (this.selectedBooks.length === 0) return;
                
                if (confirm('确定要清空所有已选样书吗？')) {
                    this.selectedBooks = [];
                    this.saveSelectedBooks();
                    this.showMessage('已清空所有样书', 'info');
                }
            },
            
            // 提交报备
            async submitReport() {
                // 表单验证
                if (!this.selectedSchool) {
                    this.showMessage('请选择推广院校', 'error');
                    return;
                }
                
                if (!this.dealerName) {
                    this.showMessage('请输入经销商姓名', 'error');
                    return;
                }
                
                if (!this.dealerPhone) {
                    this.showMessage('请输入联系电话', 'error');
                    return;
                }
                
                if (this.selectedBooks.length === 0) {
                    this.showMessage('请选择至少一本样书', 'error');
                    return;
                }
                
                // 显示提交中状态
                this.isSubmitting = true;
                
                try {
                    // 创建一个普通的对象来收集表单数据
                    const formData = {
                        dealer_name: this.dealerName,
                        dealer_phone: this.dealerPhone,
                        school_id: this.selectedSchool ? this.selectedSchool.id : null,
                        sample_ids: this.selectedBooks.map(sample => sample.id)
                    };
                    
                    // 保存原始表单数据用于冲突处理
                    this.originalFormData = formData;
                    this.conflictSchoolName = this.selectedSchool ? this.selectedSchool.name : '';
                    
                    // 发送请求
                    const response = await fetch('/api/dealer/submit_report', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                    
                    const result = await response.json();
                    
                    if (result.code === 0) {
                        // 全部成功
                        this.showResultModal = true;
                        this.resultTitle = '报备成功';
                        this.resultMessage = `<div class="text-green-600">已成功报备 ${this.selectedBooks.length} 本样书</div>`;
                        this.showReportModal = false;
                        
                        // 清空选择的样书和表单
                        this.selectedBooks = [];
                        this.saveSelectedBooks();
                    } 
                    else if (result.code === 2) {
                        // 部分成功，有冲突或重复
                        let resultMessage = '';
                        
                        if (result.success_count > 0) {
                            resultMessage += `<div class="text-green-500 mb-3">${result.success_count}本样书报备成功</div>`;
                        }
                        
                        this.showReportModal = false;
                        
                        if (result.conflicts && result.conflicts.length > 0) {
                            // 显示冲突处理表单
                            this.conflicts = result.conflicts;
                            this.showConflictModal = true;
                    return;
                }
                
                        if (result.duplicates && result.duplicates.length > 0) {
                            this.duplicates = result.duplicates;
                            resultMessage += '<div class="mb-3"><strong class="text-yellow-500">以下样书已报备过：</strong></div>';
                            
                            this.resultTitle = '报备结果';
                            this.resultMessage = resultMessage;
                            this.showResultModal = true;
                            
                            if (result.success_count > 0) {
                                // 有成功的报备，清空与重复冲突的样书
                                const successIds = new Set(result.success_ids || []);
                                this.selectedBooks = this.selectedBooks.filter(book => !successIds.has(book.id));
                                this.saveSelectedBooks();
                            }
                        }
                    }
                    else {
                        // 全部失败
                        this.showMessage(result.message || '报备失败', 'error');
                    }
                } catch (error) {
                    console.error('提交报备失败:', error);
                    this.showMessage('网络错误，请稍后重试', 'error');
                } finally {
                    this.isSubmitting = false;
                }
            },
            
            // 提交冲突处理表单
            async submitConflictForm(e) {
                // 表单验证
                if (!this.conflictReason) {
                    this.showMessage('请输入冲突处理理由', 'error');
                    return;
                }
                
                const attachment = document.getElementById('conflict_attachment').files[0];
                if (!attachment) {
                    this.showMessage('请选择证明材料', 'error');
                    return;
                }
                
                // 显示提交中状态
                this.isSubmitting = true;
                
                try {
                // 创建FormData对象，用于上传文件
                const formData = new FormData();
                
                // 添加冲突样书ID列表
                    this.conflicts.forEach(conflict => {
                    formData.append('sample_ids[]', conflict.sample_id);
                });
                
                // 添加原始表单数据
                    formData.append('school_id', this.originalFormData.school_id);
                    formData.append('dealer_name', this.originalFormData.dealer_name);
                    formData.append('dealer_phone', this.originalFormData.dealer_phone);
                    formData.append('conflict_reason', this.conflictReason);
                formData.append('attachment', attachment);
                
                    // 发送请求
                    const response = await fetch('/api/dealer/submit_conflict_report', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (result.code === 0) {
                        this.showConflictModal = false;
                        this.showMessage('冲突处理申请提交成功', 'success');
                        
                        // 清空冲突的样书
                        const conflictIds = new Set(this.conflicts.map(c => parseInt(c.sample_id)));
                        this.selectedBooks = this.selectedBooks.filter(book => !conflictIds.has(book.id));
                        this.saveSelectedBooks();
                        } else {
                        this.showMessage(result.message || '冲突处理申请提交失败', 'error');
                    }
                } catch (error) {
                    console.error('提交冲突处理失败:', error);
                    this.showMessage('网络错误，请稍后重试', 'error');
                } finally {
                    this.isSubmitting = false;
                }
            },
            
            // 关闭结果模态框
            closeResultModal(clearBooks = false) {
                this.showResultModal = false;
                this.resultTitle = '';
                this.resultMessage = '';
                this.conflicts = [];
                this.duplicates = [];
                
                if (clearBooks) {
                    this.selectedBooks = [];
                    this.saveSelectedBooks();
                }
            },
            
            // 保存选择的样书到本地存储
            saveSelectedBooks() {
                localStorage.setItem('dealerSelectedBooks', JSON.stringify(this.selectedBooks));
            },
            
            // 显示消息
            showMessage(message, type = 'info') {
                const messageContainer = document.getElementById('messageContainer');
                
                // 移除之前的消息
                const oldMessages = messageContainer.querySelectorAll('.message-toast');
                oldMessages.forEach(msg => {
                    msg.classList.add('animate-fadeOut');
                    setTimeout(() => msg.remove(), 300);
                });
                
                // 根据类型设置样式
                let bgColor = 'bg-gray-600';
                let icon = 'fa-info-circle';
                
                if (type === 'success') {
                    bgColor = 'bg-green-500';
                    icon = 'fa-check-circle';
                } else if (type === 'warning') {
                    bgColor = 'bg-yellow-500';
                    icon = 'fa-exclamation-circle';
                } else if (type === 'error') {
                    bgColor = 'bg-red-500';
                    icon = 'fa-times-circle';
                }
                
                // 创建消息元素
                const toast = document.createElement('div');
                toast.className = `message-toast ${bgColor} text-white px-4 py-3 rounded shadow-lg flex items-center mb-2 animate-fadeIn`;
                toast.innerHTML = `
                        <i class="fas ${icon} mr-2"></i>
                        <span>${message}</span>
                `;
                
                // 添加到消息容器
                messageContainer.appendChild(toast);
                
                // 自动消失
                setTimeout(() => {
                    toast.classList.add('animate-fadeOut');
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            }
        };
    }
    </script>
</body>
</html>