<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>换版推荐管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮样式 - 基于设计规范 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }
        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 消息通知容器 */
        #messageContainer {
            z-index: 9999 !important; 
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            display: inline-flex;
            align-items: center;
        }
        .status-in-progress {
            background: #fef3c7;
            color: #92400e;
        }
        .status-ended {
            background: #d1fae5;
            color: #065f46;
        }

        /* 推荐类型标签 */
        .type-direct {
            background: #dbeafe;
            color: #1d4ed8;
        }
        .type-internal {
            background: #ede9fe;
            color: #6d28d9;
        }
        .type-external {
            background: #fef3c7;
            color: #92400e;
        }

        /* 模态框背景 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }
        
        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 1000 !important;
            max-height: 240px;
            overflow: hidden;
        }

        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-text {
            flex: 1;
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .custom-select-arrow {
            transition: transform 0.2s ease;
            color: #6b7280;
        }

        .custom-select.active .custom-select-arrow {
            transform: rotate(180deg);
        }

        /* 确保下拉组件在最顶层 */
        .custom-select {
            position: relative;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .custom-select-search {
            padding: 8px;
            border-bottom: 1px solid #e5e7eb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            outline: none;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        .custom-select-options {
            max-height: 200px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.hidden {
            display: none;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table-header {
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .table-row {
            border-bottom: 1px solid #f1f5f9;
            transition: background-color 0.15s ease;
        }

        .table-row:hover {
            background-color: #f8fafc;
        }

        .table-cell {
            padding: 12px 16px;
            vertical-align: middle;
        }

        /* 加载动画 */
        .loading-spinner {
            border: 2px solid #f3f4f6;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }


    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 space-y-2 z-50"></div>

    <!-- 主要内容区域 -->
    <div class="container mx-auto px-4 py-6">
        <!-- 筛选区域 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-6 mb-6" style="overflow: visible; position: relative; z-index: 10;">
            <!-- 第一行：时间筛选、搜索、重置按钮 -->
            <div class="flex flex-col md:flex-row gap-4 md:items-end mb-4" style="overflow: visible;">
                <div class="md:w-80">
                    <label class="block text-sm font-medium text-slate-700 mb-2">时间</label>
                    <div class="custom-select" id="timeFilterContainer">
                        <div class="custom-select-trigger" id="timeFilterTrigger" style="user-select: none;">
                            <span class="custom-select-text">全部时间</span>
                            <i class="fas fa-chevron-down custom-select-arrow" style="pointer-events: none;"></i>
                        </div>
                        <div class="custom-select-dropdown hidden">
                            <div class="custom-select-options" id="timeFilterOptions">
                                <div class="custom-select-option" data-value="">全部时间</div>
                                <div class="custom-select-option" data-value="custom">自定义</div>
                                <div class="custom-select-option" data-value="today">今天</div>
                                <div class="custom-select-option" data-value="yesterday">昨天</div>
                                <div class="custom-select-option" data-value="this_month">本月</div>
                                <div class="custom-select-option" data-value="last_month">上月</div>
                                <div class="custom-select-option" data-value="this_year">本年</div>
                                <div class="custom-select-option" data-value="last_year">上年</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex-1">
                    <label class="block text-sm font-medium text-slate-700 mb-2">搜索</label>
                    <input type="text" id="keywordSearch" placeholder="搜索学校、教材名称、ISBN..." 
                           class="w-full px-3 py-2 h-10 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex items-end gap-2">
                    <button onclick="forceApplyFilters()"
                            class="btn-primary text-white px-6 py-2 h-10 rounded-xl font-medium flex items-center gap-2">
                        <i class="fas fa-search"></i>
                        <span class="hidden sm:inline">搜索</span>
                    </button>
                    <button onclick="resetFilters()" 
                            class="bg-slate-100 hover:bg-slate-200 text-slate-700 px-6 py-2 h-10 rounded-xl font-medium flex items-center gap-2 transition-colors">
                        <i class="fas fa-undo"></i>
                        <span class="hidden sm:inline">重置</span>
                    </button>
                </div>
            </div>

            <!-- 第二行：主要筛选条件 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4" style="overflow: visible;">
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">学校筛选</label>
                    <div class="custom-select" id="schoolFilterContainer">
                        <div class="custom-select-trigger">
                            <span class="custom-select-text">全部学校</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div class="custom-select-dropdown hidden">
                            <div class="custom-select-search">
                                <input type="text" placeholder="搜索学校..." />
                            </div>
                            <div class="custom-select-options" id="schoolFilterOptions">
                                <div class="custom-select-option" data-value="">全部学校</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">推荐类型</label>
                    <div class="custom-select" id="typeFilterContainer">
                        <div class="custom-select-trigger">
                            <span class="custom-select-text">全部类型</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div class="custom-select-dropdown hidden">
                            <div class="custom-select-options" id="typeFilterOptions">
                                <div class="custom-select-option" data-value="">全部类型</div>
                                <div class="custom-select-option" data-value="direct">直接推荐</div>
                                <div class="custom-select-option" data-value="internal">内部推荐</div>
                                <div class="custom-select-option" data-value="external">外部推荐</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">推荐状态</label>
                    <div class="custom-select" id="statusFilterContainer">
                        <div class="custom-select-trigger">
                            <span class="custom-select-text">全部状态</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div class="custom-select-dropdown hidden">
                            <div class="custom-select-options" id="statusFilterOptions">
                                <div class="custom-select-option" data-value="">全部状态</div>
                                <div class="custom-select-option" data-value="in_progress">推荐中</div>
                                <div class="custom-select-option" data-value="ended">已结束</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">经销商公司</label>
                    <div class="custom-select" id="dealerFilterContainer">
                        <div class="custom-select-trigger">
                            <span class="custom-select-text">全部经销商</span>
                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                        </div>
                        <div class="custom-select-dropdown hidden">
                            <div class="custom-select-search">
                                <input type="text" placeholder="搜索经销商..." />
                            </div>
                            <div class="custom-select-options" id="dealerFilterOptions">
                                <div class="custom-select-option" data-value="">全部经销商</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6" style="position: relative; z-index: 1;">
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                        <i class="fas fa-list-alt text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-slate-600">总推荐数</p>
                        <p class="text-2xl font-bold text-slate-800" id="totalRecommendationCount">-</p>
                    </div>
                </div>
            </div>
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600 mr-4">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-slate-600">推荐中</p>
                        <p class="text-2xl font-bold text-slate-800" id="inProgressCount">-</p>
                    </div>
                </div>
            </div>
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-slate-600">已结束</p>
                        <p class="text-2xl font-bold text-slate-800" id="endedCount">-</p>
                    </div>
                </div>
            </div>
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-slate-600">推荐结果</p>
                        <p class="text-2xl font-bold text-slate-800" id="resultCount">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center gap-4">
                <h2 class="text-lg font-semibold text-slate-800">推荐列表</h2>
                <div class="flex items-center gap-2 text-sm text-slate-600">
                    <span>共</span>
                    <span id="displayTotal" class="font-medium text-blue-600">0</span>
                    <span>条记录</span>
                </div>
            </div>
            <div class="flex items-center gap-2">
                <button onclick="exportData()"
                        class="btn-success text-white px-4 py-2 rounded-xl font-medium flex items-center gap-2">
                    <i class="fas fa-download"></i>
                    <span>导出数据</span>
                </button>
                <button onclick="refreshData()"
                        class="bg-slate-100 hover:bg-slate-200 text-slate-700 px-4 py-2 rounded-xl font-medium flex items-center gap-2 transition-colors">
                    <i class="fas fa-sync-alt"></i>
                    <span>刷新</span>
                </button>
            </div>
        </div>

        <!-- 推荐列表表格 -->
        <div class="table-container">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="table-header">
                        <tr>
                            <th class="table-cell text-left text-sm font-medium text-slate-600">学校信息</th>
                            <th class="table-cell text-left text-sm font-medium text-slate-600">原用教材</th>
                            <th class="table-cell text-left text-sm font-medium text-slate-600">发起信息</th>
                            <th class="table-cell text-left text-sm font-medium text-slate-600">推荐类型</th>
                            <th class="table-cell text-left text-sm font-medium text-slate-600">状态</th>
                            <th class="table-cell text-left text-sm font-medium text-slate-600">推荐结果</th>
                            <th class="table-cell text-left text-sm font-medium text-slate-600">创建时间</th>
                            <th class="table-cell text-left text-sm font-medium text-slate-600">操作</th>
                        </tr>
                    </thead>
                    <tbody id="recommendationTableBody">
                        <!-- 加载状态 -->
                        <tr id="loadingRow">
                            <td colspan="8" class="table-cell text-center py-8">
                                <div class="flex items-center justify-center gap-3">
                                    <div class="loading-spinner"></div>
                                    <span class="text-slate-600">加载中...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分页 -->
        <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
            <!-- 信息显示区域 -->
            <div class="flex items-center">
                <p class="text-sm text-gray-700 mr-4">
                    第 <span id="currentPage" class="font-medium">1</span> 页，
                    共 <span id="totalPages" class="font-medium">1</span> 页，
                    共 <span id="totalCount" class="font-medium">0</span> 条
                </p>
            </div>

            <!-- 分页按钮区域 -->
            <div class="flex gap-1">
                <!-- 首页按钮 -->
                <button id="firstBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span class="sr-only">首页</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                </button>

                <!-- 上一页按钮 -->
                <button id="prevBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    上一页
                </button>

                <!-- 页码按钮容器 -->
                <div id="pageNumbers" class="flex gap-1">
                    <!-- 页码将通过JavaScript动态生成 -->
                </div>

                <!-- 下一页按钮 -->
                <button id="nextBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    下一页
                </button>

                <!-- 末页按钮 -->
                <button id="lastBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span class="sr-only">末页</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- 自定义日期选择器模态框 -->
    <div id="datePickerModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-lg font-semibold text-slate-800">选择时间范围</h3>
                    <button onclick="closeDatePicker()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <!-- 模态框内容 -->
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">开始日期</label>
                            <input type="date" id="startDate"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">结束日期</label>
                            <input type="date" id="endDate"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                </div>
                <!-- 模态框按钮 -->
                <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                    <button onclick="closeDatePicker()"
                            class="bg-slate-100 hover:bg-slate-200 text-slate-700 px-4 py-2 rounded-xl font-medium transition-colors">
                        取消
                    </button>
                    <button onclick="confirmDateRange()"
                            class="btn-primary text-white px-4 py-2 rounded-xl font-medium">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 推荐详情模态框 -->
    <div id="detailModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-xl font-semibold text-slate-800">推荐详情</h3>
                    <button onclick="closeDetailModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <!-- 模态框内容 -->
                <div id="detailContent" class="p-6 overflow-y-auto max-h-[70vh] custom-scrollbar">
                    <!-- 详情内容将通过JavaScript填充 -->
                </div>
                <!-- 模态框按钮 -->
                <div class="p-6 border-t border-slate-200 flex justify-end gap-3 bg-white">
                    <button onclick="closeDetailModal()"
                            class="bg-slate-100 hover:bg-slate-200 text-slate-700 px-4 py-2 rounded-xl font-medium transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态管理模态框 -->
    <div id="statusModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-lg font-semibold text-slate-800">管理推荐状态</h3>
                    <button onclick="closeStatusModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <!-- 模态框内容 -->
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">推荐状态</label>
                            <select id="newStatus" class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="in_progress">推荐中</option>
                                <option value="ended">已结束</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">备注</label>
                            <textarea id="statusNotes" rows="3" placeholder="请输入状态变更备注..."
                                      class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>
                    </div>
                </div>
                <!-- 模态框按钮 -->
                <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                    <button onclick="closeStatusModal()"
                            class="bg-slate-100 hover:bg-slate-200 text-slate-700 px-4 py-2 rounded-xl font-medium transition-colors">
                        取消
                    </button>
                    <button onclick="confirmStatusUpdate()"
                            class="btn-primary text-white px-4 py-2 rounded-xl font-medium">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let currentFilters = {};
        let currentRecommendationId = null;
        let timeFilterSelect = null;
        let schoolFilterSelect = null;
        let typeFilterSelect = null;
        let statusFilterSelect = null;
        let dealerFilterSelect = null;
        let customStartDate = '';
        let customEndDate = '';
        let currentTimeFilter = '';
        let searchTimeout = null;
        let totalCount = 0;

        // 页面加载完成后初始化
        $(document).ready(function() {
            initializeComponents();
            loadFilterOptions();
            loadRecommendations();
            loadStatistics();
            initializePagination();
        });

        // 初始化分页事件绑定
        function initializePagination() {
            // 绑定首页按钮事件
            $('#firstBtn').click(function() {
                if (currentPage !== 1) {
                    loadRecommendations(1);
                }
            });

            // 绑定上一页按钮事件
            $('#prevBtn').click(function() {
                if (currentPage > 1) {
                    loadRecommendations(currentPage - 1);
                }
            });

            // 绑定下一页按钮事件
            $('#nextBtn').click(function() {
                const totalPagesCount = parseInt($('#totalPages').text()) || 1;
                if (currentPage < totalPagesCount) {
                    loadRecommendations(currentPage + 1);
                }
            });

            // 绑定末页按钮事件
            $('#lastBtn').click(function() {
                const totalPagesCount = parseInt($('#totalPages').text()) || 1;
                if (currentPage !== totalPagesCount) {
                    loadRecommendations(totalPagesCount);
                }
            });
        }

        // 初始化组件
        function initializeComponents() {
            // 初始化时间筛选器
            timeFilterSelect = new CustomSelect('timeFilterContainer', {
                placeholder: '全部时间',
                onSelect: function(value, text) {
                    handleTimeFilterSelect(value, text);
                }
            });

            // 初始化学校筛选器
            schoolFilterSelect = new CustomSelect('schoolFilterContainer', {
                placeholder: '全部学校',
                searchable: true,
                onSelect: function(value, text) {
                    currentFilters.school_id = value;
                    applyFilters(); // 立即应用筛选
                }
            });

            // 初始化推荐类型筛选器
            typeFilterSelect = new CustomSelect('typeFilterContainer', {
                placeholder: '全部类型',
                onSelect: function(value, text) {
                    currentFilters.recommendation_type = value;
                    applyFilters(); // 立即应用筛选
                }
            });

            // 初始化状态筛选器
            statusFilterSelect = new CustomSelect('statusFilterContainer', {
                placeholder: '全部状态',
                onSelect: function(value, text) {
                    currentFilters.status = value;
                    applyFilters(); // 立即应用筛选
                }
            });

            // 初始化经销商筛选器
            dealerFilterSelect = new CustomSelect('dealerFilterContainer', {
                placeholder: '全部经销商',
                searchable: true,
                onSelect: function(value, text) {
                    currentFilters.initiator_company_id = value;
                    applyFilters(); // 立即应用筛选
                }
            });

            // 绑定搜索框事件 - 使用防抖延迟搜索
            $('#keywordSearch').on('input', function() {
                const keyword = $(this).val();
                currentFilters.keyword = keyword;

                // 清除之前的定时器
                clearTimeout(searchTimeout);

                // 设置新的定时器，500ms后执行搜索
                searchTimeout = setTimeout(function() {
                    applyFilters();
                }, 500);
            });
        }

        // 处理时间筛选选择
        function handleTimeFilterSelect(value, text) {
            currentTimeFilter = value;

            if (value === 'custom') {
                // 显示自定义日期选择器
                showDatePicker();
            } else {
                // 清空自定义日期
                customStartDate = '';
                customEndDate = '';
                currentFilters.start_date = '';
                currentFilters.end_date = '';

                // 设置预设时间范围
                setPresetTimeRange(value);
                // 立即应用筛选
                applyFilters();
            }
        }

        // 设置预设时间范围
        function setPresetTimeRange(value) {
            const today = new Date();
            let startDate = null;
            let endDate = null;

            switch(value) {
                case 'today':
                    startDate = endDate = today.toISOString().split('T')[0];
                    break;
                case 'yesterday':
                    const yesterday = new Date(today);
                    yesterday.setDate(yesterday.getDate() - 1);
                    startDate = endDate = yesterday.toISOString().split('T')[0];
                    break;
                case 'this_month':
                    startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
                    endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString().split('T')[0];
                    break;
                case 'last_month':
                    startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1).toISOString().split('T')[0];
                    endDate = new Date(today.getFullYear(), today.getMonth(), 0).toISOString().split('T')[0];
                    break;
                case 'this_year':
                    startDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
                    endDate = new Date(today.getFullYear(), 11, 31).toISOString().split('T')[0];
                    break;
                case 'last_year':
                    startDate = new Date(today.getFullYear() - 1, 0, 1).toISOString().split('T')[0];
                    endDate = new Date(today.getFullYear() - 1, 11, 31).toISOString().split('T')[0];
                    break;
            }

            if (startDate && endDate) {
                currentFilters.start_date = startDate;
                currentFilters.end_date = endDate;
            }
        }

        // 显示日期选择器
        function showDatePicker() {
            // 设置默认日期为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('startDate').value = customStartDate || today;
            document.getElementById('endDate').value = customEndDate || today;

            // 显示模态框
            document.getElementById('datePickerModal').classList.remove('hidden');
        }

        // 关闭日期选择器
        function closeDatePicker() {
            document.getElementById('datePickerModal').classList.add('hidden');

            // 如果没有设置自定义日期，重置时间筛选器
            if (!customStartDate || !customEndDate) {
                timeFilterSelect.reset();
                currentTimeFilter = '';
            }
        }

        // 确认日期范围
        function confirmDateRange() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                showMessage('请选择开始和结束日期', 'error');
                return;
            }

            if (startDate > endDate) {
                showMessage('开始日期不能晚于结束日期', 'error');
                return;
            }

            // 保存自定义日期
            customStartDate = startDate;
            customEndDate = endDate;
            currentFilters.start_date = startDate;
            currentFilters.end_date = endDate;

            // 更新时间筛选器显示文本
            const startDateStr = formatDate(startDate);
            const endDateStr = formatDate(endDate);
            timeFilterSelect.textSpan.textContent = `${startDateStr} 至 ${endDateStr}`;

            // 关闭模态框
            closeDatePicker();

            // 立即应用筛选
            applyFilters();
        }

        // 格式化日期显示
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            return `${date.getMonth() + 1}/${date.getDate()}`;
        }

        // 加载筛选选项
        function loadFilterOptions() {
            $.ajax({
                url: '/api/admin/get_recommendation_filter_options',
                method: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        const data = response.data;

                        // 设置学校选项
                        const schoolOptions = [
                            {value: '', text: '全部学校'},
                            ...data.schools.map(school => ({
                                value: school.id,
                                text: `${school.name} (${school.school_level || '未分类'})`
                            }))
                        ];
                        schoolFilterSelect.setOptions(schoolOptions);

                        // 设置经销商选项
                        const dealerOptions = [
                            {value: '', text: '全部经销商'},
                            ...data.dealer_companies.map(company => ({
                                value: company.id,
                                text: company.name
                            }))
                        ];
                        dealerFilterSelect.setOptions(dealerOptions);
                    }
                },
                error: function() {
                    showMessage('加载筛选选项失败', 'error');
                }
            });
        }

        // 加载推荐列表
        function loadRecommendations(page = 1) {
            currentPage = page;

            // 显示加载状态
            $('#loadingRow').show();
            $('#recommendationTableBody tr:not(#loadingRow)').remove();

            const params = {
                page: page,
                per_page: 20,
                ...currentFilters
            };

            $.ajax({
                url: '/api/admin/get_book_recommendations',
                method: 'GET',
                data: params,
                success: function(response) {
                    $('#loadingRow').hide();

                    if (response.code === 0) {
                        const data = response.data;
                        renderRecommendationTable(data.recommendations);
                        updatePagination(data.page, data.total_pages, data.total);
                        // 更新显示总数
                        $('#displayTotal').text(data.total);
                    } else {
                        showMessage(response.message || '加载推荐列表失败', 'error');
                    }
                },
                error: function() {
                    $('#loadingRow').hide();
                    showMessage('加载推荐列表失败', 'error');
                }
            });
        }

        // 渲染推荐表格
        function renderRecommendationTable(recommendations) {
            const tbody = $('#recommendationTableBody');

            if (recommendations.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="8" class="table-cell text-center py-8 text-slate-500">
                            <i class="fas fa-inbox text-3xl mb-2 block"></i>
                            暂无推荐数据
                        </td>
                    </tr>
                `);
                return;
            }

            recommendations.forEach(rec => {
                const statusClass = rec.status === 'in_progress' ? 'status-in-progress' : 'status-ended';
                const statusText = rec.status === 'in_progress' ? '推荐中' : '已结束';

                const typeClass = `type-${rec.recommendation_type}`;
                const typeText = {
                    'direct': '直接推荐',
                    'internal': '内部推荐',
                    'external': '外部推荐'
                }[rec.recommendation_type] || rec.recommendation_type;

                const row = `
                    <tr class="table-row">
                        <td class="table-cell">
                            <div>
                                <div class="font-medium text-slate-800">${rec.school_name}</div>
                                <div class="text-sm text-slate-500">${rec.school_level || '未分类'}</div>
                            </div>
                        </td>
                        <td class="table-cell">
                            <div>
                                <div class="font-medium text-slate-800">${rec.original_book_name}</div>
                                <div class="text-sm text-slate-500">ISBN: ${rec.original_book_isbn}</div>
                                <div class="text-sm text-slate-500">${rec.original_book_publisher}</div>
                            </div>
                        </td>
                        <td class="table-cell">
                            <div>
                                <div class="font-medium text-slate-800">${rec.initiator_company_name}</div>
                                <div class="text-sm text-slate-500">${rec.initiator_name}</div>
                            </div>
                        </td>
                        <td class="table-cell">
                            <span class="status-badge ${typeClass}">${typeText}</span>
                        </td>
                        <td class="table-cell">
                            <span class="status-badge ${statusClass}">${statusText}</span>
                        </td>
                        <td class="table-cell">
                            <div class="text-sm">
                                <div>推荐总数: <span class="font-medium">${rec.result_count || 0}</span></div>
                            </div>
                        </td>
                        <td class="table-cell">
                            <div class="text-sm text-slate-600">${rec.created_at}</div>
                        </td>
                        <td class="table-cell">
                            <div class="flex items-center gap-2">
                                <button onclick="viewDetail(${rec.id})"
                                        class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    <i class="fas fa-eye mr-1"></i>详情
                                </button>
                                <button onclick="manageStatus(${rec.id}, '${rec.status}')"
                                        class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                    <i class="fas fa-cog mr-1"></i>管理
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // 页码生成算法（按照前端规范）
        function getPageNumbers(currentPage, totalPages) {
            const pageNumbers = [];

            if (totalPages <= 7) {
                // 总页数不超过7页，显示所有页码
                for (let i = 1; i <= totalPages; i++) {
                    pageNumbers.push(i);
                }
            } else {
                // 总页数超过7页，使用省略号
                pageNumbers.push(1);

                if (currentPage <= 4) {
                    // 当前页在前部
                    pageNumbers.push(2, 3, 4, 5);
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages);
                } else if (currentPage >= totalPages - 3) {
                    // 当前页在后部
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
                    pageNumbers.push(totalPages);
                } else {
                    // 当前页在中部
                    pageNumbers.push('...');
                    pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages);
                }
            }

            return pageNumbers;
        }

        // 渲染页码按钮（按照前端规范）
        function renderPageNumbers(containerSelector, currentPage, totalPages, clickHandler) {
            const container = $(containerSelector);
            container.empty();

            const pageNumbers = getPageNumbers(currentPage, totalPages);

            pageNumbers.forEach(pageNumber => {
                if (pageNumber === '...') {
                    // 省略号
                    container.append(`
                        <span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700">
                            ...
                        </span>
                    `);
                } else {
                    // 页码按钮
                    const isActive = pageNumber === currentPage;
                    const activeClass = isActive ? 'bg-blue-50 text-blue-600 border-blue-500' : 'bg-white text-gray-700 hover:bg-gray-50';
                    container.append(`
                        <button class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md ${activeClass}"
                                onclick="${clickHandler}(${pageNumber})">
                            ${pageNumber}
                        </button>
                    `);
                }
            });
        }

        // 更新分页（按照前端规范）
        function updatePagination(page, totalPagesParam, total) {
            currentPage = page;
            totalPages = totalPagesParam;  // 更新全局变量
            totalCount = total;            // 更新全局变量

            // 更新显示信息
            $('#currentPage').text(currentPage);
            $('#totalPages').text(totalPages);
            $('#totalCount').text(total);

            // 更新按钮状态
            $('#firstBtn').prop('disabled', currentPage <= 1);
            $('#prevBtn').prop('disabled', currentPage <= 1);
            $('#nextBtn').prop('disabled', currentPage >= totalPages);
            $('#lastBtn').prop('disabled', currentPage >= totalPages);

            // 渲染页码按钮
            if (totalPages > 1) {
                renderPageNumbers('#pageNumbers', currentPage, totalPages, 'loadRecommendations');
            } else {
                $('#pageNumbers').empty();
            }
        }



        // 加载统计数据
        function loadStatistics() {
            $.ajax({
                url: '/api/admin/get_recommendation_statistics',
                method: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        const stats = response.data.basic_stats;
                        $('#totalRecommendationCount').text(stats.total_recommendations || 0);
                        $('#inProgressCount').text(stats.in_progress_count || 0);
                        $('#endedCount').text(stats.ended_count || 0);

                        const resultStats = response.data.result_stats;
                        $('#resultCount').text(resultStats.total_results || 0);
                    }
                },
                error: function() {
                    console.error('加载统计数据失败');
                }
            });
        }

        // 强制立即应用筛选（清除搜索延迟）
        function forceApplyFilters() {
            // 清除搜索延迟定时器
            clearTimeout(searchTimeout);
            // 立即应用筛选
            applyFilters();
        }

        // 应用筛选
        function applyFilters() {
            currentPage = 1;
            loadRecommendations();
        }

        // 重置筛选
        function resetFilters() {
            currentFilters = {};
            currentTimeFilter = '';
            customStartDate = '';
            customEndDate = '';

            // 重置所有筛选器
            timeFilterSelect.reset();
            schoolFilterSelect.reset();
            typeFilterSelect.reset();
            statusFilterSelect.reset();
            dealerFilterSelect.reset();

            // 重置搜索框
            $('#keywordSearch').val('');

            // 重新加载数据
            loadRecommendations();
        }

        // 刷新数据
        function refreshData() {
            loadRecommendations(currentPage);
            loadStatistics();
        }

        // 查看详情
        function viewDetail(recommendationId) {
            $.ajax({
                url: '/api/admin/get_book_recommendation_detail',
                method: 'GET',
                data: { id: recommendationId },
                success: function(response) {
                    if (response.code === 0) {
                        renderDetailModal(response.data);
                        $('#detailModal').removeClass('hidden');
                    } else {
                        showMessage(response.message || '获取详情失败', 'error');
                    }
                },
                error: function() {
                    showMessage('获取详情失败', 'error');
                }
            });
        }

        // 渲染详情模态框
        function renderDetailModal(data) {
            const rec = data.recommendation;
            const results = data.results;

            const statusText = rec.status === 'in_progress' ? '推荐中' : '已结束';
            const typeText = {
                'direct': '直接推荐',
                'internal': '内部推荐',
                'external': '外部推荐'
            }[rec.recommendation_type] || rec.recommendation_type;

            const reasonText = {
                'no_reprint': '不再重印',
                'new_book_not_published': '新书未出版',
                'no_supplier': '无供应商',
                'other': '其他'
            }[rec.replacement_reason] || rec.replacement_reason;

            let content = `
                <div class="space-y-4">
                    <!-- 基本信息 -->
                    <div class="bg-slate-50 rounded-lg p-4">
                        <h4 class="text-base font-semibold text-slate-800 mb-3">基本信息</h4>
                        <div class="grid grid-cols-3 gap-3 text-sm">
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">推荐状态</label>
                                <p class="text-slate-800">${statusText}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">推荐类型</label>
                                <p class="text-slate-800">${typeText}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">创建时间</label>
                                <p class="text-slate-800">${rec.created_at}</p>
                            </div>
                        </div>
                    </div>

                    <!-- 学校信息 -->
                    <div class="bg-slate-50 rounded-lg p-4">
                        <h4 class="text-base font-semibold text-slate-800 mb-3">学校信息</h4>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">学校名称</label>
                                <p class="text-slate-800">${rec.school_name}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">学校层次</label>
                                <p class="text-slate-800">${rec.school_level || '未分类'}</p>
                            </div>
                        </div>
                    </div>

                    <!-- 发起人信息 -->
                    <div class="bg-slate-50 rounded-lg p-4">
                        <h4 class="text-base font-semibold text-slate-800 mb-3">发起人信息</h4>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">发起公司</label>
                                <p class="text-slate-800">${rec.initiator_company_name}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">发起人</label>
                                <p class="text-slate-800">${rec.initiator_name}</p>
                            </div>
                            ${rec.referrer_name ? `
                            <div class="col-span-2">
                                <label class="block text-xs font-medium text-slate-600 mb-1">转荐人</label>
                                <p class="text-slate-800">${rec.referrer_name}</p>
                            </div>
                            ` : ''}
                        </div>
                    </div>

                    <!-- 原用教材信息 -->
                    <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                        <h4 class="text-base font-semibold text-slate-800 mb-3">原用教材信息</h4>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">教材名称</label>
                                <p class="text-slate-800 font-medium">${rec.original_book_name}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">ISBN</label>
                                <p class="text-slate-800 font-mono">${rec.original_book_isbn}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">作者</label>
                                <p class="text-slate-800">${rec.original_book_author}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">出版社</label>
                                <p class="text-slate-800">${rec.original_book_publisher}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">价格</label>
                                <p class="text-slate-800">¥${rec.original_book_price}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-slate-600 mb-1">出版日期</label>
                                <p class="text-slate-800">${rec.original_book_publication_date || '未知'}</p>
                            </div>
                        </div>
                    </div>

                    <!-- 换版原因 -->
                    <div class="bg-orange-50 rounded-lg p-4 border border-orange-200">
                        <h4 class="text-base font-semibold text-slate-800 mb-3">换版原因</h4>
                        <div class="space-y-2">
                            <p class="text-slate-800 font-medium">${reasonText}</p>
                            ${rec.replacement_reason_other ? `<p class="text-slate-600 text-sm">其他原因：${rec.replacement_reason_other}</p>` : ''}
                        </div>
                    </div>

                    <!-- 推荐要求 -->
                    <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                        <h4 class="text-base font-semibold text-slate-800 mb-3">推荐要求</h4>
                        <div class="space-y-3">
                            <!-- 基础要求：一行两个 -->
                            <div class="grid grid-cols-2 gap-x-6 gap-y-2">
                                ${rec.requirement_no_monopoly ? '<p class="text-sm text-slate-700"><i class="fas fa-check text-green-600 mr-2"></i>禁用包销书</p>' : ''}
                                ${rec.requirement_recent_publish ? '<p class="text-sm text-slate-700"><i class="fas fa-check text-green-600 mr-2"></i>近三年出版</p>' : ''}
                                ${rec.requirement_sufficient_stock ? '<p class="text-sm text-slate-700"><i class="fas fa-check text-green-600 mr-2"></i>库存充足</p>' : ''}
                                ${rec.requirement_national_priority ? '<p class="text-sm text-slate-700"><i class="fas fa-check text-green-600 mr-2"></i>国规优先</p>' : ''}
                            </div>

                            <!-- 其他要求：单独一行 -->
                            ${rec.requirement_other ? `
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-1">其他要求</label>
                                    <p class="text-slate-800 bg-white rounded p-2 border">${rec.requirement_other}</p>
                                </div>
                            ` : ''}

                            <!-- 无要求提示 -->
                            ${!rec.requirement_no_monopoly && !rec.requirement_recent_publish && !rec.requirement_sufficient_stock && !rec.requirement_national_priority && !rec.requirement_other ?
                                '<p class="text-sm text-slate-500">无特殊要求</p>' : ''}
                        </div>
                    </div>

                    <!-- 推荐书目列表 -->
                    <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                        <h4 class="text-base font-semibold text-slate-800 mb-3">推荐书目列表 (${results.length}本)</h4>
                        ${results.length > 0 ? `
                            <div class="space-y-3">
                                ${results.map((result, index) => `
                                    <div class="bg-white rounded-lg p-3 border border-slate-200">
                                        <div class="flex items-start justify-between mb-2">
                                            <div class="flex items-center gap-2">
                                                <span class="bg-slate-100 text-slate-700 text-xs font-medium px-2 py-1 rounded">#${index + 1}</span>
                                                <h5 class="font-medium text-slate-800 text-sm">${result.book_name}</h5>
                                            </div>
                                            <div class="text-right text-xs">
                                                <div class="text-slate-700 font-medium">${result.recommender_name}</div>
                                                <div class="text-slate-600">${result.recommender_company}</div>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-3 gap-2 text-xs">
                                            <div>
                                                <span class="text-slate-600">ISBN：</span>
                                                <span class="font-mono text-slate-800">${result.isbn}</span>
                                            </div>
                                            <div>
                                                <span class="text-slate-600">作者：</span>
                                                <span class="text-slate-800">${result.author}</span>
                                            </div>
                                            <div>
                                                <span class="text-slate-600">出版社：</span>
                                                <span class="text-slate-800">${result.publisher_name}</span>
                                            </div>
                                            <div>
                                                <span class="text-slate-600">价格：</span>
                                                <span class="text-slate-800 font-medium">¥${result.price}</span>
                                            </div>
                                            <div>
                                                <span class="text-slate-600">出版日期：</span>
                                                <span class="text-slate-800">${result.publication_date || '未知'}</span>
                                            </div>
                                            <div>
                                                <span class="text-slate-600">推荐时间：</span>
                                                <span class="text-slate-800">${result.created_at}</span>
                                            </div>
                                        </div>

                                        <!-- 冲突警告 -->
                                        ${result.is_monopoly_conflict ? `
                                            <div class="bg-red-100 border border-red-300 rounded-lg p-2 mt-2">
                                                <div class="flex items-center text-red-700">
                                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                                    <span class="font-medium text-xs">包销冲突</span>
                                                </div>
                                                <div class="text-xs text-red-600 mt-1">
                                                    该样书为包销书，与"禁用包销书"要求冲突
                                                </div>
                                            </div>
                                        ` : ''}
                                        ${result.is_publication_conflict ? `
                                            <div class="bg-orange-100 border border-orange-300 rounded-lg p-2 mt-2">
                                                <div class="flex items-center text-orange-700">
                                                    <i class="fas fa-clock mr-2"></i>
                                                    <span class="font-medium text-xs">出版时间冲突</span>
                                                </div>
                                                <div class="text-xs text-orange-600 mt-1">
                                                    该样书出版时间超过三年，与"近三年出版"要求冲突
                                                </div>
                                            </div>
                                        ` : ''}

                                        ${result.notes ? `
                                            <div class="mt-2 pt-2 border-t border-slate-200">
                                                <span class="text-slate-600 text-xs">推荐说明：</span>
                                                <p class="text-slate-800 text-xs mt-1">${result.notes}</p>
                                            </div>
                                        ` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        ` : `
                            <div class="text-center py-6 text-slate-500">
                                <i class="fas fa-inbox text-2xl mb-2 block"></i>
                                <p class="text-sm">暂无推荐书目</p>
                            </div>
                        `}
                    </div>

                    ${rec.notes ? `
                    <!-- 管理员备注 -->
                    <div class="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                        <h4 class="text-base font-semibold text-slate-800 mb-2">管理员备注</h4>
                        <p class="text-slate-700 text-sm">${rec.notes}</p>
                    </div>
                    ` : ''}
                </div>
            `;

            $('#detailContent').html(content);
        }

        // 关闭详情模态框
        function closeDetailModal() {
            $('#detailModal').addClass('hidden');
        }

        // 管理状态
        function manageStatus(recommendationId, currentStatus) {
            currentRecommendationId = recommendationId;
            $('#newStatus').val(currentStatus);
            $('#statusNotes').val('');
            $('#statusModal').removeClass('hidden');
        }

        // 关闭状态模态框
        function closeStatusModal() {
            $('#statusModal').addClass('hidden');
            currentRecommendationId = null;
        }

        // 确认状态更新
        function confirmStatusUpdate() {
            if (!currentRecommendationId) return;

            const newStatus = $('#newStatus').val();
            const notes = $('#statusNotes').val();

            $.ajax({
                url: '/api/admin/update_recommendation_status',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    recommendation_id: currentRecommendationId,
                    status: newStatus,
                    notes: notes
                }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('状态更新成功', 'success');
                        closeStatusModal();
                        loadRecommendations(currentPage);
                        loadStatistics();
                    } else {
                        showMessage(response.message || '状态更新失败', 'error');
                    }
                },
                error: function() {
                    showMessage('状态更新失败', 'error');
                }
            });
        }

        // 导出数据
        function exportData() {
            const params = new URLSearchParams(currentFilters);
            window.open(`/api/admin/export_recommendations?${params.toString()}`, '_blank');
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageContainer = $('#messageContainer');
            const messageId = 'message_' + Date.now();

            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'warning': 'bg-yellow-500',
                'info': 'bg-blue-500'
            }[type] || 'bg-blue-500';

            const icon = {
                'success': 'fa-check-circle',
                'error': 'fa-exclamation-circle',
                'warning': 'fa-exclamation-triangle',
                'info': 'fa-info-circle'
            }[type] || 'fa-info-circle';

            const messageHtml = `
                <div id="${messageId}" class="${bgColor} text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-3 message-slide-in">
                    <i class="fas ${icon}"></i>
                    <span>${message}</span>
                    <button onclick="$('#${messageId}').remove()" class="ml-auto text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            messageContainer.append(messageHtml);

            // 3秒后自动移除
            setTimeout(() => {
                $('#' + messageId).remove();
            }, 3000);
        }

        // CustomSelect组件
        class CustomSelect {
            constructor(containerId, options = {}) {
                this.container = document.getElementById(containerId);
                this.options = options;
                this.isOpen = false;
                this.selectedValue = '';
                this.selectedText = options.placeholder || '请选择';
                this.searchable = options.searchable || false;
                this.data = [];

                this.trigger = this.container.querySelector('.custom-select-trigger');
                this.dropdown = this.container.querySelector('.custom-select-dropdown');
                this.textSpan = this.container.querySelector('.custom-select-text');
                this.arrow = this.container.querySelector('.custom-select-arrow');
                this.optionsContainer = this.container.querySelector('.custom-select-options');
                this.searchInput = this.container.querySelector('.custom-select-search input');

                this.init();
            }

            init() {
                // 确保下拉框默认关闭
                this.close();

                // 绑定事件
                this.trigger.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggle();
                });

                if (this.searchInput) {
                    this.searchInput.addEventListener('input', (e) => {
                        this.filterOptions(e.target.value);
                    });
                }

                // 点击外部关闭
                document.addEventListener('click', (e) => {
                    if (!this.container.contains(e.target)) {
                        this.close();
                    }
                });

                // 绑定选项点击事件
                this.optionsContainer.addEventListener('click', (e) => {
                    const option = e.target.closest('.custom-select-option');
                    if (option) {
                        const value = option.dataset.value;
                        const text = option.textContent;
                        this.select(value, text);
                    }
                });
            }

            toggle() {
                if (this.isOpen) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                this.isOpen = true;
                this.container.classList.add('active');
                this.dropdown.classList.remove('hidden');

                if (this.searchInput) {
                    this.searchInput.focus();
                }
            }

            close() {
                this.isOpen = false;
                this.container.classList.remove('active');
                this.dropdown.classList.add('hidden');

                if (this.searchInput) {
                    this.searchInput.value = '';
                    this.filterOptions('');
                }
            }

            select(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.textSpan.textContent = text;

                // 更新选中状态
                this.optionsContainer.querySelectorAll('.custom-select-option').forEach(option => {
                    option.classList.remove('selected');
                    if (option.dataset.value === value) {
                        option.classList.add('selected');
                    }
                });

                this.close();

                // 触发回调
                if (this.options.onSelect) {
                    this.options.onSelect(value, text);
                }
            }

            setOptions(options) {
                this.data = options;
                this.renderOptions();
            }

            renderOptions() {
                this.optionsContainer.innerHTML = '';
                this.data.forEach(option => {
                    const optionElement = document.createElement('div');
                    optionElement.className = 'custom-select-option';
                    optionElement.dataset.value = option.value;
                    optionElement.textContent = option.text;

                    if (option.value === this.selectedValue) {
                        optionElement.classList.add('selected');
                    }

                    this.optionsContainer.appendChild(optionElement);
                });
            }

            filterOptions(searchTerm) {
                const options = this.optionsContainer.querySelectorAll('.custom-select-option');
                options.forEach(option => {
                    const text = option.textContent.toLowerCase();
                    const matches = text.includes(searchTerm.toLowerCase());
                    option.classList.toggle('hidden', !matches);
                });
            }

            reset() {
                this.selectedValue = '';
                this.selectedText = this.options.placeholder || '请选择';
                this.textSpan.textContent = this.selectedText;

                this.optionsContainer.querySelectorAll('.custom-select-option').forEach(option => {
                    option.classList.remove('selected');
                });

                this.close();
            }

            getValue() {
                return this.selectedValue;
            }

            getText() {
                return this.selectedText;
            }
        }
    </script>
</body>
</html>
