from flask import Flask

def create_app():
    app = Flask(__name__)

    app.secret_key = 'rQx3tWqvofSC2ihxW4Nt-Q'  # 设置 session 密钥

    # Register blueprints
    from .common.api import common_bp
    from .common.share import share_bp
    from .users.teacher import teacher_bp
    from .users.publisher import publisher_bp
    from .users.dealer import dealer_bp
    from .users.admin import admin_bp
    from .frontend.views import frontend_bp

    app.register_blueprint(common_bp, url_prefix='/api/common')
    app.register_blueprint(share_bp, url_prefix='/api/share')
    app.register_blueprint(teacher_bp, url_prefix='/api/teacher')
    app.register_blueprint(publisher_bp, url_prefix='/api/publisher')
    app.register_blueprint(dealer_bp, url_prefix='/api/dealer')
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    app.register_blueprint(frontend_bp, url_prefix='/')

    return app 