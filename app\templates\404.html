<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - 404</title>
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #333;
        }
        
        .error-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            max-width: 500px;
            width: 90%;
            backdrop-filter: blur(10px);
        }
        
        .error-code {
            font-size: 120px;
            font-weight: bold;
            color: #667eea;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .error-title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin: 20px 0 15px 0;
        }
        
        .error-message {
            font-size: 16px;
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .error-icon {
            font-size: 80px;
            color: #667eea;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        

        
        .footer-text {
            margin-top: 30px;
            font-size: 14px;
            color: #999;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .error-icon {
            animation: float 3s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h1 class="error-code">404</h1>
        
        <h2 class="error-title">页面未找到</h2>
        
        <p class="error-message">
            抱歉，您访问的登录入口不存在或已被禁用。<br>
            请检查URL是否正确，或联系管理员获取正确的登录地址。
        </p>

        <div class="footer-text">
            如果问题持续存在，请联系系统管理员
        </div>
    </div>
</body>
</html>
